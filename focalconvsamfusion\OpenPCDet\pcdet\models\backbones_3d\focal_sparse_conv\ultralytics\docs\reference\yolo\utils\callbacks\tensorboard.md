---
description: Learn how to monitor the training process with Tensorboard using Ultralytics YOLO's "_log_scalars" and "on_batch_end" methods.
keywords: TensorBoard callbacks, YOLO training, ultralytics YOLO
---

## _log_scalars
---
### ::: ultralytics.yolo.utils.callbacks.tensorboard._log_scalars
<br><br>

## on_pretrain_routine_start
---
### ::: ultralytics.yolo.utils.callbacks.tensorboard.on_pretrain_routine_start
<br><br>

## on_batch_end
---
### ::: ultralytics.yolo.utils.callbacks.tensorboard.on_batch_end
<br><br>

## on_fit_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.tensorboard.on_fit_epoch_end
<br><br>
