---
description: Maximize YOLO performance with Ultralytics' InfiniteData<PERSON>oader, seed_worker, build_dataloader, and load_inference_source functions.
keywords: Ultralytics, YOLO, object detection, data loading, build dataloader, load inference source
---

## InfiniteDataLoader
---
### ::: ultralytics.yolo.data.build.InfiniteDataLoader
<br><br>

## _RepeatSampler
---
### ::: ultralytics.yolo.data.build._RepeatSampler
<br><br>

## seed_worker
---
### ::: ultralytics.yolo.data.build.seed_worker
<br><br>

## build_yolo_dataset
---
### ::: ultralytics.yolo.data.build.build_yolo_dataset
<br><br>

## build_dataloader
---
### ::: ultralytics.yolo.data.build.build_dataloader
<br><br>

## check_source
---
### ::: ultralytics.yolo.data.build.check_source
<br><br>

## load_inference_source
---
### ::: ultralytics.yolo.data.build.load_inference_source
<br><br>
