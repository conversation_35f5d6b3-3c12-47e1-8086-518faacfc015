---
description: Explore Ultralytics events, including 'request_with_credentials' and 'smart_request', to improve your project's performance and efficiency.
keywords: Ultralytics, Hub Utils, API Documentation, Python, requests_with_progress, Events, classes, usage, examples
---

## Events
---
### ::: ultralytics.hub.utils.Events
<br><br>

## request_with_credentials
---
### ::: ultralytics.hub.utils.request_with_credentials
<br><br>

## requests_with_progress
---
### ::: ultralytics.hub.utils.requests_with_progress
<br><br>

## smart_request
---
### ::: ultralytics.hub.utils.smart_request
<br><br>
