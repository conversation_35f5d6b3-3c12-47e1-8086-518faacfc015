#!/usr/bin/env python3
"""
调试KITTI-C数据集结构的脚本
检查文件路径和结构是否匹配
"""

import _init_path
import argparse
import pickle
from pathlib import Path

from pcdet.config import cfg, cfg_from_yaml_file


def parse_args():
    parser = argparse.ArgumentParser(description='Debug KITTI-C structure')
    parser.add_argument('--corruption', type=str, default='sun',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_original.yaml',
                       help='config file')
    
    return parser.parse_args()


def check_data_structure(data_path, corruption):
    """检查KITTI-C数据结构"""
    
    data_path = Path(data_path)
    print(f"🔍 检查KITTI-C数据结构")
    print(f"📂 数据路径: {data_path}")
    print(f"📂 腐败类型: {corruption}")
    
    # 检查主要目录
    print(f"\n📋 主要目录结构:")
    if data_path.exists():
        print(f"  ✅ 数据根目录存在: {data_path}")
        
        # 列出主要子目录
        subdirs = [d for d in data_path.iterdir() if d.is_dir()]
        print(f"  📁 子目录:")
        for subdir in sorted(subdirs):
            print(f"    - {subdir.name}")
    else:
        print(f"  ❌ 数据根目录不存在: {data_path}")
        return
    
    # 检查training目录结构
    training_dir = data_path / 'training'
    if training_dir.exists():
        print(f"\n📋 training目录结构:")
        training_subdirs = [d for d in training_dir.iterdir() if d.is_dir()]
        for subdir in sorted(training_subdirs):
            file_count = len(list(subdir.glob('*')))
            print(f"    - {subdir.name}: {file_count} 个文件")
    else:
        print(f"\n❌ training目录不存在: {training_dir}")
    
    # 检查testing目录结构
    testing_dir = data_path / 'testing'
    if testing_dir.exists():
        print(f"\n📋 testing目录结构:")
        testing_subdirs = [d for d in testing_dir.iterdir() if d.is_dir()]
        for subdir in sorted(testing_subdirs):
            file_count = len(list(subdir.glob('*')))
            print(f"    - {subdir.name}: {file_count} 个文件")
    else:
        print(f"\n❌ testing目录不存在: {testing_dir}")


def check_info_file(data_path, corruption):
    """检查info文件内容"""
    
    info_file_path = Path(data_path) / f'kitti_c_infos_val_{corruption}.pkl'
    
    print(f"\n🔍 检查info文件:")
    print(f"📂 Info文件路径: {info_file_path}")
    
    if not info_file_path.exists():
        print(f"❌ Info文件不存在")
        return
    
    print(f"✅ Info文件存在")
    
    # 加载info文件
    try:
        with open(info_file_path, 'rb') as f:
            infos = pickle.load(f)
        
        print(f"📊 Info文件统计:")
        print(f"  - 样本数量: {len(infos)}")
        
        if len(infos) > 0:
            first_info = infos[0]
            print(f"  - 第一个样本字段: {list(first_info.keys())}")
            
            # 检查点云信息
            if 'point_cloud' in first_info:
                pc_info = first_info['point_cloud']
                print(f"  - 点云信息: {pc_info}")
                
                if 'lidar_idx' in pc_info:
                    sample_idx = pc_info['lidar_idx']
                    print(f"  - 第一个样本ID: {sample_idx}")
                    
                    # 检查对应的文件是否存在
                    data_root = Path(data_path)
                    
                    # 检查不同可能的文件路径
                    possible_paths = [
                        # 原始KITTI结构
                        data_root / 'training' / 'calib' / f'{sample_idx}.txt',
                        data_root / 'testing' / 'calib' / f'{sample_idx}.txt',
                        # KITTI-C可能的结构
                        data_root / 'calib' / f'{sample_idx}.txt',
                        data_root / f'calib_{corruption}' / f'{sample_idx}.txt',
                        # 点云文件
                        data_root / 'training' / 'velodyne' / f'{sample_idx}.bin',
                        data_root / 'testing' / 'velodyne' / f'{sample_idx}.bin',
                        data_root / 'training' / 'velodyne' / f'{sample_idx}.npy',
                        data_root / 'testing' / 'velodyne' / f'{sample_idx}.npy',
                        data_root / 'velodyne' / f'{sample_idx}_{corruption}_d1.npy',
                        # 图像文件
                        data_root / 'training' / 'image_2' / f'{sample_idx}.png',
                        data_root / 'testing' / 'image_2' / f'{sample_idx}.png',
                        data_root / 'images' / f'{sample_idx}_{corruption}_d1.png',
                    ]
                    
                    print(f"\n📋 文件存在性检查 (样本ID: {sample_idx}):")
                    for path in possible_paths:
                        exists = "✅" if path.exists() else "❌"
                        print(f"  {exists} {path}")
            
            # 检查标注信息
            if 'annos' in first_info:
                annos = first_info['annos']
                print(f"  - 标注字段: {list(annos.keys())}")
                if 'name' in annos:
                    print(f"  - 标注对象数量: {len(annos['name'])}")
                    print(f"  - 对象类别: {list(set(annos['name']))}")
            else:
                print(f"  ⚠️ 第一个样本没有标注信息")
    
    except Exception as e:
        print(f"❌ 加载info文件失败: {e}")


def main():
    args = parse_args()
    
    # 加载配置
    cfg_from_yaml_file(args.cfg_file, cfg)
    
    data_path = cfg.DATA_CONFIG.DATA_PATH
    
    # 检查数据结构
    check_data_structure(data_path, args.corruption)
    
    # 检查info文件
    check_info_file(data_path, args.corruption)
    
    print(f"\n💡 调试完成！")
    print(f"📋 根据上述信息，可以确定:")
    print(f"  1. 文件路径结构是否正确")
    print(f"  2. Info文件中的sample_idx格式")
    print(f"  3. 实际文件的命名规则")


if __name__ == '__main__':
    main()
