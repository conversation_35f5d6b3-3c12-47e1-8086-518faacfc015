import copy
import pickle
import numpy as np
from pathlib import Path

from . import kitti_utils
from .kitti_dataset import KittiDataset
from ...utils import common_utils, box_utils


class KittiCAdaptedDataset(KittiDataset):
    """
    适配KITTI-C文件结构的KittiDataset子类
    继承KittiDataset的正确evaluation方法，但适配KITTI-C的文件路径
    """
    
    def __init__(self, dataset_cfg, class_names, training=True, root_path=None, logger=None):
        # 调用DatasetTemplate的初始化，跳过KittiDataset的初始化
        from ..dataset import DatasetTemplate
        DatasetTemplate.__init__(
            self, dataset_cfg=dataset_cfg, class_names=class_names, training=training, root_path=root_path, logger=logger
        )

        # 获取腐败类型
        self.corruption_type = getattr(dataset_cfg, 'DEFAULT_CORRUPTION', 'fog')

        # KITTI-C的split映射
        self.split = self.dataset_cfg.DATA_SPLIT[self.mode]

        # KITTI-C没有training/testing分离，都在腐败类型目录下
        self.root_split_path = self.root_path / self.corruption_type

        # 不需要ImageSets文件，直接从info文件获取样本列表
        self.sample_id_list = None

        self.kitti_infos = []
        self.include_kitti_data(self.mode)

    def include_kitti_data(self, mode):
        """加载KITTI-C info数据"""
        if self.logger is not None:
            self.logger.info('Loading KITTI-C dataset')
        
        kitti_infos = []
        
        for info_path in self.dataset_cfg.INFO_PATH[mode]:
            info_path = self.root_path / info_path
            if not info_path.exists():
                continue
            with open(info_path, 'rb') as f:
                infos = pickle.load(f)
                kitti_infos.extend(infos)

        self.kitti_infos.extend(kitti_infos)

        if self.logger is not None:
            self.logger.info('Total samples for KITTI-C dataset: %d' % (len(kitti_infos)))

    def get_lidar(self, idx):
        """
        根据info文件中的velodyne_path加载点云数据
        """
        # 从info中查找对应的点云路径
        for info in self.kitti_infos:
            if info['point_cloud']['lidar_idx'] == idx:
                velodyne_path = info['point_cloud'].get('velodyne_path', None)
                if velodyne_path:
                    # velodyne_path是相对于root_path的路径，如 'sun/velodyne/val/000000_sun_d2.npy'
                    lidar_file = self.root_path / velodyne_path
                    if lidar_file.exists():
                        if lidar_file.suffix == '.npy':
                            return np.load(str(lidar_file)).astype(np.float32)
                        else:
                            return np.fromfile(str(lidar_file), dtype=np.float32).reshape(-1, 4)
        
        # 如果info中没有找到，尝试默认路径
        # 尝试.npy格式（KITTI-C格式），5种深度级别
        possible_lidar_paths = []

        # KITTI-C格式，5种深度级别 (d1, d2, d3, d4, d5)
        for depth in ['d1', 'd2', 'd3', 'd4', 'd5']:
            possible_lidar_paths.extend([
                self.root_split_path / 'velodyne' / 'val' / f'{idx}_{self.corruption_type}_{depth}.npy',
                self.root_split_path / 'velodyne' / f'{idx}_{self.corruption_type}_{depth}.npy',
            ])

        # 原始KITTI格式
        possible_lidar_paths.extend([
            self.root_split_path / 'velodyne' / f'{idx}.bin',
            self.root_path / 'training' / 'velodyne' / f'{idx}.bin',
            self.root_path / 'testing' / 'velodyne' / f'{idx}.bin',
        ])

        for lidar_file in possible_lidar_paths:
            if lidar_file.exists():
                if lidar_file.suffix == '.npy':
                    return np.load(str(lidar_file)).astype(np.float32)
                else:
                    return np.fromfile(str(lidar_file), dtype=np.float32).reshape(-1, 4)

        raise FileNotFoundError(f"Cannot find lidar file for sample {idx}")

    def get_image(self, idx):
        """
        加载图像数据，适配KITTI-C的图像路径
        """
        # 尝试不同的图像路径和命名格式（5种深度级别）
        possible_paths = []

        # KITTI-C格式，5种深度级别 (d1, d2, d3, d4, d5)
        for depth in ['d1', 'd2', 'd3', 'd4', 'd5']:
            possible_paths.extend([
                self.root_split_path / 'images' / 'val' / f'{idx}_{self.corruption_type}_{depth}.png',
                self.root_split_path / 'images' / f'{idx}_{self.corruption_type}_{depth}.png',
            ])

        # 原始KITTI格式
        possible_paths.extend([
            self.root_split_path / 'image_2' / f'{idx}.png',
            self.root_path / 'training' / 'image_2' / f'{idx}.png',
            self.root_path / 'testing' / 'image_2' / f'{idx}.png',
            self.root_path / 'images' / f'{idx}.png',
        ])

        for img_file in possible_paths:
            if img_file.exists():
                # 加载图像数据而不是返回路径
                from PIL import Image
                import numpy as np
                with Image.open(img_file) as img:
                    return np.array(img)

        # 如果找不到图像，创建一个默认图像（KITTI标准尺寸）
        print(f"⚠️ 警告：找不到样本 {idx} 的图像文件，使用默认图像")
        import numpy as np
        # 创建一个默认的黑色图像 (375, 1242, 3)
        default_image = np.zeros((375, 1242, 3), dtype=np.uint8)
        return default_image

    def get_image_shape(self, idx):
        """获取图像尺寸"""
        try:
            img_array = self.get_image(idx)
            # img_array的形状是 (height, width, channels)
            return np.array([img_array.shape[0], img_array.shape[1]], dtype=np.int32)  # (height, width)
        except Exception:
            # 返回KITTI默认尺寸
            return np.array([375, 1242], dtype=np.int32)

    def get_calib(self, idx):
        """
        加载标定文件，适配KITTI-C的标定文件路径
        """
        # KITTI-C的标定文件在 corruption_type/calib/ 目录下
        calib_file = self.root_split_path / 'calib' / f'{idx}.txt'
        
        if not calib_file.exists():
            # 如果找不到，尝试其他可能的路径
            alternative_paths = [
                self.root_path / 'calib' / f'{idx}.txt',
                self.root_split_path / 'calib' / 'val' / f'{idx}.txt',
            ]
            
            for alt_path in alternative_paths:
                if alt_path.exists():
                    calib_file = alt_path
                    break
            else:
                raise FileNotFoundError(f"Cannot find calib file for sample {idx}: {calib_file}")
        
        from ...utils import calibration_kitti
        return calibration_kitti.Calibration(calib_file)

    def get_sparsedepth(self, idx):
        """
        获取稀疏深度图（如果需要）
        """
        # KITTI-C可能没有深度图，返回None或创建虚拟深度图
        return None

    def set_corruption_type(self, corruption_type):
        """设置腐败类型"""
        self.corruption_type = corruption_type
        self.root_split_path = self.root_path / self.corruption_type
        
        # 重新加载对应腐败类型的info文件
        self.kitti_infos = []
        
        # 更新info文件路径
        info_file = f'kitti_c_infos_val_{corruption_type}.pkl'
        info_path = self.root_path / info_file
        
        if info_path.exists():
            with open(info_path, 'rb') as f:
                infos = pickle.load(f)
                self.kitti_infos.extend(infos)
        
        if self.logger is not None:
            self.logger.info(f'Switched to corruption type: {corruption_type}, samples: {len(self.kitti_infos)}')

    def get_fov_flag(self, pts_rect, img_shape, calib):
        """
        获取视野内的点云标志
        """
        pts_img, pts_rect_depth = calib.rect_to_img(pts_rect)
        val_flag_1 = np.logical_and(pts_img[:, 0] >= 0, pts_img[:, 0] < img_shape[1])
        val_flag_2 = np.logical_and(pts_img[:, 1] >= 0, pts_img[:, 1] < img_shape[0])
        val_flag_merge = np.logical_and(val_flag_1, val_flag_2)
        pts_valid_flag = np.logical_and(val_flag_merge, pts_rect_depth >= 0)
        return pts_valid_flag

    def evaluation(self, det_annos, class_names, **kwargs):
        """
        使用标准KITTI评估方法
        这是从原始KittiDataset继承的关键方法
        """
        if 'annos' not in self.kitti_infos[0].keys():
            return None, {}

        from .kitti_object_eval_python import eval as kitti_eval

        eval_det_annos = copy.deepcopy(det_annos)
        eval_gt_annos = [copy.deepcopy(info['annos']) for info in self.kitti_infos]
        ap_result_str, ap_dict = kitti_eval.get_official_eval_result(eval_gt_annos, eval_det_annos, class_names)

        return ap_result_str, ap_dict

    def __len__(self):
        if self._merge_all_iters_to_one_epoch:
            return len(self.kitti_infos) * self.total_epochs

        return len(self.kitti_infos)

    def __getitem__(self, index):
        """
        重写__getitem__方法以适配KITTI-C的数据加载
        """
        if self._merge_all_iters_to_one_epoch:
            index = index % len(self.kitti_infos)

        info = copy.deepcopy(self.kitti_infos[index])
        sample_idx = info['point_cloud']['lidar_idx']
        img_shape = info['image']['image_shape']
        calib = self.get_calib(sample_idx)
        get_item_list = self.dataset_cfg.get('GET_ITEM_LIST', ['points'])

        input_dict = {
            'frame_id': sample_idx,
            'calib': calib,
            'image_shape': img_shape,
        }

        if 'annos' in info:
            annos = info['annos']
            annos = common_utils.drop_info_with_name(annos, name='DontCare')
            loc, dims, rots = annos['location'], annos['dimensions'], annos['rotation_y']
            gt_names = annos['name']

            # 检查是否有有效的标注
            if len(gt_names) > 0:
                # 过滤异常值（-1000等无效坐标）
                valid_mask = (
                    (np.array(loc)[:, 0] > -100) & (np.array(loc)[:, 0] < 100) &
                    (np.array(loc)[:, 1] > -100) & (np.array(loc)[:, 1] < 100) &
                    (np.array(loc)[:, 2] > -10) & (np.array(loc)[:, 2] < 200) &
                    (np.array(dims)[:, 0] > 0) & (np.array(dims)[:, 1] > 0) & (np.array(dims)[:, 2] > 0)
                )

                if valid_mask.sum() > 0:
                    # 只保留有效的标注
                    loc = np.array(loc)[valid_mask]
                    dims = np.array(dims)[valid_mask]
                    rots = np.array(rots)[valid_mask]
                    gt_names = np.array(gt_names)[valid_mask]

                    # KITTI-C info文件中是相机坐标系，需要转换为激光雷达坐标系
                    gt_boxes_camera = np.concatenate([loc, dims, rots[..., np.newaxis]], axis=1).astype(np.float32)
                    gt_boxes_lidar = box_utils.boxes3d_kitti_camera_to_lidar(gt_boxes_camera, calib)

                    input_dict.update({
                        'gt_names': gt_names,
                        'gt_boxes': gt_boxes_lidar
                    })
                else:
                    # 没有有效标注
                    input_dict.update({
                        'gt_names': np.array([]),
                        'gt_boxes': np.zeros((0, 7), dtype=np.float32)
                    })
            else:
                # 没有标注
                input_dict.update({
                    'gt_names': np.array([]),
                    'gt_boxes': np.zeros((0, 7), dtype=np.float32)
                })
            if "gt_boxes2d" in get_item_list:
                input_dict['gt_boxes2d'] = annos["bbox"]

        if "points" in get_item_list:
            points = self.get_lidar(sample_idx)
            if self.dataset_cfg.FOV_POINTS_ONLY:
                pts_rect = calib.lidar_to_rect(points[:, 0:3])
                fov_flag = self.get_fov_flag(pts_rect, img_shape, calib)
                points = points[fov_flag]
            input_dict['points'] = points

        if "images" in get_item_list:
            input_dict['images'] = self.get_image(sample_idx)

        if "calib_matricies" in get_item_list:
            input_dict["trans_lidar_to_cam"], input_dict["trans_cam_to_img"] = kitti_utils.calib_to_matricies(calib)

        if "sparsedepth" in get_item_list:
            sparse_depth = self.get_sparsedepth(sample_idx)
            if sparse_depth is not None:
                input_dict['sparsedepth'] = sparse_depth

        # 确定是否使用深度信息
        usedepth = "sparsedepth" in get_item_list or "depth_maps" in get_item_list

        data_dict = self.prepare_data(data_dict=input_dict, usedepth=usedepth)
        data_dict['image_shape'] = img_shape
        return data_dict
