#!/usr/bin/env python3
"""
KITTI-C鲁棒性测试脚本
使用更稳定的方式进行多GPU测试，避免分布式训练问题
"""

import os
import sys
import subprocess
import time
import signal
from pathlib import Path


def run_single_gpu_test(gpu_id, cfg_file, ckpt_path, extra_tag):
    """在单个GPU上运行测试"""
    
    print(f"\n=== 在GPU {gpu_id}上运行测试 ===")
    
    # 设置环境变量
    env = os.environ.copy()
    env['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
    
    # 构建命令
    cmd = [
        'python', 'test_kitti_c_memory_optimized.py',
        '--cfg_file', cfg_file,
        '--ckpt', ckpt_path,
        '--extra_tag', f'{extra_tag}_gpu_{gpu_id}',
        '--save_to_file',
        '--infer_time'
    ]
    
    print(f"运行命令: {' '.join(cmd)}")
    print(f"GPU: {gpu_id}")
    
    try:
        # 运行测试，设置超时时间
        result = subprocess.run(
            cmd, 
            env=env,
            capture_output=True, 
            text=True, 
            timeout=3600,  # 1小时超时
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print(f"✓ GPU {gpu_id} 测试成功完成")
            return True, result.stdout
        else:
            print(f"✗ GPU {gpu_id} 测试失败")
            print(f"错误输出: {result.stderr}")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"✗ GPU {gpu_id} 测试超时")
        return False, "Timeout"
    except Exception as e:
        print(f"✗ GPU {gpu_id} 测试出错: {e}")
        return False, str(e)


def run_sequential_multi_gpu_test(gpu_list, cfg_file, ckpt_path, extra_tag):
    """顺序在多个GPU上运行测试（避免分布式问题）"""
    
    print("=== 顺序多GPU测试模式 ===")
    print(f"使用GPU: {gpu_list}")
    
    results = {}
    
    for gpu_id in gpu_list:
        success, output = run_single_gpu_test(gpu_id, cfg_file, ckpt_path, extra_tag)
        results[gpu_id] = {
            'success': success,
            'output': output
        }
        
        # 每个GPU测试完后等待一下，让GPU内存完全释放
        time.sleep(5)
    
    return results


def run_parallel_safe_test(gpu_list, cfg_file, ckpt_path, extra_tag):
    """安全的并行测试（使用进程池）"""
    
    print("=== 安全并行测试模式 ===")
    print(f"使用GPU: {gpu_list}")
    
    from concurrent.futures import ProcessPoolExecutor, as_completed
    import multiprocessing
    
    # 限制并行数量，避免资源竞争
    max_workers = min(len(gpu_list), 2)  # 最多同时运行2个进程
    
    results = {}
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交任务
        future_to_gpu = {
            executor.submit(run_single_gpu_test, gpu_id, cfg_file, ckpt_path, extra_tag): gpu_id 
            for gpu_id in gpu_list
        }
        
        # 收集结果
        for future in as_completed(future_to_gpu):
            gpu_id = future_to_gpu[future]
            try:
                success, output = future.result(timeout=3600)  # 1小时超时
                results[gpu_id] = {
                    'success': success,
                    'output': output
                }
                print(f"GPU {gpu_id} 完成")
            except Exception as e:
                print(f"GPU {gpu_id} 异常: {e}")
                results[gpu_id] = {
                    'success': False,
                    'output': str(e)
                }
    
    return results


def main():
    """主函数"""
    
    print("=== KITTI-C鲁棒性测试工具 (改进版) ===")
    
    # 配置参数
    cfg_file = "cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml"
    ckpt_path = "../output/kitti_models/sam_onlinev3/robofusion_tmux/ckpt/checkpoint_epoch_80.pth"
    extra_tag = "kitti_c_robustness_safe"
    
    # GPU列表
    gpu_list = [4, 5, 6, 7]  # 根据你的CUDA_VISIBLE_DEVICES设置
    
    print(f"配置文件: {cfg_file}")
    print(f"权重文件: {ckpt_path}")
    print(f"GPU列表: {gpu_list}")
    
    # 检查文件是否存在
    if not Path(cfg_file).exists():
        print(f"错误: 配置文件不存在: {cfg_file}")
        return
    
    # 选择测试模式
    print("\n请选择测试模式:")
    print("1. 单GPU测试 (推荐)")
    print("2. 顺序多GPU测试")
    print("3. 安全并行测试")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == '1':
        # 单GPU测试
        gpu_id = gpu_list[0]  # 使用第一个GPU
        print(f"\n使用单GPU模式，GPU: {gpu_id}")
        success, output = run_single_gpu_test(gpu_id, cfg_file, ckpt_path, extra_tag)
        
        if success:
            print("\n✓ 测试成功完成!")
        else:
            print(f"\n✗ 测试失败: {output}")
            
    elif choice == '2':
        # 顺序多GPU测试
        results = run_sequential_multi_gpu_test(gpu_list, cfg_file, ckpt_path, extra_tag)
        
        # 汇总结果
        print("\n=== 测试结果汇总 ===")
        success_count = 0
        for gpu_id, result in results.items():
            status = "✓ 成功" if result['success'] else "✗ 失败"
            print(f"GPU {gpu_id}: {status}")
            if result['success']:
                success_count += 1
        
        print(f"\n总计: {success_count}/{len(gpu_list)} 个GPU测试成功")
        
    elif choice == '3':
        # 安全并行测试
        results = run_parallel_safe_test(gpu_list, cfg_file, ckpt_path, extra_tag)
        
        # 汇总结果
        print("\n=== 测试结果汇总 ===")
        success_count = 0
        for gpu_id, result in results.items():
            status = "✓ 成功" if result['success'] else "✗ 失败"
            print(f"GPU {gpu_id}: {status}")
            if result['success']:
                success_count += 1
        
        print(f"\n总计: {success_count}/{len(gpu_list)} 个GPU测试成功")
        
    else:
        print("无效选择，退出")


if __name__ == '__main__':
    main()
