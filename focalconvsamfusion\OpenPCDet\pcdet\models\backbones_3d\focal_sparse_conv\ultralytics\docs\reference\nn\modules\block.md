---
description: Explore ultralytics.nn.modules.block to build powerful YOLO object detection models. Master DFL, HGStem, SPP, CSP components and more.
keywords: Ultralytics, NN Modules, Blocks, DFL, HGStem, SPP, C1, C2f, C3x, C3TR, GhostB<PERSON>leneck, BottleneckCSP, Computer Vision
---

## DFL
---
### ::: ultralytics.nn.modules.block.DFL
<br><br>

## Proto
---
### ::: ultralytics.nn.modules.block.Proto
<br><br>

## HGStem
---
### ::: ultralytics.nn.modules.block.HGStem
<br><br>

## HGBlock
---
### ::: ultralytics.nn.modules.block.HGBlock
<br><br>

## SPP
---
### ::: ultralytics.nn.modules.block.SPP
<br><br>

## SPPF
---
### ::: ultralytics.nn.modules.block.SPPF
<br><br>

## C1
---
### ::: ultralytics.nn.modules.block.C1
<br><br>

## C2
---
### ::: ultralytics.nn.modules.block.C2
<br><br>

## C2f
---
### ::: ultralytics.nn.modules.block.C2f
<br><br>

## C3
---
### ::: ultralytics.nn.modules.block.C3
<br><br>

## C3x
---
### ::: ultralytics.nn.modules.block.C3x
<br><br>

## RepC3
---
### ::: ultralytics.nn.modules.block.RepC3
<br><br>

## C3TR
---
### ::: ultralytics.nn.modules.block.C3TR
<br><br>

## C3Ghost
---
### ::: ultralytics.nn.modules.block.C3Ghost
<br><br>

## GhostBottleneck
---
### ::: ultralytics.nn.modules.block.GhostBottleneck
<br><br>

## Bottleneck
---
### ::: ultralytics.nn.modules.block.Bottleneck
<br><br>

## BottleneckCSP
---
### ::: ultralytics.nn.modules.block.BottleneckCSP
<br><br>
