import copy
import pickle
import numpy as np
from pathlib import Path
from skimage import io

from ...ops.roiaware_pool3d import roiaware_pool3d_utils
from ...utils import box_utils, calibration_kitti, common_utils, object3d_kitti
from ..dataset import DatasetTemplate


class KittiCDataset(DatasetTemplate):
    """
    KITTI-C (KITTI Corruptions) Dataset
    支持fog, rain, snow, sun等腐败类型
    """
    def __init__(self, dataset_cfg, class_names, training=True, root_path=None, logger=None,
                 corruption_type='fog', corruptions=[None, None], severity=0, save_cor_flag=False):
        """
        Args:
            root_path: KITTI-C数据集路径
            dataset_cfg: 数据集配置
            class_names: 类别名称
            training: 是否为训练模式
            corruption_type: 腐败类型 ('fog', 'rain', 'snow', 'sun')
            corruptions: 兼容原有接口的腐败参数
            severity: 腐败严重程度
            save_cor_flag: 是否保存腐败数据标志
        """
        super().__init__(
            dataset_cfg=dataset_cfg, class_names=class_names, training=training, root_path=root_path, logger=logger
        )

        # 处理腐败参数，兼容原有接口
        self.corruptions = corruptions
        self.severity = severity
        self.save_cor_flag = save_cor_flag

        # 如果corruptions参数指定了腐败类型，使用它；否则使用默认的corruption_type
        if corruptions[0] is not None and corruptions[0] in ['fog', 'rain', 'snow', 'sun']:
            self.corruption_type = corruptions[0]
        else:
            self.corruption_type = corruption_type

        self.corruption_types = dataset_cfg.get('CORRUPTION_TYPES', ['fog', 'rain', 'snow', 'sun'])
        
        # 设置数据路径
        self.split = self.dataset_cfg.DATA_SPLIT['train'] if training else self.dataset_cfg.DATA_SPLIT['test']
        self.root_split_path = self.root_path / self.corruption_type

        # 获取样本ID列表
        split_dir = self.root_path / 'ImageSets' / (self.split + '.txt')
        if split_dir.exists():
            self.sample_id_list = [x.strip() for x in open(split_dir).readlines()]
        else:
            # 如果没有ImageSets，从所有腐败类型目录中收集样本
            self.sample_id_list = []

            for corruption_type in self.corruption_types:
                corruption_path = self.root_path / corruption_type

                # 尝试val子目录
                image_dir = corruption_path / 'images' / 'val'
                if not image_dir.exists():
                    # 尝试直接在images目录下
                    image_dir = corruption_path / 'images'

                if image_dir.exists():
                    corruption_samples = [f.stem for f in image_dir.glob('*.png')]
                    self.sample_id_list.extend(corruption_samples)
                    if self.logger is not None:
                        self.logger.info(f'Found {len(corruption_samples)} samples in {corruption_type}: {image_dir}')

            self.sample_id_list.sort()

            if not self.sample_id_list:
                if self.logger is not None:
                    self.logger.error(f"No samples found in any corruption type directories")
                    self.logger.error(f"Available directories in {self.root_path}: {list(self.root_path.iterdir())}")
                raise FileNotFoundError(f"No samples found in KITTI-C dataset")

            if self.logger is not None:
                self.logger.info(f'Total samples found across all corruption types: {len(self.sample_id_list)}')

        self.kitti_infos = []
        self.include_kitti_data(self.mode)

    def _extract_corruption_from_filename(self, filename):
        """从文件名中提取腐败类型"""
        # 例如：006014_sun_d3 -> sun
        filename_str = str(filename)
        for corruption_type in self.corruption_types:
            if f'_{corruption_type}_' in filename_str:
                return corruption_type
        return None

    def include_kitti_data(self, mode):
        if self.logger is not None:
            self.logger.info('Loading KITTI-C dataset')
        kitti_infos = []

        for info_path in self.dataset_cfg.INFO_PATH[mode]:
            info_path = self.root_path / info_path
            if not info_path.exists():
                # 如果info文件不存在，创建基本的info结构
                if self.logger is not None:
                    self.logger.warning(f'Info file {info_path} not found, creating basic info structure')
                kitti_infos.extend(self.create_basic_infos())
                continue
            with open(info_path, 'rb') as f:
                infos = pickle.load(f)
                kitti_infos.extend(infos)

        self.kitti_infos.extend(kitti_infos)

        if self.logger is not None:
            self.logger.info('Total samples for KITTI-C dataset: %d' % (len(kitti_infos)))

    def create_basic_infos(self):
        """创建基本的info结构，当pkl文件不存在时使用"""
        infos = []
        for sample_id in self.sample_id_list:
            info = {
                'point_cloud': {'num_features': 4, 'lidar_idx': sample_id},
                'image': {'image_idx': sample_id, 'image_shape': [375, 1242]},  # KITTI标准尺寸
                'calib': {},
                'annos': {
                    'name': np.array([]),
                    'truncated': np.array([]),
                    'occluded': np.array([]),
                    'alpha': np.array([]),
                    'bbox': np.zeros([0, 4]),
                    'dimensions': np.zeros([0, 3]),
                    'location': np.zeros([0, 3]),
                    'rotation_y': np.array([]),
                    'score': np.array([]),
                    'difficulty': np.array([]),
                    'num_points_in_gt': np.array([])
                }
            }
            infos.append(info)
        return infos

    def get_lidar(self, idx):
        """加载点云数据 (.npy格式)"""
        # 从文件名中提取腐败类型，例如：006014_sun_d3 -> sun
        corruption_from_filename = self._extract_corruption_from_filename(idx)

        # 如果文件名包含腐败类型，使用对应的目录
        if corruption_from_filename:
            target_corruption_path = self.root_path / corruption_from_filename
        else:
            # 否则使用当前设置的腐败类型目录
            target_corruption_path = self.root_split_path

        # 首先尝试标准格式 (000000.npy)
        lidar_file = target_corruption_path / 'velodyne' / 'val' / ('%s.npy' % idx)
        if not lidar_file.exists():
            # 尝试直接在velodyne目录下
            lidar_file = target_corruption_path / 'velodyne' / ('%s.npy' % idx)

        # 如果标准格式不存在，尝试KITTI-C格式 (000000_corruption_d1.npy)
        if not lidar_file.exists():
            # 尝试在val子目录中查找匹配的文件
            velodyne_val_dir = target_corruption_path / 'velodyne' / 'val'
            if velodyne_val_dir.exists():
                matching_files = list(velodyne_val_dir.glob(f'{idx}_*.npy'))
                if matching_files:
                    lidar_file = matching_files[0]

        # 如果还是找不到，尝试直接在velodyne目录下查找
        if not lidar_file.exists():
            velodyne_dir = target_corruption_path / 'velodyne'
            if velodyne_dir.exists():
                matching_files = list(velodyne_dir.glob(f'{idx}_*.npy'))
                if matching_files:
                    lidar_file = matching_files[0]

        # 如果仍然找不到文件，返回虚拟点云
        if not lidar_file.exists():
            if self.logger is not None:
                self.logger.warning(f"Point cloud file not found: {idx} in {target_corruption_path}, using dummy data")
            # 返回虚拟点云数据
            return np.zeros((1000, 4), dtype=np.float32)

        # 加载.npy格式的点云数据
        try:
            points = np.load(str(lidar_file))
        except Exception as e:
            if self.logger is not None:
                self.logger.warning(f"Failed to load point cloud file {lidar_file}: {e}, using dummy data")
            return np.zeros((1000, 4), dtype=np.float32)

        # 确保数据形状正确 (N, 4)
        if points.ndim == 1:
            points = points.reshape(-1, 4)
        elif points.shape[1] != 4:
            points = points[:, :4]  # 只取前4维

        return points.astype(np.float32)

    def get_image(self, idx):
        """加载图像数据"""
        # 从文件名中提取腐败类型
        corruption_from_filename = self._extract_corruption_from_filename(idx)

        # 如果文件名包含腐败类型，使用对应的目录
        if corruption_from_filename:
            target_corruption_path = self.root_path / corruption_from_filename
        else:
            # 否则使用当前设置的腐败类型目录
            target_corruption_path = self.root_split_path

        # 首先尝试标准格式 (000000.png)
        img_file = target_corruption_path / 'images' / 'val' / ('%s.png' % idx)
        if not img_file.exists():
            # 尝试直接在images目录下
            img_file = target_corruption_path / 'images' / ('%s.png' % idx)

        # 如果标准格式不存在，尝试KITTI-C格式 (000000_corruption_d1.png)
        if not img_file.exists():
            # 尝试在val子目录中查找匹配的文件
            images_val_dir = target_corruption_path / 'images' / 'val'
            if images_val_dir.exists():
                matching_files = list(images_val_dir.glob(f'{idx}_*.png'))
                if matching_files:
                    img_file = matching_files[0]

        # 如果还是找不到，尝试直接在images目录下查找
        if not img_file.exists():
            images_dir = target_corruption_path / 'images'
            if images_dir.exists():
                matching_files = list(images_dir.glob(f'{idx}_*.png'))
                if matching_files:
                    img_file = matching_files[0]

        # 如果仍然找不到文件，返回虚拟图像
        if not img_file.exists():
            if self.logger is not None:
                self.logger.warning(f"Image file not found: {idx} in {target_corruption_path}, using dummy image")
            # 返回虚拟图像数据 (KITTI标准尺寸)
            return np.zeros((375, 1242, 3), dtype=np.float32)

        try:
            image = io.imread(img_file)
            image = image.astype(np.float32)
            image /= 255.0
            return image
        except Exception as e:
            if self.logger is not None:
                self.logger.warning(f"Failed to load image file {img_file}: {e}, using dummy image")
            return np.zeros((375, 1242, 3), dtype=np.float32)

    def get_image_shape(self, idx):
        # 从文件名中提取腐败类型
        corruption_from_filename = self._extract_corruption_from_filename(idx)

        # 如果文件名包含腐败类型，使用对应的目录
        if corruption_from_filename:
            target_corruption_path = self.root_path / corruption_from_filename
        else:
            # 否则使用当前设置的腐败类型目录
            target_corruption_path = self.root_split_path

        # 首先尝试标准格式 (000000.png)
        img_file = target_corruption_path / 'images' / 'val' / ('%s.png' % idx)
        if not img_file.exists():
            # 尝试直接在images目录下
            img_file = target_corruption_path / 'images' / ('%s.png' % idx)

        # 如果标准格式不存在，尝试KITTI-C格式 (000000_corruption_d1.png)
        if not img_file.exists():
            # 尝试在val子目录中查找匹配的文件
            images_val_dir = target_corruption_path / 'images' / 'val'
            if images_val_dir.exists():
                matching_files = list(images_val_dir.glob(f'{idx}_*.png'))
                if matching_files:
                    img_file = matching_files[0]

        # 如果还是找不到，尝试直接在images目录下查找
        if not img_file.exists():
            images_dir = target_corruption_path / 'images'
            if images_dir.exists():
                matching_files = list(images_dir.glob(f'{idx}_*.png'))
                if matching_files:
                    img_file = matching_files[0]

        # 如果仍然找不到文件，使用默认尺寸
        if not img_file.exists():
            if self.logger is not None:
                self.logger.warning(f"Image file not found for shape: {idx} in {target_corruption_path}, using default shape")
            # 返回KITTI标准图像尺寸
            return np.array([375, 1242], dtype=np.int32)

        try:
            return np.array(io.imread(img_file).shape[:2], dtype=np.int32)
        except Exception as e:
            if self.logger is not None:
                self.logger.warning(f"Failed to read image shape from {img_file}: {e}, using default shape")
            return np.array([375, 1242], dtype=np.int32)

    def get_label(self, idx):
        """获取标签信息"""
        # 标签文件使用纯数字部分（去掉腐败类型后缀）
        # 例如：000000_sun_d2 -> 000000
        if '_' in str(idx):
            clean_idx = str(idx).split('_')[0]
        else:
            clean_idx = str(idx)

        label_file = self.root_split_path / 'label_2' / ('%s.txt' % clean_idx)
        if not label_file.exists():
            # 如果没有标签文件，返回空标签
            return []
        return object3d_kitti.get_objects_from_label(label_file)

    def get_calib(self, idx):
        """获取标定信息"""
        # 从sample_idx中提取纯数字部分（去掉腐败类型后缀）
        # 例如：006014_sun_d3 -> 006014
        if '_' in str(idx):
            clean_idx = str(idx).split('_')[0]
        else:
            clean_idx = str(idx)

        # 标定文件在腐败类型目录下的calib文件夹中
        calib_file = self.root_split_path / 'calib' / ('%s.txt' % clean_idx)

        if not calib_file.exists():
            # 如果没有标定文件，尝试使用根目录下的标定文件
            calib_file = self.root_path / 'calib' / ('%s.txt' % clean_idx)

        if not calib_file.exists():
            # 如果还是没有，使用默认的KITTI标定参数
            if self.logger is not None:
                self.logger.warning(f"Calibration file not found: {calib_file}, using default calibration")
            return self._get_default_calibration()

        return calibration_kitti.Calibration(calib_file)

    def _get_default_calibration(self):
        """返回默认的KITTI标定参数"""
        # 创建一个临时的标定文件内容
        import tempfile
        import os

        # KITTI的默认标定参数
        calib_content = """P0: 7.215377000000e+02 0.000000000000e+00 6.095593000000e+02 0.000000000000e+00 0.000000000000e+00 7.215377000000e+02 1.728540000000e+02 0.000000000000e+00 0.000000000000e+00 0.000000000000e+00 1.000000000000e+00 0.000000000000e+00
P1: 7.215377000000e+02 0.000000000000e+00 6.095593000000e+02 -3.875744000000e+02 0.000000000000e+00 7.215377000000e+02 1.728540000000e+02 0.000000000000e+00 0.000000000000e+00 0.000000000000e+00 1.000000000000e+00 0.000000000000e+00
P2: 7.215377000000e+02 0.000000000000e+00 6.095593000000e+02 4.485728000000e+01 0.000000000000e+00 7.215377000000e+02 1.728540000000e+02 2.163791000000e-01 0.000000000000e+00 0.000000000000e+00 1.000000000000e+00 2.745884000000e-03
P3: 7.215377000000e+02 0.000000000000e+00 6.095593000000e+02 -3.395242000000e+02 0.000000000000e+00 7.215377000000e+02 1.728540000000e+02 2.199936000000e+00 0.000000000000e+00 0.000000000000e+00 1.000000000000e+00 2.729905000000e-03
R0_rect: 9.999239000000e-01 9.837760000000e-03 -7.445048000000e-03 -9.869795000000e-03 9.999421000000e-01 -4.278459000000e-03 7.402527000000e-03 4.351614000000e-03 9.999631000000e-01
Tr_velo_to_cam: 7.533745000000e-03 -9.999714000000e-01 -6.166020000000e-04 -4.069766000000e-03 1.480249000000e-02 7.280733000000e-04 -9.998902000000e-01 -7.631618000000e-02 9.998621000000e-01 7.523790000000e-03 1.480755000000e-02 -2.717806000000e-01
Tr_imu_to_velo: 9.999976000000e-01 7.553071000000e-04 -2.035826000000e-03 -8.086759000000e-01 -7.854027000000e-04 9.998898000000e-01 -1.482298000000e-02 3.195559000000e-01 2.024406000000e-03 1.482454000000e-02 9.998881000000e-01 -7.997231000000e-01"""

        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(calib_content)
            temp_calib_file = f.name

        # 创建标定对象
        calib = calibration_kitti.Calibration(temp_calib_file)

        # 删除临时文件
        os.unlink(temp_calib_file)

        return calib

    def get_road_plane(self, idx):
        plane_file = self.root_split_path / 'planes' / ('%s.txt' % idx)
        if not plane_file.exists():
            return None

        with open(plane_file, 'r') as f:
            lines = f.readlines()
        lines = [float(i) for i in lines[3].split()]
        plane = np.asarray(lines)

        # Ensure normal is always facing up, this is in the rectified camera coordinate
        if plane[1] > 0:
            plane = -plane

        norm = np.linalg.norm(plane[0:3])
        plane = plane / norm
        return plane

    def get_sparsedepth(self, idx):
        """获取稀疏深度图"""
        try:
            # 从点云投影生成稀疏深度图
            points = self.get_lidar(idx)
            calib = self.get_calib(idx)

            # 投影点云到图像平面
            pts_rect = calib.lidar_to_rect(points[:, 0:3])
            fov_flag = self.get_fov_flag(pts_rect, self.get_image_shape(idx), calib)
            pts_img, pts_rect_depth = calib.rect_to_img(pts_rect)

            # 创建稀疏深度图
            img_shape = self.get_image_shape(idx)
            sparse_depth = np.zeros((img_shape[0], img_shape[1], 2), dtype=np.float32)

            # 只保留在FOV内的点
            pts_img = pts_img[fov_flag]
            pts_rect_depth = pts_rect_depth[fov_flag]

            # 填充稀疏深度图
            for i, (u, v) in enumerate(pts_img.astype(int)):
                if 0 <= u < img_shape[1] and 0 <= v < img_shape[0]:
                    sparse_depth[v, u, 0] = pts_rect_depth[i]
                    sparse_depth[v, u, 1] = 1.0  # 有效性标志

            return sparse_depth
        except Exception as e:
            if self.logger is not None:
                self.logger.warning(f"Failed to generate sparse depth for {idx}: {e}, using dummy sparse depth")
            # 返回虚拟稀疏深度图
            img_shape = self.get_image_shape(idx)
            return np.zeros((img_shape[0], img_shape[1], 2), dtype=np.float32)

    @staticmethod
    def get_fov_flag(pts_rect, img_shape, calib):
        """获取在视野内的点的标志"""
        pts_img, pts_rect_depth = calib.rect_to_img(pts_rect)
        val_flag_1 = np.logical_and(pts_img[:, 0] >= 0, pts_img[:, 0] < img_shape[1])
        val_flag_2 = np.logical_and(pts_img[:, 1] >= 0, pts_img[:, 1] < img_shape[0])
        val_flag_merge = np.logical_and(val_flag_1, val_flag_2)
        pts_valid_flag = np.logical_and(val_flag_merge, pts_rect_depth >= 0)
        return pts_valid_flag

    def __len__(self):
        if self._merge_all_iters_to_one_epoch:
            return len(self.kitti_infos) * self.total_epochs

        return len(self.kitti_infos)

    def __getitem__(self, index):
        if self._merge_all_iters_to_one_epoch:
            index = index % len(self.kitti_infos)

        info = copy.deepcopy(self.kitti_infos[index])

        sample_idx = info['point_cloud']['lidar_idx']

        img_shape = info['image']['image_shape']
        calib = self.get_calib(sample_idx)
        get_item_list = self.dataset_cfg.get('GET_ITEM_LIST', ['points'])

        input_dict = {
            'frame_id': sample_idx,
            'calib': calib,
            'image_shape': img_shape,
        }

        if 'annos' in info and len(info['annos']['name']) > 0:
            annos = info['annos']
            annos = common_utils.drop_info_with_name(annos, name='DontCare')

            # 检查是否有有效的标注
            if len(annos['name']) > 0:
                loc, dims, rots = annos['location'], annos['dimensions'], annos['rotation_y']
                gt_names = annos['name']
                gt_boxes_camera = np.concatenate([loc, dims, rots[..., np.newaxis]], axis=1).astype(np.float32)
                gt_boxes_lidar = box_utils.boxes3d_kitti_camera_to_lidar(gt_boxes_camera, calib)

                input_dict.update({
                    'gt_names': gt_names,
                    'gt_boxes': gt_boxes_lidar
                })
            else:
                # 没有有效标注时，提供空的标注
                input_dict.update({
                    'gt_names': np.array([]),
                    'gt_boxes': np.zeros((0, 7), dtype=np.float32)
                })
        else:
            # 测试模式下没有标注信息
            input_dict.update({
                'gt_names': np.array([]),
                'gt_boxes': np.zeros((0, 7), dtype=np.float32)
            })

        if "points" in get_item_list:
            points = self.get_lidar(sample_idx)
            input_dict['points'] = points

        if "images" in get_item_list:
            images = self.get_image(sample_idx)
            input_dict['images'] = images

        if "sparsedepth" in get_item_list:
            sparse_depth = self.get_sparsedepth(sample_idx)
            input_dict['sparsedepth'] = sparse_depth

        # 添加calib_matricies支持
        if "calib_matricies" in get_item_list:
            from . import kitti_utils
            input_dict["trans_lidar_to_cam"], input_dict["trans_cam_to_img"] = kitti_utils.calib_to_matricies(calib)

        # 确定是否使用深度信息
        usedepth = "sparsedepth" in get_item_list or "depth_maps" in get_item_list

        input_dict['calib'] = calib
        data_dict = self.prepare_data(data_dict=input_dict, usedepth=usedepth)

        data_dict['image_shape'] = img_shape
        return data_dict

    def prepare_data(self, data_dict, usedepth):
        """
        数据预处理方法，兼容原有的prepare_data接口

        Args:
            data_dict: 输入数据字典
            usedepth: 是否使用深度信息

        Returns:
            data_dict: 处理后的数据字典
        """
        # 调用父类的prepare_data方法
        # 如果父类不支持usedepth参数，则只传递data_dict
        try:
            return super().prepare_data(data_dict=data_dict, usedepth=usedepth)
        except TypeError:
            # 如果父类不支持usedepth参数，则使用原始方法
            return super().prepare_data(data_dict=data_dict)

    def set_corruption_type(self, corruption_type):
        """动态设置腐败类型"""
        if corruption_type in self.corruption_types:
            self.corruption_type = corruption_type
            self.root_split_path = self.root_path / self.corruption_type
        else:
            raise ValueError(f"Unsupported corruption type: {corruption_type}")

    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None):
        """
        生成预测结果字典，用于评估

        Args:
            batch_dict: 批次数据字典
                frame_id: 帧ID列表
                calib: 标定信息列表
                image_shape: 图像尺寸列表
            pred_dicts: 预测结果列表
                pred_boxes: (N, 7), Tensor - 预测的3D框
                pred_scores: (N), Tensor - 预测分数
                pred_labels: (N), Tensor - 预测标签
            class_names: 类别名称列表
            output_path: 输出路径（可选）

        Returns:
            annos: 预测结果注释列表
        """
        def get_template_prediction(num_samples):
            ret_dict = {
                'name': np.zeros(num_samples, dtype='<U10'),
                'truncated': np.zeros(num_samples, dtype=np.float32),
                'occluded': np.zeros(num_samples, dtype=np.int32),
                'alpha': np.zeros(num_samples, dtype=np.float32),
                'bbox': np.zeros([num_samples, 4], dtype=np.float32),
                'dimensions': np.zeros([num_samples, 3], dtype=np.float32),
                'location': np.zeros([num_samples, 3], dtype=np.float32),
                'rotation_y': np.zeros(num_samples, dtype=np.float32),
                'score': np.zeros(num_samples, dtype=np.float32),
                'boxes_lidar': np.zeros([num_samples, 7], dtype=np.float32)
            }
            return ret_dict

        def generate_single_sample_dict(batch_index, box_dict):
            pred_scores = box_dict['pred_scores'].cpu().numpy()
            pred_boxes = box_dict['pred_boxes'].cpu().numpy()
            pred_labels = box_dict['pred_labels'].cpu().numpy()
            pred_dict = get_template_prediction(pred_scores.shape[0])

            if pred_scores.shape[0] == 0:
                return pred_dict

            calib = batch_dict['calib'][batch_index]
            image_shape = batch_dict['image_shape'][batch_index].cpu().numpy()

            # 转换坐标系
            pred_boxes_camera = box_utils.boxes3d_lidar_to_kitti_camera(pred_boxes, calib)
            pred_boxes_img = box_utils.boxes3d_kitti_camera_to_imageboxes(
                pred_boxes_camera, calib, image_shape=image_shape
            )

            pred_dict['name'] = np.array(class_names)[pred_labels - 1]
            pred_dict['alpha'] = -np.arctan2(-pred_boxes[:, 1], pred_boxes[:, 0]) + pred_boxes_camera[:, 6]
            pred_dict['bbox'] = pred_boxes_img
            pred_dict['dimensions'] = pred_boxes_camera[:, 3:6]
            pred_dict['location'] = pred_boxes_camera[:, 0:3]
            pred_dict['rotation_y'] = pred_boxes_camera[:, 6]
            pred_dict['score'] = pred_scores
            pred_dict['boxes_lidar'] = pred_boxes

            return pred_dict

        annos = []
        for index, box_dict in enumerate(pred_dicts):
            frame_id = batch_dict['frame_id'][index]
            single_pred_dict = generate_single_sample_dict(index, box_dict)
            single_pred_dict['frame_id'] = frame_id
            annos.append(single_pred_dict)

        if output_path is not None:
            # 保存预测结果到文件
            import pickle
            output_path = Path(output_path)
            output_path.mkdir(parents=True, exist_ok=True)

            with open(output_path / 'predictions.pkl', 'wb') as f:
                pickle.dump(annos, f)

        return annos

    def evaluation(self, det_annos, class_names, **kwargs):
        """
        评估预测结果

        Args:
            det_annos: 检测结果注释列表
            class_names: 类别名称列表
            **kwargs: 其他参数

        Returns:
            result_str: 评估结果字符串
            result_dict: 评估结果字典
        """
        try:
            # 检查是否有ground truth数据
            has_gt = len(self.kitti_infos) > 0 and 'annos' in self.kitti_infos[0].keys()

            # 如果info文件中没有标注，检查labels文件夹是否存在
            if not has_gt:
                corruption_type = getattr(self.dataset_cfg, 'DEFAULT_CORRUPTION', 'fog')
                labels_path = self.root_path / corruption_type / 'labels' / 'val'
                if labels_path.exists():
                    label_files = list(labels_path.glob('*.txt'))
                    if len(label_files) > 0:
                        if self.logger:
                            self.logger.info(f"Found {len(label_files)} label files in {labels_path}")
                            self.logger.info("Labels folder exists but not loaded in info files. Consider regenerating info files.")
                        has_gt = True  # 标注文件存在，但需要重新生成info文件

            if not has_gt:
                # 如果没有ground truth，返回基本的统计信息
                result_str = f"KITTI-C Robustness Test Results:\n"
                result_str += f"Total samples processed: {len(det_annos)}\n"

                # 统计检测到的目标数量
                total_detections = sum(len(anno.get('name', [])) for anno in det_annos)
                avg_detections = total_detections / len(det_annos) if det_annos else 0

                result_str += f"Total detections: {total_detections}\n"
                result_str += f"Average detections per frame: {avg_detections:.2f}\n"

                # 统计各类别检测数量
                for class_name in class_names:
                    class_count = 0
                    for anno in det_annos:
                        if 'name' in anno and len(anno['name']) > 0:
                            class_count += np.sum(anno['name'] == class_name)
                    result_str += f"{class_name} detections: {class_count}\n"

                result_dict = {
                    'total_samples': len(det_annos),
                    'total_detections': total_detections,
                    'avg_detections_per_frame': avg_detections
                }

                for class_name in class_names:
                    class_count = 0
                    for anno in det_annos:
                        if 'name' in anno and len(anno['name']) > 0:
                            class_count += np.sum(anno['name'] == class_name)
                    result_dict[f'{class_name}_detections'] = class_count

                return result_str, result_dict
        except Exception as e:
            # 如果评估过程中出现任何错误，返回基本信息
            if self.logger is not None:
                self.logger.error(f"Error during evaluation: {e}")

            result_str = f"KITTI-C Evaluation Error: {e}\n"
            result_str += f"Total samples processed: {len(det_annos)}\n"

            result_dict = {
                'evaluation_error': str(e),
                'total_samples': len(det_annos)
            }

            return result_str, result_dict
        else:
            # 如果有ground truth，使用标准的KITTI评估
            try:
                # 尝试多种导入方式
                eval_utils = None
                try:
                    from pcdet.utils import eval_utils
                except ImportError:
                    try:
                        # 尝试从tools目录导入
                        import sys
                        import os
                        tools_path = os.path.join(os.path.dirname(__file__), '../../../tools')
                        if tools_path not in sys.path:
                            sys.path.insert(0, tools_path)
                        from eval_utils import eval_utils
                    except ImportError:
                        # 最后尝试直接导入
                        try:
                            from eval_utils.eval_utils import get_official_eval_result
                            class EvalUtils:
                                @staticmethod
                                def get_official_eval_result(gt_annos, dt_annos, class_names):
                                    return get_official_eval_result(gt_annos, dt_annos, class_names)
                            eval_utils = EvalUtils()
                        except ImportError:
                            eval_utils = None

                if eval_utils is not None:
                    eval_det_annos = copy.deepcopy(det_annos)
                    eval_gt_annos = [copy.deepcopy(info['annos']) for info in self.kitti_infos]

                    ap_result_str, ap_dict = eval_utils.get_official_eval_result(eval_gt_annos, eval_det_annos, class_names)
                    return ap_result_str, ap_dict
                else:
                    raise ImportError("Cannot import eval_utils from any location")

            except (ImportError, Exception) as e:
                if self.logger is not None:
                    self.logger.warning(f"Cannot import eval_utils: {e}, using basic evaluation")

                # 回退到基本评估
                result_str = f"KITTI-C Basic Evaluation (eval_utils not available):\n"
                result_str += f"Total samples processed: {len(det_annos)}\n"

                # 统计检测到的目标数量
                total_detections = sum(len(anno.get('name', [])) for anno in det_annos)
                avg_detections = total_detections / len(det_annos) if det_annos else 0

                result_str += f"Total detections: {total_detections}\n"
                result_str += f"Average detections per frame: {avg_detections:.2f}\n"

                # 统计各类别检测数量
                for class_name in class_names:
                    class_count = 0
                    for anno in det_annos:
                        if 'name' in anno and len(anno['name']) > 0:
                            class_count += np.sum(anno['name'] == class_name)
                    result_str += f"{class_name} detections: {class_count}\n"

                result_dict = {
                    'total_samples': len(det_annos),
                    'total_detections': total_detections,
                    'avg_detections_per_frame': avg_detections
                }

                for class_name in class_names:
                    class_count = 0
                    for anno in det_annos:
                        if 'name' in anno and len(anno['name']) > 0:
                            class_count += np.sum(anno['name'] == class_name)
                    result_dict[f'{class_name}_detections'] = class_count

                return result_str, result_dict


def create_kitti_c_infos(dataset_cfg, class_names, data_path, save_path, workers=4):
    """创建KITTI-C数据集的info文件"""
    dataset = KittiCDataset(dataset_cfg=dataset_cfg, class_names=class_names, root_path=data_path, training=False)
    train_split, val_split = 'train', 'val'

    train_filename = save_path / ('kitti_c_infos_%s.pkl' % train_split)
    val_filename = save_path / ('kitti_c_infos_%s.pkl' % val_split)
    trainval_filename = save_path / 'kitti_c_infos_trainval.pkl'
    test_filename = save_path / 'kitti_c_infos_test.pkl'

    print('---------------Start to generate data infos---------------')

    dataset.set_split(train_split)
    kitti_infos_train = dataset.get_infos(num_workers=workers, has_label=True, count_inside_pts=True)
    with open(train_filename, 'wb') as f:
        pickle.dump(kitti_infos_train, f)
    print('KITTI-C info train file is saved to %s' % train_filename)

    dataset.set_split(val_split)
    kitti_infos_val = dataset.get_infos(num_workers=workers, has_label=True, count_inside_pts=True)
    with open(val_filename, 'wb') as f:
        pickle.dump(kitti_infos_val, f)
    print('KITTI-C info val file is saved to %s' % val_filename)

    with open(trainval_filename, 'wb') as f:
        pickle.dump(kitti_infos_train + kitti_infos_val, f)
    print('KITTI-C info trainval file is saved to %s' % trainval_filename)

    dataset.set_split('test')
    kitti_infos_test = dataset.get_infos(num_workers=workers, has_label=False, count_inside_pts=False)
    with open(test_filename, 'wb') as f:
        pickle.dump(kitti_infos_test, f)
    print('KITTI-C info test file is saved to %s' % test_filename)

    print('---------------Start create groundtruth database for data augmentation---------------')
    dataset.set_split(train_split)
    dataset.create_groundtruth_database(train_filename, split=train_split)

    print('---------------Data preparation Done---------------')


if __name__ == '__main__':
    import sys
    if sys.argv.__len__() > 1 and sys.argv[1] == 'create_kitti_c_infos':
        import yaml
        from pathlib import Path
        from easydict import EasyDict
        
        dataset_cfg = EasyDict(yaml.safe_load(open(sys.argv[2])))
        ROOT_DIR = (Path(__file__).resolve().parent / '../../../').resolve()
        create_kitti_c_infos(
            dataset_cfg=dataset_cfg,
            class_names=['Car', 'Pedestrian', 'Cyclist'],
            data_path=ROOT_DIR / 'data' / 'kitti_c',
            save_path=ROOT_DIR / 'data' / 'kitti_c'
        )
