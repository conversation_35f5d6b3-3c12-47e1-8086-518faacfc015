from __future__ import division

from math import cos, pi

from det3d.solver import learning_schedules_fastai as lsf

from .hook import Hook


class LrUpdaterHook(Hook):
    def __init__(
        self, by_epoch=True, warmup=None, warmup_iters=0, warmup_ratio=0.1, **kwargs
    ):
        if warmup is not None:
            if warmup not in ["constant", "linear", "exp"]:
                raise ValueError(
                    '"{}" is not a supported type for warming up, valid types'
                    ' are "constant" and "linear"'.format(warmup)
                )

        if warmup is not None:
            assert warmup_iters > 0, '"warmup_iters" must be a positive integer'
            assert 0 < warmup_ratio <= 1.0, '"warmup_ratio" must be in range (0,1]'

        self.by_epoch = by_epoch
        self.warmup = warmup
        self.warmup_ratio = warmup_ratio
        self.warmup_iters = warmup_iters

        self.base_lr = []  # initial lr for all param groups
        self.regular_lr = []  # expected lr if no warming up is performed

    def _set_lr(self, trainer, lr_groups):
        for param_group, lr in zip(trainer.optimizer.param_groups, lr_groups):
            param_group["lr"] = lr

    def get_lr(self, runner, base_lr):
        raise NotImplementedError

    def get_regular_lr(self, trainer):
        return [self.get_lr(trainer, _base_lr) for _base_lr in self.base_lr]

    def get_warmup_lr(self, cur_iters):
        if self.warmup == "constant":
            warmup_lr = [_lr * self.warmup_ratio for _lr in self.regular_lr]
        elif self.warmup == "linear":
            k = (1 - cur_iters / self.warmup_iters) * (1 - self.warmup_ratio)
            warmup_lr = [_lr * (1 - k) for _lr in self.regular_lr]
        elif self.warmup == "exp":
            k = self.warmup_ratio ** (1 - cur_iters / self.warmup_iters)
            warmup_lr = [_lr * k for _lr in self.regular_lr]

        return warmup_lr

    def before_run(self, trainer):
        for group in trainer.optimizer.param_groups:
            group.setdefault("initial_lr", group["lr"])
        self.base_lr = [group["initial_lr"] for group in trainer.optimizer.param_groups]

    def before_train_epoch(self, trainer):
        if not self.by_epoch:
            return
        self.regular_lr = self.get_regular_lr(trainer)
        self._set_lr(trainer, self.regular_lr)

    def before_train_iter(self, trainer):
        cur_iter = trainer.iter
        if not self.by_epoch:
            self.regular_lr = self.get_regular_lr(trainer)
            if self.warmup is None or cur_iter >= self.warmup_iters:
                self._set_lr(trainer, self.regular_lr)
            else:
                warmup_lr = self.get_warmup_lr(cur_iter)
                self._set_lr(trainer, warmup_lr)
        elif self.by_epoch:
            if self.warmup is None or cur_iter > self.warmup_iters:
                return
            elif cur_iter == self.warmup_iters:
                self._set_lr(trainer, self.regular_lr)
            else:
                warmup_lr = self.get_warmup_lr(cur_iter)
                self._set_lr(trainer, warmup_lr)


class FixedLrUpdaterHook(LrUpdaterHook):
    def __init__(self, **kwargs):
        super(FixedLrUpdaterHook, self).__init__(**kwargs)

    def get_lr(self, trainer, base_lr):
        return base_lr


class StepLrUpdaterHook(LrUpdaterHook):
    def __init__(self, step, gamma=0.1, **kwargs):
        assert isinstance(step, (list, int))
        if isinstance(step, list):
            for s in step:
                assert isinstance(s, int) and s > 0
        elif isinstance(step, int):
            assert step > 0
        else:
            raise TypeError('"step" must be a list or integer')
        self.step = step
        self.gamma = gamma
        super(StepLrUpdaterHook, self).__init__(**kwargs)

    def get_lr(self, runner, base_lr):
        progress = runner.epoch if self.by_epoch else trainer.iter

        if isinstance(self.step, int):
            return base_lr * (self.gamma ** (progress // self.step))

        exp = len(self.step)
        for i, s in enumerate(self.step):
            if progress < s:
                exp = i
                break

        return base_lr * self.gamma ** exp


class ExpLrUpdaterHook(LrUpdaterHook):
    def __init__(self, gamma, **kwargs):
        self.gamma = gamma
        super(ExpLrUpdaterHook, self).__init__(**kwargs)

    def get_lr(self, runner, base_lr):
        progress = trainer.epoch if self.by_epoch else trainer.iter
        return base_lr * self.gamma ** progress


class PolyLrUpdaterHook(LrUpdaterHook):
    def __init__(self, power=1.0, min_lr=0.0, **kwargs):
        self.power = power
        self.min_lr = min_lr
        super(PolyLrUpdaterHook, self).__init__(**kwargs)

    def get_lr(self, trainer, base_lr):
        if self.by_epoch:
            progress = trainer.epoch
            max_progress = trainer.max_epochs
        else:
            progress = trainer.iter
            max_progress = trainer.max_iters
        coeff = (1 - progress / max_progress) ** self.power
        return (base_lr - self.min_lr) * coeff + self.min_lr


class InvLrUpdaterHook(LrUpdaterHook):
    def __init__(self, gamma, power=1.0, **kwargs):
        self.gamma = gamma
        self.power = power
        super(InvLrUpdaterHook, self).__init__(**kwargs)

    def get_lr(self, trainer, base_lr):
        progress = trainer.epoch if self.by_epoch else trainer.iter
        return base_lr * (1 + self.gamma * progress) ** (-self.power)


class CosineLrUpdaterHook(LrUpdaterHook):
    def __init__(self, target_lr=0, **kwargs):
        self.target_lr = target_lr
        super(CosineLrUpdaterHook, self).__init__(**kwargs)

    def get_lr(self, trainer, base_lr):
        if self.by_epoch:
            progress = trainer.epoch
            max_progress = trainer.max_epochs
        else:
            progress = trainer.iter
            max_progress = trainer.max_iters

        return self.target_lr + 0.5 * (base_lr - self.target_lr) * (
            1 + cos(pi * (progress / max_progress))
        )
