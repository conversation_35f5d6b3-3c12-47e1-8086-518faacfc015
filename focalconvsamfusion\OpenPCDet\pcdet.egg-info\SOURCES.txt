LICENSE
setup.py
pcdet/__init__.py
pcdet/config.py
pcdet/version.py
pcdet.egg-info/PKG-INFO
pcdet.egg-info/SOURCES.txt
pcdet.egg-info/dependency_links.txt
pcdet.egg-info/requires.txt
pcdet.egg-info/top_level.txt
pcdet/datasets/__init__.py
pcdet/datasets/__init__backup.py
pcdet/datasets/__init__cor.py
pcdet/datasets/dataset.py
pcdet/models/__init__.py
pcdet/models/backbones_2d/__init__.py
pcdet/models/backbones_2d/base_bev_backbone.py
pcdet/models/backbones_2d/map_to_bev/__init__.py
pcdet/models/backbones_2d/map_to_bev/conv2d_collapse.py
pcdet/models/backbones_2d/map_to_bev/height_compression.py
pcdet/models/backbones_2d/map_to_bev/pointpillar_scatter.py
pcdet/models/backbones_3d/__init__.py
pcdet/models/backbones_3d/pointnet2_backbone.py
pcdet/models/backbones_3d/spconv_backbone.py
pcdet/models/backbones_3d/spconv_backbone_focal.py
pcdet/models/backbones_3d/spconv_backbone_focal_beifen.py
pcdet/models/backbones_3d/spconv_backbone_focal_cuda.py
pcdet/models/backbones_3d/spconv_backbone_focal_db.py
pcdet/models/backbones_3d/spconv_backbone_focal_gx.py
pcdet/models/backbones_3d/spconv_unet.py
pcdet/models/backbones_3d/pfe/__init__.py
pcdet/models/backbones_3d/pfe/voxel_set_abstraction.py
pcdet/models/backbones_3d/vfe/__init__.py
pcdet/models/backbones_3d/vfe/image_vfe.py
pcdet/models/backbones_3d/vfe/mean_vfe.py
pcdet/models/backbones_3d/vfe/pillar_vfe.py
pcdet/models/backbones_3d/vfe/vfe_template.py
pcdet/models/dense_heads/__init__.py
pcdet/models/dense_heads/anchor_head_multi.py
pcdet/models/dense_heads/anchor_head_single.py
pcdet/models/dense_heads/anchor_head_template.py
pcdet/models/dense_heads/center_head.py
pcdet/models/dense_heads/point_head_box.py
pcdet/models/dense_heads/point_head_simple.py
pcdet/models/dense_heads/point_head_template.py
pcdet/models/dense_heads/point_intra_part_head.py
pcdet/models/detectors/PartA2_net.py
pcdet/models/detectors/__init__.py
pcdet/models/detectors/caddn.py
pcdet/models/detectors/centerpoint.py
pcdet/models/detectors/detector3d_template.py
pcdet/models/detectors/point_rcnn.py
pcdet/models/detectors/pointpillar.py
pcdet/models/detectors/pv_rcnn.py
pcdet/models/detectors/pv_rcnn_plusplus.py
pcdet/models/detectors/second_net.py
pcdet/models/detectors/second_net_iou.py
pcdet/models/detectors/voxel_rcnn.py
pcdet/models/roi_heads/__init__.py
pcdet/models/roi_heads/partA2_head.py
pcdet/models/roi_heads/pointrcnn_head.py
pcdet/models/roi_heads/pvrcnn_head.py
pcdet/models/roi_heads/roi_head_template.py
pcdet/models/roi_heads/second_head.py
pcdet/models/roi_heads/voxelrcnn_head.py
pcdet/ops/iou3d_nms/src/iou3d_cpu.cpp
pcdet/ops/iou3d_nms/src/iou3d_nms.cpp
pcdet/ops/iou3d_nms/src/iou3d_nms_api.cpp
pcdet/ops/iou3d_nms/src/iou3d_nms_kernel.cu
pcdet/ops/pointnet2/pointnet2_batch/src/ball_query.cpp
pcdet/ops/pointnet2/pointnet2_batch/src/ball_query_gpu.cu
pcdet/ops/pointnet2/pointnet2_batch/src/group_points.cpp
pcdet/ops/pointnet2/pointnet2_batch/src/group_points_gpu.cu
pcdet/ops/pointnet2/pointnet2_batch/src/interpolate.cpp
pcdet/ops/pointnet2/pointnet2_batch/src/interpolate_gpu.cu
pcdet/ops/pointnet2/pointnet2_batch/src/pointnet2_api.cpp
pcdet/ops/pointnet2/pointnet2_batch/src/sampling.cpp
pcdet/ops/pointnet2/pointnet2_batch/src/sampling_gpu.cu
pcdet/ops/pointnet2/pointnet2_stack/src/ball_query.cpp
pcdet/ops/pointnet2/pointnet2_stack/src/ball_query_gpu.cu
pcdet/ops/pointnet2/pointnet2_stack/src/group_points.cpp
pcdet/ops/pointnet2/pointnet2_stack/src/group_points_gpu.cu
pcdet/ops/pointnet2/pointnet2_stack/src/interpolate.cpp
pcdet/ops/pointnet2/pointnet2_stack/src/interpolate_gpu.cu
pcdet/ops/pointnet2/pointnet2_stack/src/pointnet2_api.cpp
pcdet/ops/pointnet2/pointnet2_stack/src/sampling.cpp
pcdet/ops/pointnet2/pointnet2_stack/src/sampling_gpu.cu
pcdet/ops/pointnet2/pointnet2_stack/src/vector_pool.cpp
pcdet/ops/pointnet2/pointnet2_stack/src/vector_pool_gpu.cu
pcdet/ops/pointnet2/pointnet2_stack/src/voxel_query.cpp
pcdet/ops/pointnet2/pointnet2_stack/src/voxel_query_gpu.cu
pcdet/ops/roiaware_pool3d/src/roiaware_pool3d.cpp
pcdet/ops/roiaware_pool3d/src/roiaware_pool3d_kernel.cu
pcdet/ops/roipoint_pool3d/src/roipoint_pool3d.cpp
pcdet/ops/roipoint_pool3d/src/roipoint_pool3d_kernel.cu