ninja_required_version = 1.3
cxx = c++
nvcc = /usr/local/cuda-11.3/bin/nvcc

cflags = -pthread -B /home/<USER>/miniconda3/envs/focalsconvmm_nus_env/compiler_compat -Wl,--sysroot=/ -Wsign-compare -DNDEBUG -g -fwrapv -O3 -Wall -Wstrict-prototypes -fPIC -I/home/<USER>/miniconda3/envs/focalsconvmm_nus_env/lib/python3.8/site-packages/torch/include -I/home/<USER>/miniconda3/envs/focalsconvmm_nus_env/lib/python3.8/site-packages/torch/include/torch/csrc/api/include -I/home/<USER>/miniconda3/envs/focalsconvmm_nus_env/lib/python3.8/site-packages/torch/include/TH -I/home/<USER>/miniconda3/envs/focalsconvmm_nus_env/lib/python3.8/site-packages/torch/include/THC -I/usr/local/cuda-11.3/include -I/home/<USER>/miniconda3/envs/focalsconvmm_nus_env/include/python3.8 -c
post_cflags = -g '-I /usr/local/cuda/include' -DTORCH_API_INCLUDE_EXTENSION_H '-DPYBIND11_COMPILER_TYPE="_gcc"' '-DPYBIND11_STDLIB="_libstdcpp"' '-DPYBIND11_BUILD_ABI="_cxxabi1011"' -DTORCH_EXTENSION_NAME=iou3d_nms_cuda -D_GLIBCXX_USE_CXX11_ABI=0 -std=c++14
cuda_cflags = -I/home/<USER>/miniconda3/envs/focalsconvmm_nus_env/lib/python3.8/site-packages/torch/include -I/home/<USER>/miniconda3/envs/focalsconvmm_nus_env/lib/python3.8/site-packages/torch/include/torch/csrc/api/include -I/home/<USER>/miniconda3/envs/focalsconvmm_nus_env/lib/python3.8/site-packages/torch/include/TH -I/home/<USER>/miniconda3/envs/focalsconvmm_nus_env/lib/python3.8/site-packages/torch/include/THC -I/usr/local/cuda-11.3/include -I/home/<USER>/miniconda3/envs/focalsconvmm_nus_env/include/python3.8 -c
cuda_post_cflags = -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr --compiler-options ''"'"'-fPIC'"'"'' -O2 -DTORCH_API_INCLUDE_EXTENSION_H '-DPYBIND11_COMPILER_TYPE="_gcc"' '-DPYBIND11_STDLIB="_libstdcpp"' '-DPYBIND11_BUILD_ABI="_cxxabi1011"' -DTORCH_EXTENSION_NAME=iou3d_nms_cuda -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_80,code=compute_80 -gencode=arch=compute_80,code=sm_80 -std=c++14
ldflags = 

rule compile
  command = $cxx -MMD -MF $out.d $cflags -c $in -o $out $post_cflags
  depfile = $out.d
  deps = gcc

rule cuda_compile
  depfile = $out.d
  deps = gcc
  command = $nvcc --generate-dependencies-with-compile --dependency-output $out.d $cuda_cflags -c $in -o $out $cuda_post_cflags



build /home/<USER>/sda/szy/focalsconv-mm/CenterPoint/det3d/ops/iou3d_nms/build/temp.linux-x86_64-cpython-38/src/iou3d_cpu.o: compile /home/<USER>/sda/szy/focalsconv-mm/CenterPoint/det3d/ops/iou3d_nms/src/iou3d_cpu.cpp
build /home/<USER>/sda/szy/focalsconv-mm/CenterPoint/det3d/ops/iou3d_nms/build/temp.linux-x86_64-cpython-38/src/iou3d_nms.o: compile /home/<USER>/sda/szy/focalsconv-mm/CenterPoint/det3d/ops/iou3d_nms/src/iou3d_nms.cpp
build /home/<USER>/sda/szy/focalsconv-mm/CenterPoint/det3d/ops/iou3d_nms/build/temp.linux-x86_64-cpython-38/src/iou3d_nms_api.o: compile /home/<USER>/sda/szy/focalsconv-mm/CenterPoint/det3d/ops/iou3d_nms/src/iou3d_nms_api.cpp
build /home/<USER>/sda/szy/focalsconv-mm/CenterPoint/det3d/ops/iou3d_nms/build/temp.linux-x86_64-cpython-38/src/iou3d_nms_kernel.o: cuda_compile /home/<USER>/sda/szy/focalsconv-mm/CenterPoint/det3d/ops/iou3d_nms/src/iou3d_nms_kernel.cu





