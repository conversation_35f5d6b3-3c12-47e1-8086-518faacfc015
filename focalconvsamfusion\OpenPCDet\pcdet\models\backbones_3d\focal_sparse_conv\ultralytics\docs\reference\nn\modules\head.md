---
description: 'Learn about Ultralytics YOLO modules: Segment, Classify, and RTDETRDecoder. Optimize object detection and classification in your project.'
keywords: Ultralytics, YOLO, object detection, pose estimation, RTDETRDecoder, modules, classes, documentation
---

## Detect
---
### ::: ultralytics.nn.modules.head.Detect
<br><br>

## Segment
---
### ::: ultralytics.nn.modules.head.Segment
<br><br>

## Pose
---
### ::: ultralytics.nn.modules.head.Pose
<br><br>

## Classify
---
### ::: ultralytics.nn.modules.head.Classify
<br><br>

## RTDETRDecoder
---
### ::: ultralytics.nn.modules.head.RTDETRDecoder
<br><br>
