---
description: Train a custom image classification model using Ultralytics YOLOv8 with ClassificationTrainer. Boost accuracy and efficiency today.
keywords: Ultralytics, YOLOv8, object detection, classification, training, API
---

## ClassificationTrainer
---
### ::: ultralytics.yolo.v8.classify.train.ClassificationTrainer
<br><br>

## train
---
### ::: ultralytics.yolo.v8.classify.train.train
<br><br>
