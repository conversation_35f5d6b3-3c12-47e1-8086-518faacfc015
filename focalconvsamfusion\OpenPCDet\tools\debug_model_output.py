#!/usr/bin/env python3
"""
调试模型输出，检查检测结果的合理性
"""

import _init_path
import pickle
import numpy as np
from pathlib import Path

def analyze_detection_results():
    """分析检测结果的详细信息"""
    
    result_file = Path('../output/kitti_models/sam_onlinev3_kitti_c_sun_adapted/original_test/result.pkl')
    
    if not result_file.exists():
        print(f"❌ 结果文件不存在: {result_file}")
        return
    
    print(f"📂 分析结果文件: {result_file}")
    
    with open(result_file, 'rb') as f:
        results = pickle.load(f)
    
    print(f"📊 总样本数: {len(results)}")
    
    # 统计检测结果
    total_detections = 0
    confidence_stats = []
    
    for i, result in enumerate(results[:10]):  # 只看前10个样本
        frame_id = result.get('frame_id', f'sample_{i}')
        boxes_3d = result.get('boxes_3d', np.array([]))
        scores_3d = result.get('scores_3d', np.array([]))
        labels_3d = result.get('labels_3d', np.array([]))
        
        num_detections = len(boxes_3d) if len(boxes_3d.shape) > 1 else 0
        total_detections += num_detections
        
        print(f"\n📋 样本 {i} (ID: {frame_id}):")
        print(f"  检测数量: {num_detections}")
        
        if num_detections > 0:
            print(f"  置信度范围: [{scores_3d.min():.3f}, {scores_3d.max():.3f}]")
            print(f"  平均置信度: {scores_3d.mean():.3f}")
            
            # 检查坐标范围
            if len(boxes_3d.shape) > 1 and boxes_3d.shape[1] >= 7:
                x_coords = boxes_3d[:, 0]
                y_coords = boxes_3d[:, 1] 
                z_coords = boxes_3d[:, 2]
                
                print(f"  位置范围:")
                print(f"    X: [{x_coords.min():.2f}, {x_coords.max():.2f}]")
                print(f"    Y: [{y_coords.min():.2f}, {y_coords.max():.2f}]")
                print(f"    Z: [{z_coords.min():.2f}, {z_coords.max():.2f}]")
                
                # 检查是否有异常坐标
                if np.any(np.abs(x_coords) > 100) or np.any(np.abs(y_coords) > 100):
                    print(f"  ⚠️ 警告：检测到异常坐标！")
            
            confidence_stats.extend(scores_3d.tolist())
        else:
            print(f"  ❌ 没有检测结果")
    
    print(f"\n📊 总体统计:")
    print(f"  总检测数量: {total_detections}")
    print(f"  平均每样本检测数: {total_detections / min(10, len(results)):.2f}")
    
    if confidence_stats:
        confidence_stats = np.array(confidence_stats)
        print(f"  置信度统计:")
        print(f"    最小值: {confidence_stats.min():.3f}")
        print(f"    最大值: {confidence_stats.max():.3f}")
        print(f"    平均值: {confidence_stats.mean():.3f}")
        print(f"    中位数: {np.median(confidence_stats):.3f}")
        
        # 检查高置信度检测
        high_conf = confidence_stats[confidence_stats > 0.5]
        print(f"    高置信度(>0.5)检测: {len(high_conf)} / {len(confidence_stats)}")
    
    # 检查是否有明显的问题
    print(f"\n🔍 问题诊断:")
    if total_detections == 0:
        print(f"  ❌ 严重问题：没有任何检测结果！")
    elif total_detections < len(results) * 0.1:
        print(f"  ⚠️ 问题：检测数量过少，可能模型有问题")
    elif confidence_stats and np.mean(confidence_stats) < 0.1:
        print(f"  ⚠️ 问题：置信度过低，可能模型不适配")
    else:
        print(f"  ✅ 检测结果数量和置信度看起来正常")


def compare_with_clean_kitti():
    """对比干净KITTI的结果（如果有的话）"""
    
    # 尝试找到干净KITTI的结果
    clean_result_paths = [
        '../output/kitti_models/sam_onlinev3/default/eval/epoch_80/val/result.pkl',
        '../output/kitti_models/sam_onlinev3/robofusion_tmux/eval/result.pkl',
    ]
    
    for clean_path in clean_result_paths:
        clean_file = Path(clean_path)
        if clean_file.exists():
            print(f"\n📊 对比干净KITTI结果: {clean_file}")
            
            with open(clean_file, 'rb') as f:
                clean_results = pickle.load(f)
            
            # 统计干净数据的检测结果
            clean_detections = 0
            clean_confidences = []
            
            for result in clean_results[:10]:
                boxes_3d = result.get('boxes_3d', np.array([]))
                scores_3d = result.get('scores_3d', np.array([]))
                
                num_det = len(boxes_3d) if len(boxes_3d.shape) > 1 else 0
                clean_detections += num_det
                
                if num_det > 0:
                    clean_confidences.extend(scores_3d.tolist())
            
            print(f"  干净KITTI检测数量: {clean_detections}")
            if clean_confidences:
                print(f"  干净KITTI平均置信度: {np.mean(clean_confidences):.3f}")
            
            break
    else:
        print(f"\n❌ 没有找到干净KITTI的结果进行对比")


def debug_model_inference():
    """调试模型推理过程"""

    print(f"\n🔧 调试模型推理...")

    # 检查模型配置
    import yaml
    config_file = 'cfgs/kitti_models/sam_onlinev3_kitti_c_original.yaml'

    try:
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)

        print(f"📋 模型配置:")
        model_cfg = config.get('MODEL', {})
        print(f"  模型名称: {model_cfg.get('NAME', 'Unknown')}")

        backbone_3d = model_cfg.get('BACKBONE_3D', {})
        print(f"  3D骨干网络: {backbone_3d.get('NAME', 'Unknown')}")
        print(f"  使用图像: {backbone_3d.get('USE_IMG', False)}")
        print(f"  使用SAM: {backbone_3d.get('USE_SAM_ONLINEV2', False)}")

        post_proc = model_cfg.get('POST_PROCESSING', {})
        print(f"  置信度阈值: {post_proc.get('SCORE_THRESH', 'Unknown')}")
        print(f"  NMS阈值: {post_proc.get('NMS_CONFIG', {}).get('NMS_THRESH', 'Unknown')}")

    except Exception as e:
        print(f"❌ 读取配置失败: {e}")

    # 检查模型权重
    import torch
    ckpt_file = '../output/kitti_models/sam_onlinev3/robofusion_tmux/ckpt/checkpoint_epoch_80.pth'

    try:
        checkpoint = torch.load(ckpt_file, map_location='cpu')
        print(f"\n📦 模型权重信息:")
        print(f"  训练轮数: {checkpoint.get('epoch', 'Unknown')}")
        print(f"  模型状态字典键数量: {len(checkpoint.get('model_state', {}))}")

        # 检查一些关键的权重
        model_state = checkpoint.get('model_state', {})
        key_patterns = ['backbone_3d', 'dense_head', 'roi_head']

        for pattern in key_patterns:
            matching_keys = [k for k in model_state.keys() if pattern in k]
            print(f"  {pattern}相关权重: {len(matching_keys)}个")
            if matching_keys:
                print(f"    示例: {matching_keys[0]}")

    except Exception as e:
        print(f"❌ 读取权重失败: {e}")


def test_single_sample():
    """测试单个样本的推理"""

    print(f"\n🧪 测试单个样本推理...")

    try:
        import argparse
        from pcdet.config import cfg, cfg_from_yaml_file
        from pcdet.datasets import DatasetTemplate
        from pcdet.models import build_network, load_data_to_gpu
        from pcdet.utils import common_utils
        import torch

        # 加载配置
        config_file = 'cfgs/kitti_models/sam_onlinev3_kitti_c_original.yaml'
        cfg_from_yaml_file(config_file, cfg)

        # 创建数据集
        test_set = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=1,
            dist=False, workers=0, training=False
        )

        print(f"✅ 数据集创建成功，样本数: {len(test_set.dataset)}")

        # 加载模型
        model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=test_set.dataset)
        model.load_params_from_file('../output/kitti_models/sam_onlinev3/robofusion_tmux/ckpt/checkpoint_epoch_80.pth', logger=None)
        model.cuda()
        model.eval()

        print(f"✅ 模型加载成功")

        # 测试第一个样本
        for i, batch_dict in enumerate(test_set):
            if i >= 1:  # 只测试第一个样本
                break

            load_data_to_gpu(batch_dict)

            with torch.no_grad():
                pred_dicts, _ = model.forward(batch_dict)

            print(f"📊 推理结果:")
            if pred_dicts and len(pred_dicts) > 0:
                pred = pred_dicts[0]
                print(f"  预测框数量: {len(pred.get('pred_boxes', []))}")
                print(f"  预测分数: {pred.get('pred_scores', [])}")
                print(f"  预测标签: {pred.get('pred_labels', [])}")
            else:
                print(f"  ❌ 没有预测结果")

            break

    except Exception as e:
        print(f"❌ 单样本测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    analyze_detection_results()
    compare_with_clean_kitti()
    debug_model_inference()
    # test_single_sample()  # 暂时注释掉，因为可能有导入问题
