CLASS_NAMES: ['Car']

DATA_CONFIG:
    _BASE_CONFIG_: cfgs/dataset_configs/kitti_c_dataset.yaml
    GET_ITEM_LIST: ["points", "calib_matricies"]  # 只使用点云和标定，不使用图像
    
    # 测试时不需要数据增强
    DATA_AUGMENTOR:
        DISABLE_AUG_LIST: ['gt_sampling', 'random_world_flip', 'random_world_rotation', 'random_world_scaling']
        AUG_CONFIG_LIST: []

    DATA_PROCESSOR:
        - NAME: mask_points_and_boxes_outside_range
          REMOVE_OUTSIDE_BOXES: True

        - NAME: shuffle_points
          SHUFFLE_ENABLED: {
            'train': False,
            'test': False
          }

        - NAME: transform_points_to_voxels
          VOXEL_SIZE: [0.2, 0.2, 0.4]  # 大幅增大voxel尺寸
          MAX_POINTS_PER_VOXEL: 2      # 最少点数
          MAX_NUMBER_OF_VOXELS: {
            'train': 16000,
            'test': 2000   # 极少的voxel数量
          }

MODEL:
    NAME: VoxelRCNN

    VFE:
        NAME: MeanVFE

    BACKBONE_3D: 
        NAME: VoxelBackBone8xFocal
        USE_IMG: False      # 完全禁用图像分支
        USE_SAM_ONLINEV2: False  # 禁用SAM
        USE_SAM_FPN: False
        ZGX_CODE: True      # 禁用fusion模块
        USE_WAVE: False     # 禁用小波注意力

    MAP_TO_BEV:
        NAME: HeightCompression
        NUM_BEV_FEATURES: 128  # 减少特征维度

    BACKBONE_2D:
        NAME: BaseBEVBackbone

        LAYER_NUMS: [3, 3]     # 减少层数
        LAYER_STRIDES: [1, 2]
        NUM_FILTERS: [64, 128]  # 减少通道数
        UPSAMPLE_STRIDES: [1, 2]
        NUM_UPSAMPLE_FILTERS: [128, 128]

    DENSE_HEAD:
        NAME: AnchorHeadSingle
        CLASS_AGNOSTIC: False

        USE_DIRECTION_CLASSIFIER: True
        DIR_OFFSET: 0.78539
        DIR_LIMIT_OFFSET: 0.0
        NUM_DIR_BINS: 2

        ANCHOR_GENERATOR_CONFIG: [
            {
                'class_name': 'Car',
                'anchor_sizes': [[3.9, 1.6, 1.56]],
                'anchor_rotations': [0, 1.57],
                'anchor_bottom_heights': [-1.78],
                'align_center': False,
                'feature_map_stride': 8,
                'matched_threshold': 0.6,
                'unmatched_threshold': 0.45
            }
        ]

        TARGET_ASSIGNER_CONFIG:
            NAME: AxisAlignedTargetAssigner
            POS_FRACTION: -1.0
            SAMPLE_SIZE: 256  # 减少样本数
            NORM_BY_NUM_EXAMPLES: False
            MATCH_HEIGHT: False
            BOX_CODER: ResidualCoder

        LOSS_CONFIG:
            LOSS_WEIGHTS: {
                'cls_weight': 1.0,
                'loc_weight': 2.0,
                'dir_weight': 0.2,
                'code_weights': [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
            }

    ROI_HEAD:
        NAME: VoxelRCNNHead
        CLASS_AGNOSTIC: True

        SHARED_FC: [128, 128]  # 减少FC层大小
        CLS_FC: [128, 128]
        REG_FC: [128, 128]
        DP_RATIO: 0.3

        NMS_CONFIG:
            TRAIN:
                NMS_TYPE: nms_gpu
                MULTI_CLASSES_NMS: False
                NMS_PRE_MAXSIZE: 4000  # 减少
                NMS_POST_MAXSIZE: 256  # 减少
                NMS_THRESH: 0.8
            TEST:
                NMS_TYPE: nms_gpu
                MULTI_CLASSES_NMS: False
                USE_FAST_NMS: False
                SCORE_THRESH: 0.0
                NMS_PRE_MAXSIZE: 1024  # 减少
                NMS_POST_MAXSIZE: 50   # 减少
                NMS_THRESH: 0.7

        ROI_GRID_POOL:
            FEATURES_SOURCE: ['x_conv2']  # 只使用一个特征层
            PRE_MLP: True
            GRID_SIZE: 4  # 减少grid size
            POOL_LAYERS:
                x_conv2:
                    MLPS: [[16, 16]]  # 减少MLP大小
                    QUERY_RANGES: [[2, 2, 2]]  # 减少查询范围
                    POOL_RADIUS: [0.4]
                    NSAMPLE: [8]  # 减少采样点数
                    POOL_METHOD: max_pool

        TARGET_CONFIG:
            BOX_CODER: ResidualCoder
            ROI_PER_IMAGE: 64  # 减少ROI数量
            FG_RATIO: 0.5

            SAMPLE_ROI_BY_EACH_CLASS: True
            CLS_SCORE_TYPE: roi_iou

            CLS_FG_THRESH: 0.75
            CLS_BG_THRESH: 0.25
            CLS_BG_THRESH_LO: 0.1
            HARD_BG_RATIO: 0.8

            REG_FG_THRESH: 0.55

        LOSS_CONFIG:
            CLS_LOSS: BinaryCrossEntropy
            REG_LOSS: smooth-l1
            CORNER_LOSS_REGULARIZATION: True
            LOSS_WEIGHTS: {
                'rcnn_cls_weight': 1.0,
                'rcnn_reg_weight': 1.0,
                'rcnn_corner_weight': 1.0,
                'code_weights': [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
            }

    POST_PROCESSING:
        RECALL_THRESH_LIST: [0.3, 0.5, 0.7]
        SCORE_THRESH: 0.1
        OUTPUT_RAW_SCORE: False

        EVAL_METRIC: kitti

        # 测试模式配置
        TEST_MODE: True

        NMS_CONFIG:
            MULTI_CLASSES_NMS: False
            NMS_TYPE: nms_gpu
            NMS_THRESH: 0.1
            NMS_PRE_MAXSIZE: 2048  # 减少
            NMS_POST_MAXSIZE: 100  # 减少


OPTIMIZATION:
    BATCH_SIZE_PER_GPU: 1  # 测试时使用batch size 1
    NUM_EPOCHS: 80

    OPTIMIZER: adam_onecycle
    LR: 0.005
    WEIGHT_DECAY: 0.01
    MOMENTUM: 0.9

    MOMS: [0.95, 0.85]
    PCT_START: 0.4
    DIV_FACTOR: 10
    DECAY_STEP_LIST: [35, 45]
    LR_DECAY: 0.1
    LR_CLIP: 0.0000001

    LR_WARMUP: False
    WARMUP_EPOCH: 1

    GRAD_NORM_CLIP: 10
