---
description: Explore the Ultralytics nn modules pages on Transformer and MLP blocks, LayerNorm2d, and Deformable Transformer Decoder Layer.
keywords: Ultralytics, NN Modules, TransformerEncoderLayer, TransformerLayer, M<PERSON>Block, LayerNorm2d, DeformableTransformerDecoderLayer, examples, code snippets, tutorials
---

## TransformerEncoderLayer
---
### ::: ultralytics.nn.modules.transformer.TransformerEncoderLayer
<br><br>

## AIFI
---
### ::: ultralytics.nn.modules.transformer.AIFI
<br><br>

## TransformerLayer
---
### ::: ultralytics.nn.modules.transformer.TransformerLayer
<br><br>

## TransformerBlock
---
### ::: ultralytics.nn.modules.transformer.TransformerBlock
<br><br>

## MLPBlock
---
### ::: ultralytics.nn.modules.transformer.MLPBlock
<br><br>

## MLP
---
### ::: ultralytics.nn.modules.transformer.MLP
<br><br>

## LayerNorm2d
---
### ::: ultralytics.nn.modules.transformer.LayerNorm2d
<br><br>

## MSDeformAttn
---
### ::: ultralytics.nn.modules.transformer.MSDeformAttn
<br><br>

## DeformableTransformerDecoderLayer
---
### ::: ultralytics.nn.modules.transformer.DeformableTransformerDecoderLayer
<br><br>

## DeformableTransformerDecoder
---
### ::: ultralytics.nn.modules.transformer.DeformableTransformerDecoder
<br><br>
