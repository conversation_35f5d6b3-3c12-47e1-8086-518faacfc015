#!/usr/bin/env python3
"""
专门计算mAP的KITTI-C测试脚本
参考samtransfusion/tools/test.py的评估方式
"""

import _init_path
import argparse
import datetime
import os
import re
import time
import gc
import torch
import pickle
import json
from pathlib import Path

import numpy as np

from eval_utils import eval_utils
from pcdet.config import cfg, cfg_from_yaml_file, log_config_to_file
from pcdet.datasets import build_dataloader
from pcdet.models import build_network
from pcdet.utils import common_utils


def parse_config():
    parser = argparse.ArgumentParser(description='KITTI-C test with mAP calculation')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml',
                       help='specify the config for testing')
    parser.add_argument('--ckpt', type=str, 
                       default='../output/kitti_models/sam_onlinev3/robofusion_tmux/ckpt/checkpoint_epoch_80.pth',
                       help='checkpoint to start from')
    parser.add_argument('--corruption', type=str, default='fog',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type to test')
    parser.add_argument('--batch_size', type=int, default=1, help='batch size for testing')
    parser.add_argument('--workers', type=int, default=1, help='number of workers for dataloader')
    
    args = parser.parse_args()
    
    # 加载配置文件
    cfg_from_yaml_file(args.cfg_file, cfg)
    
    # 更新配置以使用对应的info文件
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption
    
    # 设置标签
    cfg.TAG = f"sam_onlinev3_kitti_c_{args.corruption}_mAP"
    cfg.EXP_GROUP_PATH = 'kitti_models'
    
    np.random.seed(1024)
    
    return args, cfg


def run_inference(model, test_loader, logger):
    """运行推理获取检测结果"""
    
    logger.info('开始推理...')
    model.eval()
    
    results = []
    dataset = test_loader.dataset
    prog_bar = mmcv.ProgressBar(len(dataset)) if 'mmcv' in globals() else None
    
    for i, data in enumerate(test_loader):
        with torch.no_grad():
            result = model(return_loss=False, rescale=True, **data)
        
        # 转换结果格式
        if isinstance(result, list):
            results.extend(result)
        else:
            results.append(result)
        
        if prog_bar:
            prog_bar.update()
        
        if (i + 1) % 100 == 0:
            logger.info(f'已处理 {i + 1}/{len(test_loader)} 批次')
    
    logger.info(f'推理完成，共获得 {len(results)} 个结果')
    return results


def calculate_mAP(results, test_set, logger, output_dir):
    """计算mAP"""
    
    logger.info('开始计算mAP...')
    
    try:
        # 调用数据集的evaluation方法
        eval_results = test_set.evaluation(results, test_set.CLASSES)
        
        if isinstance(eval_results, tuple):
            ap_result_str, ap_dict = eval_results
        else:
            ap_result_str = str(eval_results)
            ap_dict = eval_results if isinstance(eval_results, dict) else {}
        
        logger.info('mAP计算完成')
        logger.info('评估结果:')
        logger.info('\n' + ap_result_str)
        
        # 保存结果
        mAP_result_path = output_dir / 'mAP_results.json'
        with open(mAP_result_path, 'w') as f:
            json.dump(ap_dict, f, indent=2)
        
        # 保存文本结果
        mAP_text_path = output_dir / 'mAP_results.txt'
        with open(mAP_text_path, 'w') as f:
            f.write(ap_result_str)
        
        logger.info(f'mAP结果已保存到: {mAP_result_path}')
        
        return ap_result_str, ap_dict
        
    except Exception as e:
        logger.error(f'mAP计算失败: {e}')
        import traceback
        traceback.print_exc()
        return None, None


def main():
    args, cfg = parse_config()
    
    print("🚀 KITTI-C mAP测试")
    print(f"📂 腐败类型: {args.corruption}")
    print(f"📂 数据路径: {cfg.DATA_CONFIG.DATA_PATH}")
    print(f"📂 Info文件: {cfg.DATA_CONFIG.INFO_PATH['test']}")
    print(f"📂 模型文件: {args.ckpt}")
    
    # 检查info文件是否存在
    info_file_path = Path(cfg.DATA_CONFIG.DATA_PATH) / f'kitti_c_infos_val_{args.corruption}.pkl'
    if not info_file_path.exists():
        print(f"❌ Info文件不存在: {info_file_path}")
        print("请先运行 fix_kitti_c_infos.py 生成包含标注的info文件")
        return
    else:
        print(f"✅ Info文件存在: {info_file_path}")
    
    # 创建输出目录
    output_dir = cfg.ROOT_DIR / 'output' / cfg.EXP_GROUP_PATH / cfg.TAG / 'mAP_test'
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    log_file = output_dir / f'log_mAP_{args.corruption}_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    logger = common_utils.create_logger(log_file, rank=cfg.LOCAL_RANK)
    
    logger.info('**********************开始mAP测试**********************')
    logger.info(f'腐败类型: {args.corruption}')
    logger.info(f'数据路径: {cfg.DATA_CONFIG.DATA_PATH}')
    logger.info(f'Info文件: {cfg.DATA_CONFIG.INFO_PATH["test"]}')
    logger.info(f'模型文件: {args.ckpt}')
    
    try:
        # 构建数据集
        print("📊 构建数据集...")
        test_set, test_loader, _ = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=args.batch_size,
            dist=False, workers=args.workers, logger=logger, training=False
        )
        
        logger.info(f'数据集样本数量: {len(test_set)}')
        print(f"✅ 数据集构建成功，样本数量: {len(test_set)}")
        
        # 构建模型
        print("🤖 构建模型...")
        model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=test_set)
        
        # 加载checkpoint
        print("📥 加载模型权重...")
        model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=False)
        model = model.cuda()
        
        # 清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        # 运行推理
        print(f"🧪 开始推理 {args.corruption.upper()}...")
        logger.info(f'*************** 开始推理 {args.corruption.upper()} *****************')
        
        # 使用eval_utils进行推理
        with torch.no_grad():
            eval_utils.eval_one_epoch(
                cfg, args, model, test_loader, 0, logger, dist_test=False,
                result_dir=output_dir, save_to_file=True
            )
        
        # 加载推理结果
        result_pkl_path = output_dir / 'result.pkl'
        if result_pkl_path.exists():
            print("📊 加载推理结果...")
            with open(result_pkl_path, 'rb') as f:
                results = pickle.load(f)
            
            logger.info(f'加载了 {len(results)} 个推理结果')
            
            # 计算mAP
            print("📈 计算mAP...")
            ap_result_str, ap_dict = calculate_mAP(results, test_set, logger, output_dir)
            
            if ap_result_str:
                print(f"\n🎉 {args.corruption.upper()} mAP计算完成!")
                print("📊 评估结果:")
                print(ap_result_str)
                
                # 提取关键指标
                if ap_dict:
                    print("\n📋 关键指标:")
                    for key, value in ap_dict.items():
                        if isinstance(value, (int, float)):
                            print(f"  {key}: {value:.4f}")
            else:
                print("❌ mAP计算失败")
        else:
            print(f"❌ 未找到推理结果文件: {result_pkl_path}")
        
        print(f"\n📂 所有结果保存在: {output_dir}")
        print(f"📄 日志文件: {log_file}")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
