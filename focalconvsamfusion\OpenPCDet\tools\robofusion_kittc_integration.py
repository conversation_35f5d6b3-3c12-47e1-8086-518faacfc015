# RoboFusion 集成 KITTI-C 测试集完整实现

import os
import pickle
import numpy as np
import torch
from pathlib import Path
from tqdm import tqdm
import yaml

# Step 1: 修改 RoboFusion 的数据集类
# 文件路径: RoboFusion/pcdet/datasets/kitti/kitti_dataset.py

class KITTIDataset(DatasetTemplate):
    """修改现有的 KITTI 数据集类以支持 KITTI-C"""
    
    def __init__(self, dataset_cfg, class_names, training=True, root_path=None, logger=None, ext='.bin'):
        super().__init__(
            dataset_cfg=dataset_cfg, class_names=class_names, training=training, root_path=root_path, logger=logger
        )
        
        # 添加 KITTI-C 相关配置
        self.is_kitti_c = dataset_cfg.get('IS_KITTI_C', False)
        if self.is_kitti_c:
            self.corruption_type = dataset_cfg.get('CORRUPTION_TYPE', 'fog')
            self.severity = dataset_cfg.get('SEVERITY', 3)
            self._setup_kitti_c_paths()
            
    def _setup_kitti_c_paths(self):
        """设置 KITTI-C 特定的路径"""
        # 修改点云和图像路径以指向损坏数据
        corruption_dir = f"{self.corruption_type}/severity_{self.severity}"
        
        self.lidar_path = self.root_path / corruption_dir / 'velodyne'
        self.image_path = self.root_path / corruption_dir / 'image_2'
        
        # 标定和标签路径保持不变（使用原始的）
        self.calib_path = self.root_path / 'calib'
        self.label_path = self.root_path / 'label_2'
        
    def get_lidar(self, idx):
        """重写获取激光雷达数据的方法"""
        if self.is_kitti_c:
            lidar_file = self.lidar_path / f'{idx}.bin'
        else:
            lidar_file = self.root_path / 'training' / 'velodyne' / f'{idx}.bin'
            
        assert lidar_file.exists(), f'Lidar file not found: {lidar_file}'
        return np.fromfile(str(lidar_file), dtype=np.float32).reshape(-1, 4)

# Step 2: 创建 KITTI-C 专用配置文件
# 文件路径: RoboFusion/tools/cfgs/dataset_configs/kitti_c_dataset.yaml

kitti_c_config = """
DATASET: 'KittiDataset'
DATA_PATH: '/path/to/your/kitti_c'

# KITTI-C 特定配置
IS_KITTI_C: True
CORRUPTION_TYPE: 'fog'  # 可选: fog, rain, snow, motion_blur等
SEVERITY: 3  # 1-5

# 点云范围 [x_min, y_min, z_min, x_max, y_max, z_max]
POINT_CLOUD_RANGE: [0, -40, -3, 70.4, 40, 1]

# 体素大小
VOXEL_SIZE: [0.05, 0.05, 0.1]
MAX_POINTS_PER_VOXEL: 5
MAX_NUMBER_OF_VOXELS: {
    'train': 16000,
    'test': 40000
}

# 数据处理
DATA_PROCESSOR:
    - NAME: mask_points_and_boxes_outside_range
      REMOVE_OUTSIDE_BOXES: True

    - NAME: shuffle_points
      SHUFFLE_ENABLED: {
        'train': True,
        'test': False
      }

    - NAME: transform_points_to_voxels
      VOXEL_SIZE: [0.05, 0.05, 0.1]
      MAX_POINTS_PER_VOXEL: 5
      MAX_NUMBER_OF_VOXELS: {
        'train': 16000,
        'test': 40000
      }

# 测试时不使用数据增强
DATA_AUGMENTOR:
    DISABLE_AUG_LIST: ['placeholder']

# 点云特征编码
POINT_FEATURE_ENCODING: {
    encoding_type: absolute_coordinates_encoding,
    used_feature_list: ['x', 'y', 'z', 'intensity'],
    src_feature_list: ['x', 'y', 'z', 'intensity'],
}

# 评估配置
EVAL_METRIC: kitti
"""

# Step 3: 创建测试脚本
# 文件路径: RoboFusion/tools/test_kitti_c.py

import argparse
import datetime
import glob
import os
from pathlib import Path

import torch
import torch.nn as nn
from tensorboardX import SummaryWriter

from pcdet.config import cfg, cfg_from_list, cfg_from_yaml_file, log_config_to_file
from pcdet.datasets import build_dataloader
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils
from pcdet.datasets.kitti.kitti_object_eval_python import eval as kitti_eval

def parse_config():
    parser = argparse.ArgumentParser(description='Testing RoboFusion on KITTI-C')
    parser.add_argument('--cfg_file', type=str, default='cfgs/kitti_models/robofusion.yaml',
                        help='specify the config file')
    parser.add_argument('--batch_size', type=int, default=1, help='batch size for testing')
    parser.add_argument('--ckpt', type=str, default=None, help='checkpoint to test')
    parser.add_argument('--corruption_type', type=str, default='fog', 
                        help='corruption type: fog, rain, snow, etc.')
    parser.add_argument('--severity', type=int, default=3, 
                        help='corruption severity: 1-5')
    parser.add_argument('--save_results', action='store_true', default=False,
                        help='save detection results')
    
    args = parser.parse_args()
    
    # 加载配置
    cfg_from_yaml_file(args.cfg_file, cfg)
    
    # 设置 KITTI-C 相关参数
    cfg.DATA_CONFIG.IS_KITTI_C = True
    cfg.DATA_CONFIG.CORRUPTION_TYPE = args.corruption_type
    cfg.DATA_CONFIG.SEVERITY = args.severity
    
    return args, cfg

def test_single_corruption(model, test_loader, args, cfg):
    """测试单个损坏条件"""
    model.eval()
    
    dataset = test_loader.dataset
    class_names = dataset.class_names
    det_annos = []
    
    print(f'\nTesting {args.corruption_type} with severity {args.severity}...')
    print(f'Total samples: {len(dataset)}')
    
    progress_bar = tqdm(total=len(test_loader), desc='Testing', dynamic_ncols=True)
    
    with torch.no_grad():
        for i, batch_dict in enumerate(test_loader):
            load_data_to_gpu(batch_dict)
            
            # 前向传播
            pred_dicts, _ = model.forward(batch_dict)
            
            # 后处理
            annos = dataset.generate_prediction_dicts(
                batch_dict, pred_dicts, class_names,
                output_path=None if not args.save_results else Path('./output')
            )
            det_annos += annos
            
            progress_bar.update()
    
    progress_bar.close()
    
    # 评估结果
    result_str, result_dict = dataset.evaluation(
        det_annos, class_names,
        eval_metric=cfg.DATA_CONFIG.get('EVAL_METRIC', 'kitti')
    )
    
    return result_str, result_dict

def test_all_corruptions(model, cfg, args):
    """测试所有损坏条件"""
    corruption_types = ['fog', 'rain', 'snow', 'motion_blur', 
                       'beam_drop', 'crosstalk', 'incomplete_echo']
    severities = [1, 2, 3, 4, 5]
    
    all_results = {}
    
    for corruption in corruption_types:
        all_results[corruption] = {}
        
        for severity in severities:
            # 更新配置
            cfg.DATA_CONFIG.CORRUPTION_TYPE = corruption
            cfg.DATA_CONFIG.SEVERITY = severity
            
            # 创建数据加载器
            test_set, test_loader, _ = build_dataloader(
                dataset_cfg=cfg.DATA_CONFIG,
                class_names=cfg.CLASS_NAMES,
                batch_size=args.batch_size,
                dist=False,
                workers=4,
                logger=None,
                training=False
            )
            
            # 测试
            result_str, result_dict = test_single_corruption(
                model, test_loader, args, cfg
            )
            
            all_results[corruption][severity] = result_dict
            
            # 打印结果
            print(f"\n{corruption} - Severity {severity}:")
            print(result_str)
    
    return all_results

def main():
    args, cfg = parse_config()
    
    # 创建输出目录
    output_dir = Path('./output') / f'kitti_c_{datetime.datetime.now().strftime("%Y%m%d-%H%M%S")}'
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 构建模型
    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=None)
    model.cuda()
    
    # 加载权重
    if args.ckpt is not None:
        model.load_params_from_file(filename=args.ckpt, logger=None, to_cpu=False)
        print(f"Loaded checkpoint from: {args.ckpt}")
    
    # 如果指定了具体的损坏类型，只测试该类型
    if hasattr(args, 'test_all') and args.test_all:
        results = test_all_corruptions(model, cfg, args)
        
        # 保存所有结果
        import json
        with open(output_dir / 'all_results.json', 'w') as f:
            json.dump(results, f, indent=4)
    else:
        # 创建测试数据加载器
        test_set, test_loader, sampler = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=args.batch_size,
            dist=False,
            workers=4,
            logger=None,
            training=False
        )
        
        # 测试单个损坏条件
        result_str, result_dict = test_single_corruption(
            model, test_loader, args, cfg
        )
        
        # 保存结果
        with open(output_dir / f'{args.corruption_type}_s{args.severity}_result.txt', 'w') as f:
            f.write(result_str)
        
        print("\nFinal Results:")
        print(result_str)

if __name__ == '__main__':
    main()

# Step 4: 批量测试脚本
# 文件路径: RoboFusion/scripts/test_kitti_c_all.sh

batch_test_script = """#!/bin/bash
# 批量测试所有 KITTI-C 损坏条件

CUDA_VISIBLE_DEVICES=0

# 配置路径
CFG_FILE="tools/cfgs/kitti_models/robofusion.yaml"
CKPT="/path/to/your/robofusion_checkpoint.pth"
BATCH_SIZE=4

# 损坏类型和严重程度
CORRUPTIONS=("fog" "rain" "snow" "motion_blur" "beam_drop" "crosstalk" "incomplete_echo")
SEVERITIES=(1 2 3 4 5)

# 创建结果目录
RESULT_DIR="output/kitti_c_results_$(date +%Y%m%d_%H%M%S)"
mkdir -p $RESULT_DIR

# 测试循环
for corruption in "${CORRUPTIONS[@]}"; do
    for severity in "${SEVERITIES[@]}"; do
        echo "Testing $corruption with severity $severity..."
        
        python tools/test_kitti_c.py \\
            --cfg_file $CFG_FILE \\
            --batch_size $BATCH_SIZE \\
            --ckpt $CKPT \\
            --corruption_type $corruption \\
            --severity $severity \\
            --save_results \\
            2>&1 | tee "$RESULT_DIR/${corruption}_s${severity}.log"
            
        # 移动结果文件
        mv output/result.pkl "$RESULT_DIR/${corruption}_s${severity}_result.pkl" 2>/dev/null
    done
done

echo "All tests completed! Results saved in $RESULT_DIR"

# 生成汇总报告
python tools/analysis/summarize_kitti_c_results.py --result_dir $RESULT_DIR
"""

# Step 5: 结果分析脚本
# 文件路径: RoboFusion/tools/analysis/analyze_kitti_c.py

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import pandas as pd

def analyze_robustness(result_dir: str):
    """分析 RoboFusion 在 KITTI-C 上的鲁棒性"""
    
    result_path = Path(result_dir)
    
    # 损坏类型和严重程度
    corruptions = ['fog', 'rain', 'snow', 'motion_blur', 
                  'beam_drop', 'crosstalk', 'incomplete_echo']
    severities = [1, 2, 3, 4, 5]
    
    # 收集所有结果
    results = {}
    for corruption in corruptions:
        results[corruption] = {}
        for severity in severities:
            # 读取结果文件
            result_file = result_path / f'{corruption}_s{severity}_result.pkl'
            if result_file.exists():
                with open(result_file, 'rb') as f:
                    data = pickle.load(f)
                    # 提取 mAP
                    results[corruption][severity] = extract_map(data)
    
    # 创建结果矩阵
    result_matrix = np.zeros((len(corruptions), len(severities)))
    for i, corruption in enumerate(corruptions):
        for j, severity in enumerate(severities):
            if severity in results[corruption]:
                result_matrix[i, j] = results[corruption][severity]
    
    # 绘制热力图
    plt.figure(figsize=(10, 8))
    sns.heatmap(result_matrix, 
                annot=True, 
                fmt='.1f',
                xticklabels=severities,
                yticklabels=corruptions,
                cmap='RdYlGn',
                vmin=0, vmax=100)
    plt.xlabel('Severity Level')
    plt.ylabel('Corruption Type')
    plt.title('RoboFusion Performance on KITTI-C (mAP %)')
    plt.tight_layout()
    plt.savefig(result_path / 'robustness_heatmap.png', dpi=300)
    
    # 计算平均鲁棒性指标
    avg_performance = {}
    for corruption in corruptions:
        perfs = [results[corruption].get(s, 0) for s in severities]
        avg_performance[corruption] = np.mean(perfs)
    
    # 绘制条形图
    plt.figure(figsize=(10, 6))
    corruption_names = [c.replace('_', ' ').title() for c in corruptions]
    plt.bar(corruption_names, list(avg_performance.values()))
    plt.xlabel('Corruption Type')
    plt.ylabel('Average mAP (%)')
    plt.title('Average Performance Across All Severity Levels')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(result_path / 'average_performance.png', dpi=300)
    
    # 保存数值结果
    with open(result_path / 'robustness_metrics.json', 'w') as f:
        json.dump({
            'detailed_results': results,
            'average_performance': avg_performance,
            'overall_robustness': np.mean(list(avg_performance.values()))
        }, f, indent=4)
    
    print(f"Analysis complete! Results saved to {result_path}")
    print(f"Overall Robustness Score: {np.mean(list(avg_performance.values())):.2f}%")

def extract_map(result_dict):
    """从结果字典中提取 mAP"""
    # 根据 RoboFusion 的输出格式调整
    if 'KITTI' in result_dict:
        return result_dict['KITTI']['Car_3D_moderate']
    return 0

if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--result_dir', type=str, required=True)
    args = parser.parse_args()
    
    analyze_robustness(args.result_dir)
