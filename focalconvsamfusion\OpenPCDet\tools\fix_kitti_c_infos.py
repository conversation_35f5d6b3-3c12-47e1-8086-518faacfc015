#!/usr/bin/env python3
"""
修复KITTI-C info文件结构的脚本
解决 KeyError: 'lidar_idx' 问题
"""

import pickle
import numpy as np
from pathlib import Path
import sys


def get_calib_info(calib_path):
    """读取标定信息"""
    calib_info = {}
    
    with open(calib_path, 'r') as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if len(line) == 0 or line[0] == '#':
            continue
        
        key, value = line.split(':', 1)
        calib_info[key] = np.array([float(x) for x in value.split()])
    
    # 重塑矩阵
    if 'P2' in calib_info:
        calib_info['P2'] = calib_info['P2'].reshape(3, 4)
    if 'R0_rect' in calib_info:
        calib_info['R0_rect'] = calib_info['R0_rect'].reshape(3, 3)
    if 'Tr_velo_to_cam' in calib_info:
        calib_info['Tr_velo_to_cam'] = calib_info['Tr_velo_to_cam'].reshape(3, 4)
    
    return calib_info


def get_label_info(label_path):
    """读取标注信息"""
    with open(label_path, 'r') as f:
        lines = f.readlines()
    
    objects = []
    for line in lines:
        line = line.strip()
        if len(line) == 0:
            continue
        
        parts = line.split(' ')
        if len(parts) < 15:
            continue
        
        obj = {
            'name': parts[0],
            'truncated': float(parts[1]),
            'occluded': int(parts[2]),
            'alpha': float(parts[3]),
            'bbox': [float(parts[4]), float(parts[5]), float(parts[6]), float(parts[7])],
            'dimensions': [float(parts[8]), float(parts[9]), float(parts[10])],
            'location': [float(parts[11]), float(parts[12]), float(parts[13])],
            'rotation_y': float(parts[14])
        }
        objects.append(obj)
    
    # 转换为数组格式
    if objects:
        annos = {
            'name': np.array([obj['name'] for obj in objects]),
            'truncated': np.array([obj['truncated'] for obj in objects]),
            'occluded': np.array([obj['occluded'] for obj in objects]),
            'alpha': np.array([obj['alpha'] for obj in objects]),
            'bbox': np.array([obj['bbox'] for obj in objects]),
            'dimensions': np.array([obj['dimensions'] for obj in objects]),
            'location': np.array([obj['location'] for obj in objects]),
            'rotation_y': np.array([obj['rotation_y'] for obj in objects])
        }
        annos['num_points_in_gt'] = np.array([0] * len(objects))
        annos['difficulty'] = np.array([0] * len(objects))
    else:
        # 空标注
        annos = {
            'name': np.array([]),
            'truncated': np.array([]),
            'occluded': np.array([]),
            'alpha': np.array([]),
            'bbox': np.zeros((0, 4)),
            'dimensions': np.zeros((0, 3)),
            'location': np.zeros((0, 3)),
            'rotation_y': np.array([]),
            'num_points_in_gt': np.array([]),
            'difficulty': np.array([])
        }
    
    return annos


def create_correct_info_for_corruption(data_path, corruption_type):
    """为特定腐败类型创建正确结构的info文件"""
    
    corruption_path = data_path / corruption_type
    labels_path = corruption_path / 'labels' / 'val'
    
    if not labels_path.exists():
        print(f"❌ 标注目录不存在: {labels_path}")
        return []
    
    label_files = list(labels_path.glob('*.txt'))
    sample_ids = [f.stem for f in label_files]
    sample_ids.sort()
    
    print(f"🔄 为 {corruption_type} 创建 {len(sample_ids)} 个样本的info...")
    
    kitti_infos = []
    for sample_idx in sample_ids:
        # 查找对应的文件
        image_files = list((corruption_path / 'images' / 'val').glob(f"{sample_idx}_*"))
        velodyne_files = list((corruption_path / 'velodyne' / 'val').glob(f"{sample_idx}_*"))
        
        if not image_files or not velodyne_files:
            print(f"⚠️  样本 {sample_idx} 缺少图像或点云文件")
            continue
        
        # 创建正确结构的info
        info = {
            'point_cloud': {
                'num_features': 4,
                'lidar_idx': sample_idx,  # 关键：使用lidar_idx而不是image_idx
                'velodyne_path': str(velodyne_files[0].relative_to(data_path))
            },
            'image': {
                'image_idx': sample_idx,
                'image_path': str(image_files[0].relative_to(data_path)),
                'image_shape': [375, 1242, 3]  # KITTI标准尺寸
            }
        }
        
        # 标定信息
        calib_path = corruption_path / 'calib' / f"{sample_idx}.txt"
        if calib_path.exists():
            calib_info = get_calib_info(calib_path)
            info.update(calib_info)
        
        # 标注信息
        label_path = labels_path / f"{sample_idx}.txt"
        if label_path.exists():
            annos = get_label_info(label_path)
            info['annos'] = annos
        
        kitti_infos.append(info)
    
    return kitti_infos


def main():
    # 固定路径
    kitti_c_path = Path("/data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C")
    
    print("=" * 80)
    print("🔧 修复KITTI-C info文件结构")
    print("=" * 80)
    print(f"📂 KITTI-C路径: {kitti_c_path}")
    
    if not kitti_c_path.exists():
        print("❌ KITTI-C路径不存在!")
        sys.exit(1)
    
    corruption_types = ['fog', 'snow', 'rain', 'sun']
    
    for corruption in corruption_types:
        print(f"\n🔄 修复 {corruption} info文件...")
        
        # 检查标注文件是否存在
        labels_path = kitti_c_path / corruption / 'labels' / 'val'
        if not labels_path.exists():
            print(f"❌ {corruption} 标注目录不存在: {labels_path}")
            continue
        
        # 创建正确的info文件
        kitti_infos = create_correct_info_for_corruption(kitti_c_path, corruption)
        
        if kitti_infos:
            # 保存修复后的info文件
            output_file = kitti_c_path / f'kitti_c_infos_val_{corruption}.pkl'
            with open(output_file, 'wb') as f:
                pickle.dump(kitti_infos, f)
            
            total_objects = sum(len(info.get('annos', {}).get('name', [])) for info in kitti_infos)
            print(f"  ✅ 保存到: {output_file.name}")
            print(f"  📊 {len(kitti_infos)} 个样本, {total_objects} 个标注对象")
            
            # 验证第一个info的结构
            if kitti_infos:
                first_info = kitti_infos[0]
                print(f"  🔍 验证结构:")
                print(f"    - point_cloud.lidar_idx: {first_info['point_cloud'].get('lidar_idx', 'MISSING')}")
                print(f"    - point_cloud.velodyne_path: {first_info['point_cloud'].get('velodyne_path', 'MISSING')}")
                print(f"    - image.image_idx: {first_info['image'].get('image_idx', 'MISSING')}")
                print(f"    - annos存在: {'annos' in first_info}")
        else:
            print(f"❌ 没有找到有效的样本")
    
    print(f"\n🎉 修复完成！现在可以重新运行测试了。")
    print("=" * 80)


if __name__ == '__main__':
    main()
