---
description: Use Ultralytics YOLO Data Augmentation transforms with Base, MixUp, and Albumentations for object detection and classification.
keywords: YOLO, data augmentation, transforms, BaseTransform, MixUp, RandomHSV, Albumentations, ToTensor, classify_transforms, classify_albumentations
---

## BaseTransform
---
### ::: ultralytics.yolo.data.augment.BaseTransform
<br><br>

## Compose
---
### ::: ultralytics.yolo.data.augment.Compose
<br><br>

## BaseMixTransform
---
### ::: ultralytics.yolo.data.augment.BaseMixTransform
<br><br>

## Mosaic
---
### ::: ultralytics.yolo.data.augment.Mosaic
<br><br>

## MixUp
---
### ::: ultralytics.yolo.data.augment.MixUp
<br><br>

## RandomPerspective
---
### ::: ultralytics.yolo.data.augment.RandomPerspective
<br><br>

## RandomHSV
---
### ::: ultralytics.yolo.data.augment.RandomHSV
<br><br>

## RandomFlip
---
### ::: ultralytics.yolo.data.augment.RandomFlip
<br><br>

## LetterBox
---
### ::: ultralytics.yolo.data.augment.LetterBox
<br><br>

## CopyPaste
---
### ::: ultralytics.yolo.data.augment.CopyPaste
<br><br>

## Albumentations
---
### ::: ultralytics.yolo.data.augment.Albumentations
<br><br>

## Format
---
### ::: ultralytics.yolo.data.augment.Format
<br><br>

## ClassifyLetterBox
---
### ::: ultralytics.yolo.data.augment.ClassifyLetterBox
<br><br>

## CenterCrop
---
### ::: ultralytics.yolo.data.augment.CenterCrop
<br><br>

## ToTensor
---
### ::: ultralytics.yolo.data.augment.ToTensor
<br><br>

## v8_transforms
---
### ::: ultralytics.yolo.data.augment.v8_transforms
<br><br>

## classify_transforms
---
### ::: ultralytics.yolo.data.augment.classify_transforms
<br><br>

## hsv2colorjitter
---
### ::: ultralytics.yolo.data.augment.hsv2colorjitter
<br><br>

## classify_albumentations
---
### ::: ultralytics.yolo.data.augment.classify_albumentations
<br><br>
