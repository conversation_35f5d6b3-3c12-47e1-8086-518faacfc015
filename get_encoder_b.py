import numpy as np
import torch
import matplotlib.pyplot as plt
import cv2
import sys
from segment_anything import sam_model_registry, SamAutomaticMaskGenerator, SamPredictor,image_encoder_registry
from segment_anything.modeling import ImageEncoderViT, MaskDecoder, PromptEncoder, Sam, TwoWayTransformer, packageImageEncoderViT
from functools import partial

sam_checkpoint = "./sam_vit_b_01ec64.pth"
model_type = "vit_b"
# sam_checkpoint = "/data/liufeifei/project/RoboFusion-master/RoboFusion-master/mobile_sam.pt"  # 你的MobileSAM权重文件
# model_type = "vit_t"  # MobileSAM使用的是tiny版本
device = "cuda"
encoder_embed_dim=768
encoder_depth=12
encoder_num_heads=12
encoder_global_attn_indexes=[2, 5, 8, 11]
prompt_embed_dim = 256
image_size = 1024
vit_patch_size = 16
image_embedding_size = image_size // vit_patch_size


sam = Sam(
        image_encoder=ImageEncoderViT(
            depth=encoder_depth,
            embed_dim=encoder_embed_dim,
            img_size=image_size,
            mlp_ratio=4,
            norm_layer=partial(torch.nn.LayerNorm, eps=1e-6),
            num_heads=encoder_num_heads,
            patch_size=vit_patch_size,
            qkv_bias=True,
            use_rel_pos=True,
            global_attn_indexes=encoder_global_attn_indexes,
            window_size=14,
            out_chans=prompt_embed_dim,
        ),
        prompt_encoder=PromptEncoder(
            embed_dim=prompt_embed_dim,
            image_embedding_size=(image_embedding_size, image_embedding_size),
            input_image_size=(image_size, image_size),
            mask_in_chans=16,
        ),
        mask_decoder=MaskDecoder(
            num_multimask_outputs=3,
            transformer=TwoWayTransformer(
                depth=2,
                embedding_dim=prompt_embed_dim,
                mlp_dim=2048,
                num_heads=8,
            ),
            transformer_dim=prompt_embed_dim,
            iou_head_depth=3,
            iou_head_hidden_dim=256,
        ),
        pixel_mean=[123.675, 116.28, 103.53],
        pixel_std=[58.395, 57.12, 57.375],
    )


# print(premodel)
package_image_encoder = packageImageEncoderViT(ImageEncoderViT(
            depth=encoder_depth,
            embed_dim=encoder_embed_dim,
            img_size=image_size,
            mlp_ratio=4,
            norm_layer=partial(torch.nn.LayerNorm, eps=1e-6),
            num_heads=encoder_num_heads,
            patch_size=vit_patch_size,
            qkv_bias=True,
            use_rel_pos=True,
            global_attn_indexes=encoder_global_attn_indexes,
            window_size=14,
            out_chans=prompt_embed_dim,
        ),)



premodel = torch.load(sam_checkpoint)
# sam.load_state_dict(torch.load(sam_checkpoint))

model_image_encoder_dict = package_image_encoder.state_dict()
print(model_image_encoder_dict.keys())
# for key,value in premodel.items():
#    print(key)
state_dict = {k:v for k,v in premodel.items() if k in model_image_encoder_dict.keys()}

print(state_dict.keys())

model_image_encoder_dict.update(state_dict)
package_image_encoder.load_state_dict(model_image_encoder_dict)

torch.save(package_image_encoder.state_dict(), './sam_image_encoder_b.pth')
# print(model_image_encoder)

print("end")