#!/usr/bin/env python3
"""
测试数据集加载的脚本
验证KittiCAdaptedDataset是否能正确工作
"""

import _init_path
import argparse
from pathlib import Path

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import build_dataloader


def test_dataset_loading():
    """测试数据集加载"""
    
    # 加载配置
    cfg_file = 'cfgs/kitti_models/sam_onlinev3_kitti_c_original.yaml'
    cfg_from_yaml_file(cfg_file, cfg)
    
    # 设置腐败类型
    corruption = 'sun'
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = corruption
    
    print(f"🔍 测试数据集加载")
    print(f"📂 配置文件: {cfg_file}")
    print(f"📂 数据集类型: {cfg.DATA_CONFIG.DATASET}")
    print(f"📂 数据路径: {cfg.DATA_CONFIG.DATA_PATH}")
    print(f"📂 腐败类型: {corruption}")
    
    try:
        # 构建数据集
        print("\n📊 构建数据集...")
        test_set, test_loader, _ = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=1,
            dist=False, workers=0, logger=None, training=False  # workers=0 避免多进程问题
        )
        
        print(f"✅ 数据集构建成功")
        print(f"📋 数据集类型: {type(test_set).__name__}")
        print(f"📊 样本数量: {len(test_set)}")
        
        # 检查evaluation方法
        if hasattr(test_set, 'evaluation'):
            print("✅ 数据集有evaluation方法")
        else:
            print("❌ 数据集没有evaluation方法")
        
        # 测试加载第一个样本
        print("\n🧪 测试加载第一个样本...")
        try:
            first_sample = test_set[0]
            print("✅ 成功加载第一个样本")
            print(f"📋 样本字段: {list(first_sample.keys())}")
            
            # 检查关键字段
            if 'points' in first_sample:
                points = first_sample['points']
                print(f"📊 点云形状: {points.shape}")
            
            if 'gt_boxes' in first_sample:
                gt_boxes = first_sample['gt_boxes']
                print(f"📊 GT boxes形状: {gt_boxes.shape}")
            
            if 'gt_names' in first_sample:
                gt_names = first_sample['gt_names']
                print(f"📊 GT类别: {gt_names}")
                
        except Exception as e:
            print(f"❌ 加载第一个样本失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 测试数据加载器
        print("\n🧪 测试数据加载器...")
        try:
            for i, batch_dict in enumerate(test_loader):
                print(f"✅ 成功加载第 {i+1} 个batch")
                print(f"📋 Batch字段: {list(batch_dict.keys())}")
                if i >= 2:  # 只测试前3个batch
                    break
        except Exception as e:
            print(f"❌ 数据加载器测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n🎉 数据集加载测试完全成功！")
        return True
        
    except Exception as e:
        print(f"❌ 数据集构建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = test_dataset_loading()
    if success:
        print("\n✅ 数据集测试通过，可以进行正式测试")
    else:
        print("\n❌ 数据集测试失败，需要修复问题")
