---
description: Learn about <PERSON><PERSON><PERSON>'s callback functions from on_train_start to add_integration_callbacks. See how these callbacks modify and save models.
keywords: YOLO, Ultralytics, callbacks, object detection, training, inference
---

## on_pretrain_routine_start
---
### ::: ultralytics.yolo.utils.callbacks.base.on_pretrain_routine_start
<br><br>

## on_pretrain_routine_end
---
### ::: ultralytics.yolo.utils.callbacks.base.on_pretrain_routine_end
<br><br>

## on_train_start
---
### ::: ultralytics.yolo.utils.callbacks.base.on_train_start
<br><br>

## on_train_epoch_start
---
### ::: ultralytics.yolo.utils.callbacks.base.on_train_epoch_start
<br><br>

## on_train_batch_start
---
### ::: ultralytics.yolo.utils.callbacks.base.on_train_batch_start
<br><br>

## optimizer_step
---
### ::: ultralytics.yolo.utils.callbacks.base.optimizer_step
<br><br>

## on_before_zero_grad
---
### ::: ultralytics.yolo.utils.callbacks.base.on_before_zero_grad
<br><br>

## on_train_batch_end
---
### ::: ultralytics.yolo.utils.callbacks.base.on_train_batch_end
<br><br>

## on_train_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.base.on_train_epoch_end
<br><br>

## on_fit_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.base.on_fit_epoch_end
<br><br>

## on_model_save
---
### ::: ultralytics.yolo.utils.callbacks.base.on_model_save
<br><br>

## on_train_end
---
### ::: ultralytics.yolo.utils.callbacks.base.on_train_end
<br><br>

## on_params_update
---
### ::: ultralytics.yolo.utils.callbacks.base.on_params_update
<br><br>

## teardown
---
### ::: ultralytics.yolo.utils.callbacks.base.teardown
<br><br>

## on_val_start
---
### ::: ultralytics.yolo.utils.callbacks.base.on_val_start
<br><br>

## on_val_batch_start
---
### ::: ultralytics.yolo.utils.callbacks.base.on_val_batch_start
<br><br>

## on_val_batch_end
---
### ::: ultralytics.yolo.utils.callbacks.base.on_val_batch_end
<br><br>

## on_val_end
---
### ::: ultralytics.yolo.utils.callbacks.base.on_val_end
<br><br>

## on_predict_start
---
### ::: ultralytics.yolo.utils.callbacks.base.on_predict_start
<br><br>

## on_predict_batch_start
---
### ::: ultralytics.yolo.utils.callbacks.base.on_predict_batch_start
<br><br>

## on_predict_batch_end
---
### ::: ultralytics.yolo.utils.callbacks.base.on_predict_batch_end
<br><br>

## on_predict_postprocess_end
---
### ::: ultralytics.yolo.utils.callbacks.base.on_predict_postprocess_end
<br><br>

## on_predict_end
---
### ::: ultralytics.yolo.utils.callbacks.base.on_predict_end
<br><br>

## on_export_start
---
### ::: ultralytics.yolo.utils.callbacks.base.on_export_start
<br><br>

## on_export_end
---
### ::: ultralytics.yolo.utils.callbacks.base.on_export_end
<br><br>

## get_default_callbacks
---
### ::: ultralytics.yolo.utils.callbacks.base.get_default_callbacks
<br><br>

## add_integration_callbacks
---
### ::: ultralytics.yolo.utils.callbacks.base.add_integration_callbacks
<br><br>
