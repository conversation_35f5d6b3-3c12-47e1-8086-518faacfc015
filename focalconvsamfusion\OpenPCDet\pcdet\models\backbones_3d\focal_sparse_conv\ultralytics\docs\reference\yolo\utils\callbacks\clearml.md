---
description: Improve your YOLOv5 model training with callbacks from ClearML. Learn about log debug samples, pre-training routines, validation and more.
keywords: Ultralytics YOLO, callbacks, log plots, epoch monitoring, training end events
---

## _log_debug_samples
---
### ::: ultralytics.yolo.utils.callbacks.clearml._log_debug_samples
<br><br>

## _log_plot
---
### ::: ultralytics.yolo.utils.callbacks.clearml._log_plot
<br><br>

## on_pretrain_routine_start
---
### ::: ultralytics.yolo.utils.callbacks.clearml.on_pretrain_routine_start
<br><br>

## on_train_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.clearml.on_train_epoch_end
<br><br>

## on_fit_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.clearml.on_fit_epoch_end
<br><br>

## on_val_end
---
### ::: ultralytics.yolo.utils.callbacks.clearml.on_val_end
<br><br>

## on_train_end
---
### ::: ultralytics.yolo.utils.callbacks.clearml.on_train_end
<br><br>
