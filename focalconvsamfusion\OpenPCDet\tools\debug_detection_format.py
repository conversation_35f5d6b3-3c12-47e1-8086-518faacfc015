#!/usr/bin/env python3
"""
调试检测结果格式的脚本
检查检测结果和GT标注的格式是否匹配
"""

import _init_path
import argparse
import pickle
import numpy as np
from pathlib import Path

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import build_dataloader


def parse_args():
    parser = argparse.ArgumentParser(description='Debug detection format')
    parser.add_argument('--result_file', type=str, required=True,
                       help='path to result.pkl file')
    parser.add_argument('--corruption', type=str, default='sun',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml',
                       help='config file')
    
    return parser.parse_args()


def analyze_detection_format(det_annos, sample_count=3):
    """分析检测结果格式"""
    print(f"\n🔍 检测结果格式分析:")
    print(f"  总样本数: {len(det_annos)}")
    
    if not det_annos:
        print("  ❌ 检测结果为空")
        return
    
    # 分析前几个样本
    for i in range(min(sample_count, len(det_annos))):
        det = det_annos[i]
        print(f"\n  样本 {i}:")
        print(f"    类型: {type(det)}")
        
        if isinstance(det, dict):
            print(f"    字段: {list(det.keys())}")
            
            # 检查关键字段
            key_fields = ['name', 'score', 'boxes_lidar', 'location', 'dimensions', 'rotation_y']
            for field in key_fields:
                if field in det:
                    value = det[field]
                    if isinstance(value, np.ndarray):
                        print(f"    {field}: shape={value.shape}, dtype={value.dtype}")
                        if len(value) > 0:
                            print(f"      示例: {value[0] if value.ndim == 1 else value[0]}")
                    else:
                        print(f"    {field}: {type(value)}, len={len(value) if hasattr(value, '__len__') else 'N/A'}")
                else:
                    print(f"    {field}: ❌ 缺失")
        else:
            print(f"    内容: {det}")


def analyze_gt_format(gt_annos, sample_count=3):
    """分析GT标注格式"""
    print(f"\n🔍 GT标注格式分析:")
    print(f"  总样本数: {len(gt_annos)}")
    
    if not gt_annos:
        print("  ❌ GT标注为空")
        return
    
    # 分析前几个样本
    for i in range(min(sample_count, len(gt_annos))):
        gt = gt_annos[i]
        print(f"\n  样本 {i}:")
        print(f"    类型: {type(gt)}")
        
        if isinstance(gt, dict):
            print(f"    字段: {list(gt.keys())}")
            
            # 检查关键字段
            key_fields = ['name', 'location', 'dimensions', 'rotation_y', 'difficulty', 'bbox']
            for field in key_fields:
                if field in gt:
                    value = gt[field]
                    if isinstance(value, np.ndarray):
                        print(f"    {field}: shape={value.shape}, dtype={value.dtype}")
                        if len(value) > 0:
                            print(f"      示例: {value[0] if value.ndim == 1 else value[0]}")
                    else:
                        print(f"    {field}: {type(value)}, len={len(value) if hasattr(value, '__len__') else 'N/A'}")
                else:
                    print(f"    {field}: ❌ 缺失")
        else:
            print(f"    内容: {gt}")


def check_coordinate_system(det_annos, gt_annos):
    """检查坐标系是否匹配"""
    print(f"\n🔍 坐标系检查:")
    
    # 检查检测结果的坐标范围
    if det_annos and len(det_annos) > 0:
        det_sample = det_annos[0]
        if isinstance(det_sample, dict) and 'location' in det_sample:
            det_locations = det_sample['location']
            if isinstance(det_locations, np.ndarray) and len(det_locations) > 0:
                print(f"  检测结果location范围:")
                print(f"    X: [{det_locations[:, 0].min():.2f}, {det_locations[:, 0].max():.2f}]")
                print(f"    Y: [{det_locations[:, 1].min():.2f}, {det_locations[:, 1].max():.2f}]")
                print(f"    Z: [{det_locations[:, 2].min():.2f}, {det_locations[:, 2].max():.2f}]")
    
    # 检查GT的坐标范围
    if gt_annos and len(gt_annos) > 0:
        gt_sample = gt_annos[0]
        if isinstance(gt_sample, dict) and 'location' in gt_sample:
            gt_locations = gt_sample['location']
            if isinstance(gt_locations, np.ndarray) and len(gt_locations) > 0:
                print(f"  GT标注location范围:")
                print(f"    X: [{gt_locations[:, 0].min():.2f}, {gt_locations[:, 0].max():.2f}]")
                print(f"    Y: [{gt_locations[:, 1].min():.2f}, {gt_locations[:, 1].max():.2f}]")
                print(f"    Z: [{gt_locations[:, 2].min():.2f}, {gt_locations[:, 2].max():.2f}]")


def main():
    args = parse_args()
    
    print("🔍 检测结果格式调试")
    print(f"📂 结果文件: {args.result_file}")
    print(f"📂 腐败类型: {args.corruption}")
    
    # 检查结果文件是否存在
    result_file = Path(args.result_file)
    if not result_file.exists():
        print(f"❌ 结果文件不存在: {result_file}")
        return
    
    # 加载配置
    cfg_from_yaml_file(args.cfg_file, cfg)
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption
    
    try:
        # 构建数据集
        print("📊 构建数据集...")
        test_set, _, _ = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=1,
            dist=False, workers=1, logger=None, training=False
        )
        
        print(f"✅ 数据集构建成功，样本数量: {len(test_set)}")
        
        # 加载检测结果
        print("📥 加载检测结果...")
        with open(result_file, 'rb') as f:
            det_annos = pickle.load(f)
        
        print(f"✅ 加载了 {len(det_annos)} 个检测结果")
        
        # 提取GT标注
        print("📋 提取GT标注...")
        gt_annos = []
        for info in test_set.kitti_infos:
            gt_annos.append(info['annos'])
        
        print(f"✅ 提取了 {len(gt_annos)} 个GT标注")
        
        # 分析格式
        analyze_detection_format(det_annos)
        analyze_gt_format(gt_annos)
        check_coordinate_system(det_annos, gt_annos)
        
        # 检查数量统计
        print(f"\n📊 数量统计:")
        
        # 统计检测结果中的对象数量
        total_det_objects = 0
        for det in det_annos:
            if isinstance(det, dict) and 'name' in det:
                total_det_objects += len(det['name'])
        
        # 统计GT中的对象数量
        total_gt_objects = 0
        for gt in gt_annos:
            if isinstance(gt, dict) and 'name' in gt:
                total_gt_objects += len(gt['name'])
        
        print(f"  总检测对象数: {total_det_objects}")
        print(f"  总GT对象数: {total_gt_objects}")
        
        # 检查类别分布
        print(f"\n📋 类别分布:")
        
        # 检测结果类别分布
        det_classes = {}
        for det in det_annos:
            if isinstance(det, dict) and 'name' in det:
                for name in det['name']:
                    det_classes[name] = det_classes.get(name, 0) + 1
        
        print(f"  检测结果类别: {det_classes}")
        
        # GT类别分布
        gt_classes = {}
        for gt in gt_annos:
            if isinstance(gt, dict) and 'name' in gt:
                for name in gt['name']:
                    gt_classes[name] = gt_classes.get(name, 0) + 1
        
        print(f"  GT标注类别: {gt_classes}")
        
        # 检查置信度分布
        print(f"\n📈 置信度分布:")
        all_scores = []
        for det in det_annos:
            if isinstance(det, dict) and 'score' in det:
                all_scores.extend(det['score'])
        
        if all_scores:
            all_scores = np.array(all_scores)
            print(f"  置信度范围: [{all_scores.min():.4f}, {all_scores.max():.4f}]")
            print(f"  平均置信度: {all_scores.mean():.4f}")
            print(f"  高置信度(>0.5)数量: {np.sum(all_scores > 0.5)}")
            print(f"  中置信度(0.3-0.5)数量: {np.sum((all_scores > 0.3) & (all_scores <= 0.5))}")
            print(f"  低置信度(<0.3)数量: {np.sum(all_scores <= 0.3)}")
        
        print(f"\n💡 分析完成！检查上述信息是否有异常。")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
