#!/usr/bin/env python3
"""
从原始KITTI数据集复制标注文件到KITTI-C
用于解决KITTI-C缺少ground truth标注的问题
"""

import os
import shutil
import sys
from pathlib import Path


def extract_sample_id(filename):
    """从KITTI-C文件名提取原始样本ID"""
    # 例如: 000000_fog_d1.png -> 000000
    return filename.split('_')[0]


def copy_labels_to_kitti_c(kitti_path, kitti_c_path):
    """从KITTI复制标注文件到KITTI-C"""
    
    kitti_path = Path(kitti_path)
    kitti_c_path = Path(kitti_c_path)
    
    # 检查原始KITTI标注路径
    kitti_labels_path = kitti_path / 'training' / 'label_2'
    if not kitti_labels_path.exists():
        print(f"❌ 原始KITTI标注路径不存在: {kitti_labels_path}")
        return False
    
    print(f"📂 原始KITTI标注路径: {kitti_labels_path}")
    print(f"📂 目标KITTI-C路径: {kitti_c_path}")
    
    corruption_types = ['fog', 'snow', 'rain', 'sun']
    
    for corruption in corruption_types:
        print(f"\n🔄 处理 {corruption} 腐败类型...")
        
        corruption_path = kitti_c_path / corruption
        if not corruption_path.exists():
            print(f"❌ KITTI-C腐败文件夹不存在: {corruption_path}")
            continue
        
        # 创建labels/val目录
        labels_val_path = corruption_path / 'labels' / 'val'
        labels_val_path.mkdir(parents=True, exist_ok=True)
        print(f"📁 创建目录: {labels_val_path}")
        
        # 获取该腐败类型的图像文件列表
        images_val_path = corruption_path / 'images' / 'val'
        if not images_val_path.exists():
            print(f"❌ 图像目录不存在: {images_val_path}")
            continue
        
        # 获取所有图像文件的样本ID
        image_files = list(images_val_path.glob('*.png'))
        sample_ids = set()
        
        for img_file in image_files:
            sample_id = extract_sample_id(img_file.name)
            sample_ids.add(sample_id)
        
        print(f"📊 找到 {len(sample_ids)} 个唯一样本ID")
        
        # 复制对应的标注文件
        copied_count = 0
        for sample_id in sample_ids:
            src_label_file = kitti_labels_path / f"{sample_id}.txt"
            dst_label_file = labels_val_path / f"{sample_id}.txt"
            
            if src_label_file.exists():
                shutil.copy2(src_label_file, dst_label_file)
                copied_count += 1
            else:
                print(f"⚠️  标注文件不存在: {src_label_file}")
        
        print(f"✓ 复制了 {copied_count} 个标注文件到 {corruption}/labels/val/")
    
    print(f"\n🎉 标注文件复制完成！")
    return True


def verify_labels(kitti_c_path):
    """验证复制的标注文件"""
    
    kitti_c_path = Path(kitti_c_path)
    corruption_types = ['fog', 'snow', 'rain', 'sun']
    
    print(f"\n🔍 验证标注文件...")
    
    for corruption in corruption_types:
        labels_path = kitti_c_path / corruption / 'labels' / 'val'
        if labels_path.exists():
            label_count = len(list(labels_path.glob('*.txt')))
            print(f"  ✓ {corruption}: {label_count} 个标注文件")
        else:
            print(f"  ❌ {corruption}: 标注目录不存在")


def main():
    # 使用固定的路径，无需命令行参数
    kitti_path = "/data/liufeifei/project/robofusion/mmdetection3d/data/kitti"
    kitti_c_path = "/data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C"

    print("=" * 70)
    print("KITTI标注文件复制到KITTI-C工具")
    print("=" * 70)
    print(f"📂 原始KITTI路径: {kitti_path}")
    print(f"📂 目标KITTI-C路径: {kitti_c_path}")
    print("=" * 70)

    # 检查路径是否存在
    if not Path(kitti_path).exists():
        print(f"❌ 原始KITTI路径不存在: {kitti_path}")
        sys.exit(1)

    if not Path(kitti_c_path).exists():
        print(f"❌ KITTI-C路径不存在: {kitti_c_path}")
        sys.exit(1)

    success = copy_labels_to_kitti_c(kitti_path, kitti_c_path)

    if success:
        verify_labels(kitti_c_path)
        print(f"\n💡 标注文件复制完成！")
        print(f"💡 下一步：运行 regenerate_kitti_c_infos.py 重新生成info文件")
        print(f"💡 然后重新运行测试来获得mAP结果！")

    print("=" * 70)


if __name__ == '__main__':
    main()
