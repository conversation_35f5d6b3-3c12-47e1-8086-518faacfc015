---
description: Learn about various utility functions in Ultralytics YOLO, including x, y, width, height conversions, non-max suppression, and more.
keywords: Ultralytics, YOLO, Utils Ops, Functions, coco80_to_coco91_class, scale_boxes, non_max_suppression, clip_coords, xyxy2xywh, xywhn2xyxy, xyn2xy, xyxy2ltwh, ltwh2xyxy, resample_segments, process_mask_upsample, process_mask_native, masks2segments, clean_str
---

## Profile
---
### ::: ultralytics.yolo.utils.ops.Profile
<br><br>

## coco80_to_coco91_class
---
### ::: ultralytics.yolo.utils.ops.coco80_to_coco91_class
<br><br>

## segment2box
---
### ::: ultralytics.yolo.utils.ops.segment2box
<br><br>

## scale_boxes
---
### ::: ultralytics.yolo.utils.ops.scale_boxes
<br><br>

## make_divisible
---
### ::: ultralytics.yolo.utils.ops.make_divisible
<br><br>

## non_max_suppression
---
### ::: ultralytics.yolo.utils.ops.non_max_suppression
<br><br>

## clip_boxes
---
### ::: ultralytics.yolo.utils.ops.clip_boxes
<br><br>

## clip_coords
---
### ::: ultralytics.yolo.utils.ops.clip_coords
<br><br>

## scale_image
---
### ::: ultralytics.yolo.utils.ops.scale_image
<br><br>

## xyxy2xywh
---
### ::: ultralytics.yolo.utils.ops.xyxy2xywh
<br><br>

## xywh2xyxy
---
### ::: ultralytics.yolo.utils.ops.xywh2xyxy
<br><br>

## xywhn2xyxy
---
### ::: ultralytics.yolo.utils.ops.xywhn2xyxy
<br><br>

## xyxy2xywhn
---
### ::: ultralytics.yolo.utils.ops.xyxy2xywhn
<br><br>

## xyn2xy
---
### ::: ultralytics.yolo.utils.ops.xyn2xy
<br><br>

## xywh2ltwh
---
### ::: ultralytics.yolo.utils.ops.xywh2ltwh
<br><br>

## xyxy2ltwh
---
### ::: ultralytics.yolo.utils.ops.xyxy2ltwh
<br><br>

## ltwh2xywh
---
### ::: ultralytics.yolo.utils.ops.ltwh2xywh
<br><br>

## ltwh2xyxy
---
### ::: ultralytics.yolo.utils.ops.ltwh2xyxy
<br><br>

## segments2boxes
---
### ::: ultralytics.yolo.utils.ops.segments2boxes
<br><br>

## resample_segments
---
### ::: ultralytics.yolo.utils.ops.resample_segments
<br><br>

## crop_mask
---
### ::: ultralytics.yolo.utils.ops.crop_mask
<br><br>

## process_mask_upsample
---
### ::: ultralytics.yolo.utils.ops.process_mask_upsample
<br><br>

## process_mask
---
### ::: ultralytics.yolo.utils.ops.process_mask
<br><br>

## process_mask_native
---
### ::: ultralytics.yolo.utils.ops.process_mask_native
<br><br>

## scale_coords
---
### ::: ultralytics.yolo.utils.ops.scale_coords
<br><br>

## masks2segments
---
### ::: ultralytics.yolo.utils.ops.masks2segments
<br><br>

## clean_str
---
### ::: ultralytics.yolo.utils.ops.clean_str
<br><br>
