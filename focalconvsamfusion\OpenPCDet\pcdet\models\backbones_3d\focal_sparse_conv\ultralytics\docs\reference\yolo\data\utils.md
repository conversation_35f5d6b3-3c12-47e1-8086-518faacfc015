---
description: Efficiently handle data in YOLO with Ultralytics. Utilize HUBDatasetStats and customize dataset with these data utility functions.
keywords: YOLOv4, Object Detection, Computer Vision, Deep Learning, Convolutional Neural Network, CNN, Ultralytics Docs
---

## HUBDatasetStats
---
### ::: ultralytics.yolo.data.utils.HUBDatasetStats
<br><br>

## img2label_paths
---
### ::: ultralytics.yolo.data.utils.img2label_paths
<br><br>

## get_hash
---
### ::: ultralytics.yolo.data.utils.get_hash
<br><br>

## exif_size
---
### ::: ultralytics.yolo.data.utils.exif_size
<br><br>

## verify_image_label
---
### ::: ultralytics.yolo.data.utils.verify_image_label
<br><br>

## polygon2mask
---
### ::: ultralytics.yolo.data.utils.polygon2mask
<br><br>

## polygons2masks
---
### ::: ultralytics.yolo.data.utils.polygons2masks
<br><br>

## polygons2masks_overlap
---
### ::: ultralytics.yolo.data.utils.polygons2masks_overlap
<br><br>

## check_det_dataset
---
### ::: ultralytics.yolo.data.utils.check_det_dataset
<br><br>

## check_cls_dataset
---
### ::: ultralytics.yolo.data.utils.check_cls_dataset
<br><br>

## compress_one_image
---
### ::: ultralytics.yolo.data.utils.compress_one_image
<br><br>

## delete_dsstore
---
### ::: ultralytics.yolo.data.utils.delete_dsstore
<br><br>

## zip_directory
---
### ::: ultralytics.yolo.data.utils.zip_directory
<br><br>
