#!/usr/bin/env python3
"""
批量测试所有KITTI-C腐败类型
"""

import subprocess
import sys
from pathlib import Path
import time

def run_test(corruption, cfg_file, ckpt_file):
    """运行单个腐败类型的测试"""
    
    print(f"\n🚀 开始测试腐败类型: {corruption}")
    print("=" * 60)
    
    cmd = [
        sys.executable, "test_kitti_c_original.py",
        "--cfg_file", cfg_file,
        "--ckpt", ckpt_file,
        "--corruption", corruption,
        "--workers", "0"
    ]
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1小时超时
        end_time = time.time()
        
        print(f"⏱️ 测试耗时: {(end_time - start_time)/60:.1f} 分钟")
        
        if result.returncode == 0:
            print(f"✅ {corruption} 测试成功完成")
            
            # 提取mAP结果
            output_lines = result.stdout.split('\n')
            map_lines = []
            
            for line in output_lines:
                if "Car AP@0.70, 0.70, 0.70:" in line:
                    map_lines.append(line.strip())
                elif "3d   AP:" in line:
                    map_lines.append(line.strip())
                elif "Car AP@0.70, 0.50, 0.50:" in line:
                    map_lines.append(line.strip())
            
            if map_lines:
                print(f"📊 {corruption} 关键结果:")
                for line in map_lines:
                    print(f"  {line}")
            
            return True, map_lines
        else:
            print(f"❌ {corruption} 测试失败")
            print(f"错误输出: {result.stderr}")
            return False, []
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {corruption} 测试超时")
        return False, []
    except Exception as e:
        print(f"❌ {corruption} 测试异常: {e}")
        return False, []


def extract_3d_ap(results):
    """提取3D AP数值"""
    for line in results:
        if "3d   AP:" in line:
            # 提取数值部分
            ap_part = line.split("3d   AP:")[1].strip()
            return ap_part
    return "N/A"


def main():
    """主函数"""
    
    # 配置参数
    cfg_file = "cfgs/kitti_models/sam_onlinev3_kitti_c_original.yaml"
    ckpt_file = "../output/kitti_models/sam_onlinev3/robofusion_tmux/ckpt/checkpoint_epoch_80.pth"
    
    # 检查文件存在
    if not Path(cfg_file).exists():
        print(f"❌ 配置文件不存在: {cfg_file}")
        return
    
    if not Path(ckpt_file).exists():
        print(f"❌ 模型文件不存在: {ckpt_file}")
        return
    
    # 腐败类型列表
    corruptions = ['fog', 'rain', 'snow', 'sun']
    
    print("🔍 KITTI-C全腐败类型测试")
    print(f"📂 配置文件: {cfg_file}")
    print(f"📂 模型文件: {ckpt_file}")
    print(f"📋 测试腐败类型: {corruptions}")
    
    # 存储所有结果
    all_results = {}
    
    # 逐个测试
    for corruption in corruptions:
        success, map_results = run_test(corruption, cfg_file, ckpt_file)
        all_results[corruption] = {
            'success': success,
            'results': map_results
        }
        
        # 短暂休息
        time.sleep(5)
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 所有腐败类型测试结果汇总")
    print("=" * 80)
    
    print(f"\n📈 3D AP@0.70 结果 (Easy, Moderate, Hard):")
    print("-" * 50)
    
    for corruption in corruptions:
        result = all_results[corruption]
        if result['success']:
            # 查找@0.70的3D AP
            found_70 = False
            for i, line in enumerate(result['results']):
                if "Car AP@0.70, 0.70, 0.70:" in line:
                    # 下一行应该是3d AP
                    if i + 1 < len(result['results']) and "3d   AP:" in result['results'][i + 1]:
                        ap_values = extract_3d_ap([result['results'][i + 1]])
                        print(f"{corruption:>6}: {ap_values}")
                        found_70 = True
                        break
            
            if not found_70:
                print(f"{corruption:>6}: 未找到@0.70结果")
        else:
            print(f"{corruption:>6}: 测试失败")
    
    print(f"\n📈 3D AP@0.50 结果 (Easy, Moderate, Hard):")
    print("-" * 50)
    
    for corruption in corruptions:
        result = all_results[corruption]
        if result['success']:
            # 查找@0.50的3D AP
            found_50 = False
            for i, line in enumerate(result['results']):
                if "Car AP@0.70, 0.50, 0.50:" in line:
                    # 下一行应该是3d AP
                    if i + 1 < len(result['results']) and "3d   AP:" in result['results'][i + 1]:
                        ap_values = extract_3d_ap([result['results'][i + 1]])
                        print(f"{corruption:>6}: {ap_values}")
                        found_50 = True
                        break
            
            if not found_50:
                print(f"{corruption:>6}: 未找到@0.50结果")
        else:
            print(f"{corruption:>6}: 测试失败")
    
    print(f"\n🎉 全部测试完成!")
    print(f"💡 提示: 数值偏低是正常的，KITTI-C是测试鲁棒性的腐败数据集")


if __name__ == '__main__':
    main()
