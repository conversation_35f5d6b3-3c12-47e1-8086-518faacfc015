---
description: Download and unzip YOLO pretrained models. Ultralytics YOLO docs utils.downloads.unzip_file, checks disk space, downloads and attempts assets.
keywords: Ultralytics YOLO, downloads, trained models, datasets, weights, deep learning, computer vision
---

## is_url
---
### ::: ultralytics.yolo.utils.downloads.is_url
<br><br>

## unzip_file
---
### ::: ultralytics.yolo.utils.downloads.unzip_file
<br><br>

## check_disk_space
---
### ::: ultralytics.yolo.utils.downloads.check_disk_space
<br><br>

## safe_download
---
### ::: ultralytics.yolo.utils.downloads.safe_download
<br><br>

## attempt_download_asset
---
### ::: ultralytics.yolo.utils.downloads.attempt_download_asset
<br><br>

## download
---
### ::: ultralytics.yolo.utils.downloads.download
<br><br>
