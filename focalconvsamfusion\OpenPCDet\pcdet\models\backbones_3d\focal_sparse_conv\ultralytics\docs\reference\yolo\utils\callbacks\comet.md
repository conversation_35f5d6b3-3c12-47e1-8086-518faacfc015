---
description: Learn about YOLO callbacks using the Comet.ml platform, enhancing object detection training and testing with custom logging and visualizations.
keywords: Ultralytics, YOLO, callbacks, Comet ML, log images, log predictions, log plots, fetch metadata, fetch annotations, create experiment data, format experiment data
---

## _get_comet_mode
---
### ::: ultralytics.yolo.utils.callbacks.comet._get_comet_mode
<br><br>

## _get_comet_model_name
---
### ::: ultralytics.yolo.utils.callbacks.comet._get_comet_model_name
<br><br>

## _get_eval_batch_logging_interval
---
### ::: ultralytics.yolo.utils.callbacks.comet._get_eval_batch_logging_interval
<br><br>

## _get_max_image_predictions_to_log
---
### ::: ultralytics.yolo.utils.callbacks.comet._get_max_image_predictions_to_log
<br><br>

## _scale_confidence_score
---
### ::: ultralytics.yolo.utils.callbacks.comet._scale_confidence_score
<br><br>

## _should_log_confusion_matrix
---
### ::: ultralytics.yolo.utils.callbacks.comet._should_log_confusion_matrix
<br><br>

## _should_log_image_predictions
---
### ::: ultralytics.yolo.utils.callbacks.comet._should_log_image_predictions
<br><br>

## _get_experiment_type
---
### ::: ultralytics.yolo.utils.callbacks.comet._get_experiment_type
<br><br>

## _create_experiment
---
### ::: ultralytics.yolo.utils.callbacks.comet._create_experiment
<br><br>

## _fetch_trainer_metadata
---
### ::: ultralytics.yolo.utils.callbacks.comet._fetch_trainer_metadata
<br><br>

## _scale_bounding_box_to_original_image_shape
---
### ::: ultralytics.yolo.utils.callbacks.comet._scale_bounding_box_to_original_image_shape
<br><br>

## _format_ground_truth_annotations_for_detection
---
### ::: ultralytics.yolo.utils.callbacks.comet._format_ground_truth_annotations_for_detection
<br><br>

## _format_prediction_annotations_for_detection
---
### ::: ultralytics.yolo.utils.callbacks.comet._format_prediction_annotations_for_detection
<br><br>

## _fetch_annotations
---
### ::: ultralytics.yolo.utils.callbacks.comet._fetch_annotations
<br><br>

## _create_prediction_metadata_map
---
### ::: ultralytics.yolo.utils.callbacks.comet._create_prediction_metadata_map
<br><br>

## _log_confusion_matrix
---
### ::: ultralytics.yolo.utils.callbacks.comet._log_confusion_matrix
<br><br>

## _log_images
---
### ::: ultralytics.yolo.utils.callbacks.comet._log_images
<br><br>

## _log_image_predictions
---
### ::: ultralytics.yolo.utils.callbacks.comet._log_image_predictions
<br><br>

## _log_plots
---
### ::: ultralytics.yolo.utils.callbacks.comet._log_plots
<br><br>

## _log_model
---
### ::: ultralytics.yolo.utils.callbacks.comet._log_model
<br><br>

## on_pretrain_routine_start
---
### ::: ultralytics.yolo.utils.callbacks.comet.on_pretrain_routine_start
<br><br>

## on_train_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.comet.on_train_epoch_end
<br><br>

## on_fit_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.comet.on_fit_epoch_end
<br><br>

## on_train_end
---
### ::: ultralytics.yolo.utils.callbacks.comet.on_train_end
<br><br>
