---
description: Explore Ultralytics YOLO Utils DVC Callbacks such as logging images, plots, confusion matrices, and training progress.
keywords: Ultralytics, YOLO, Utils, DVC, Callbacks, images, plots, confusion matrices, training progress
---

## _logger_disabled
---
### ::: ultralytics.yolo.utils.callbacks.dvc._logger_disabled
<br><br>

## _log_images
---
### ::: ultralytics.yolo.utils.callbacks.dvc._log_images
<br><br>

## _log_plots
---
### ::: ultralytics.yolo.utils.callbacks.dvc._log_plots
<br><br>

## _log_confusion_matrix
---
### ::: ultralytics.yolo.utils.callbacks.dvc._log_confusion_matrix
<br><br>

## on_pretrain_routine_start
---
### ::: ultralytics.yolo.utils.callbacks.dvc.on_pretrain_routine_start
<br><br>

## on_pretrain_routine_end
---
### ::: ultralytics.yolo.utils.callbacks.dvc.on_pretrain_routine_end
<br><br>

## on_train_start
---
### ::: ultralytics.yolo.utils.callbacks.dvc.on_train_start
<br><br>

## on_train_epoch_start
---
### ::: ultralytics.yolo.utils.callbacks.dvc.on_train_epoch_start
<br><br>

## on_fit_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.dvc.on_fit_epoch_end
<br><br>

## on_train_end
---
### ::: ultralytics.yolo.utils.callbacks.dvc.on_train_end
<br><br>
