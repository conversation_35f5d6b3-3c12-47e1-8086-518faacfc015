---
description: Dynamically adjusts input size to optimize GPU memory usage during training. Learn how to use check_train_batch_size with Ultralytics YOLO.
keywords: YOLOv5, batch size, training, Ultralytics Autobatch, object detection, model performance
---

## check_train_batch_size
---
### ::: ultralytics.yolo.utils.autobatch.check_train_batch_size
<br><br>

## autobatch
---
### ::: ultralytics.yolo.utils.autobatch.autobatch
<br><br>
