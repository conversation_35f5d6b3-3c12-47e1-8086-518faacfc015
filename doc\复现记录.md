# RoboFusion 复现记录

## 项目概述
本项目基于TransFusion的SAM集成，使用focalconvsamfusion分支进行开发。

## 环境配置
- GPU: 4卡4090
- CUDA: 11.1
- 分支: focalconvsamfusion

## 修改记录

### 2025-07-24 训练配置优化
1. **学习率调整** (sam_onlinev3.yaml)
   - 将学习率从0.01降低到0.005
   - 原因：batch size从2减半到1时，需要相应调整学习率以保持训练稳定性

2. **批处理大小调整** (sam_onlinev3.yaml)
   - BATCH_SIZE_PER_GPU: 从2改为1
   - 原因：显存限制，避免OOM错误

3. **AD-FPN配置优化** (sam_onlinev3.yaml)
   - USE_SAM_FPN: 设置为False
   - 原因：禁用AD-FPN以降低分辨率，减少小波注意力显存占用

4. **小波注意力重新启用** (sam_onlinev3.yaml)
   - USE_WAVE: 设置为True
   - 原因：配合禁用AD-FPN降低分辨率后，重新启用小波注意力

### 2025-07-24 测试脚本修复
5. **图像维度转换修复** (test_simple.py)
   - 问题：SAM编码器报错 "The batch images should be 3 for RGB, but get 375"
   - 原因：图像数据维度顺序错误，从(B, H, W, 3)需要转换为(B, 3, H, W)
   - 解决：在数据转换时添加 `batch_dict[key].permute(0, 3, 1, 2)` 进行维度变换
   - 位置：第58-68行，在GPU数据转换后添加图像维度修正

6. **kornia依赖问题修复** (pcdet/models/__init__.py)
   - 问题：test.py运行时报错 "NameError: name 'kornia' is not defined"
   - 原因：kornia库未安装，但代码中使用了kornia.image_to_tensor()
   - 解决：用PyTorch原生操作替换kornia功能
   - 修改：将kornia.image_to_tensor()替换为torch.from_numpy().permute()
   - 同时简化test_simple.py，使用统一的load_data_to_gpu函数

7. **kornia版本兼容性问题修复** (kitti_dataset.py)
   - 问题：导入错误 "cannot import name 'inv_ex' from 'torch.linalg'"
   - 原因：kornia版本过新，需要PyTorch 1.11+，但当前PyTorch版本较旧
   - 解决：临时注释corruptions相关导入和MAP_KITTI字典
   - 影响：禁用数据增强中的corruptions功能，但不影响基本训练和测试

8. **kornia导入失败处理优化** (pcdet/models/__init__.py)
   - 问题：kornia导入失败时未定义，导致运行时错误
   - 原因：try-except块静默失败，没有提供备用方案
   - 解决：添加KORNIA_AVAILABLE标志和备用实现
   - 修改：如果kornia可用则使用，否则用PyTorch原生操作替代
   - 同时修改test_simple.py使用统一的load_data_to_gpu函数

## 训练结果
- 成功完成80个epoch的训练
- 获得完整的KITTI数据集评估结果
- Car类别在不同IoU阈值下的AP值均达到较高水平

## 技术要点
1. **学习率与batch size的关系**：当batch size减半时，学习率也应相应减半，以保持梯度更新的稳定性
2. **显存优化策略**：通过禁用AD-FPN和调整batch size来平衡性能和显存使用
3. **小波注意力机制**：在降低分辨率后重新启用，提升特征提取能力
4. **图像数据维度处理**：PyTorch期望图像格式为(B, C, H, W)，需要正确转换numpy数组的维度顺序

## 问题解决记录

### 图像维度错误问题
**错误信息**：`The batch images should be 3 for RGB, but get 375`

**问题分析**：
- 数据加载：图像从文件加载时形状是 (H, W, 3) ✅
- 批处理：在 collate_batch 中堆叠成 (B, H, W, 3) ✅  
- 转换问题：在 test_simple.py 中直接转为tensor，形状仍是 (B, H, W, 3) ❌
- SAM期望：SAM编码器期望输入格式为 (B, 3, H, W) ✅

**解决方案**：
在test_simple.py的数据转换部分添加维度变换：
```python
if key == 'images':
    batch_dict[key] = batch_dict[key].permute(0, 3, 1, 2)
```
