#!/usr/bin/env python3
"""
使用原始KITTI评估方式计算mAP
绕过KITTI-C数据集的evaluation问题
"""

import _init_path
import argparse
import pickle
import copy
from pathlib import Path

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import build_dataloader


def parse_args():
    parser = argparse.ArgumentParser(description='KITTI-style mAP calculation')
    parser.add_argument('--result_file', type=str, required=True,
                       help='path to result.pkl file')
    parser.add_argument('--corruption', type=str, default='sun',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml',
                       help='config file')
    
    return parser.parse_args()


def main():
    args = parse_args()
    
    print("🎯 KITTI风格mAP计算")
    print(f"📂 结果文件: {args.result_file}")
    print(f"📂 腐败类型: {args.corruption}")
    
    # 检查结果文件是否存在
    result_file = Path(args.result_file)
    if not result_file.exists():
        print(f"❌ 结果文件不存在: {result_file}")
        return
    
    # 加载配置
    cfg_from_yaml_file(args.cfg_file, cfg)
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption
    
    # 检查info文件
    info_file_path = Path(cfg.DATA_CONFIG.DATA_PATH) / f'kitti_c_infos_val_{args.corruption}.pkl'
    if not info_file_path.exists():
        print(f"❌ Info文件不存在: {info_file_path}")
        print("请先运行 fix_kitti_c_infos.py 生成包含标注的info文件")
        return
    
    print(f"✅ Info文件存在: {info_file_path}")
    
    try:
        # 构建数据集
        print("📊 构建数据集...")
        test_set, _, _ = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=1,
            dist=False, workers=1, logger=None, training=False
        )
        
        print(f"✅ 数据集构建成功，样本数量: {len(test_set)}")
        
        # 检查数据集是否有ground truth
        if not hasattr(test_set, 'kitti_infos') or len(test_set.kitti_infos) == 0:
            print("❌ 数据集没有加载info信息")
            return
        
        first_info = test_set.kitti_infos[0]
        if 'annos' not in first_info:
            print("❌ 数据集缺少ground truth标注")
            return
        
        print(f"✅ 数据集包含ground truth标注")
        
        # 加载检测结果
        print("📥 加载检测结果...")
        with open(result_file, 'rb') as f:
            det_annos = pickle.load(f)
        
        print(f"✅ 加载了 {len(det_annos)} 个检测结果")
        
        # 提取ground truth标注
        print("📋 提取ground truth标注...")
        gt_annos = []
        for info in test_set.kitti_infos:
            gt_annos.append(copy.deepcopy(info['annos']))
        
        print(f"✅ 提取了 {len(gt_annos)} 个ground truth标注")
        
        # 检查标注格式
        if gt_annos and len(gt_annos) > 0:
            first_gt = gt_annos[0]
            if isinstance(first_gt, dict) and 'name' in first_gt:
                gt_obj_count = len(first_gt['name'])
                print(f"📊 第一个样本包含 {gt_obj_count} 个标注对象")
            else:
                print(f"⚠️ Ground truth格式异常: {type(first_gt)}")
        
        # 检查检测结果格式
        if det_annos and len(det_annos) > 0:
            first_det = det_annos[0]
            if isinstance(first_det, dict) and 'name' in first_det:
                det_obj_count = len(first_det['name'])
                print(f"📊 第一个样本包含 {det_obj_count} 个检测结果")
            else:
                print(f"⚠️ 检测结果格式异常: {type(first_det)}")
        
        # 确保数量匹配
        if len(gt_annos) != len(det_annos):
            print(f"⚠️ GT数量({len(gt_annos)}) != 检测结果数量({len(det_annos)})")
            min_len = min(len(gt_annos), len(det_annos))
            gt_annos = gt_annos[:min_len]
            det_annos = det_annos[:min_len]
            print(f"📏 截取到相同长度: {min_len}")
        
        # 使用KITTI官方评估
        print("📈 使用KITTI官方评估计算mAP...")
        print("=" * 80)
        
        try:
            # 导入正确的KITTI评估模块
            from pcdet.datasets.kitti.kitti_object_eval_python import eval as kitti_eval

            # 使用KITTI官方评估函数
            ap_result_str, ap_dict = kitti_eval.get_official_eval_result(
                gt_annos, det_annos, cfg.CLASS_NAMES
            )
            
            print("🎉 mAP计算完成!")
            print("📊 评估结果:")
            print("=" * 80)
            print(ap_result_str)
            print("=" * 80)
            
            # 保存结果
            output_dir = result_file.parent
            mAP_text_path = output_dir / f'mAP_results_kitti_style_{args.corruption}.txt'
            with open(mAP_text_path, 'w') as f:
                f.write(f"KITTI-C {args.corruption.upper()} mAP Results (KITTI Style)\n")
                f.write("=" * 80 + "\n")
                f.write(ap_result_str)
                f.write("\n" + "=" * 80 + "\n")
                
                # 添加详细结果
                f.write("\nDetailed Results:\n")
                f.write("-" * 40 + "\n")
                for key, value in ap_dict.items():
                    f.write(f"{key}: {value}\n")
            
            print(f"\n💾 结果已保存到: {mAP_text_path}")
            
            # 显示关键指标
            print(f"\n📋 关键指标 ({args.corruption.upper()}):")
            print("-" * 50)
            
            # 查找Car相关的AP结果
            car_keys = [k for k in ap_dict.keys() if 'Car' in k]
            if car_keys:
                for key in sorted(car_keys):
                    value = ap_dict[key]
                    if isinstance(value, (int, float)):
                        print(f"  {key}: {value:.4f}")
                    elif isinstance(value, (list, tuple)):
                        if len(value) == 3:  # easy, moderate, hard
                            print(f"  {key}: [Easy: {value[0]:.4f}, Moderate: {value[1]:.4f}, Hard: {value[2]:.4f}]")
                        else:
                            formatted_values = [f'{v:.4f}' if isinstance(v, (int, float)) else str(v) for v in value]
                            print(f"  {key}: [{', '.join(formatted_values)}]")
            
            # 查找所有AP相关指标
            ap_keys = [k for k in ap_dict.keys() if 'AP' in k.upper()]
            if ap_keys:
                print(f"\n📈 所有AP指标:")
                print("-" * 30)
                for key in sorted(ap_keys):
                    value = ap_dict[key]
                    if isinstance(value, (int, float)):
                        print(f"  {key}: {value:.4f}")
                    elif isinstance(value, (list, tuple)) and len(value) > 0:
                        if all(isinstance(v, (int, float)) for v in value):
                            formatted_values = [f'{v:.4f}' for v in value]
                            print(f"  {key}: [{', '.join(formatted_values)}]")
            
            print(f"\n🎯 成功！这就是论文中需要的mAP结果！")
            
        except Exception as e:
            print(f"❌ KITTI评估失败: {e}")
            print("🔍 错误详情:")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
