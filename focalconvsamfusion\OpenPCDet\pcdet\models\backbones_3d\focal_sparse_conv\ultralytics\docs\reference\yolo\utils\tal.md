---
description: Improve your YOLO models with Ultralytics' TaskAlignedAssigner, select_highest_overlaps, and dist2bbox utilities. Streamline your workflow today.
keywords: Ultrayltics, YOLO, select_candidates_in_gts, make_anchor, bbox2dist, object detection, tracking
---

## TaskAlignedAssigner
---
### ::: ultralytics.yolo.utils.tal.TaskAlignedAssigner
<br><br>

## select_candidates_in_gts
---
### ::: ultralytics.yolo.utils.tal.select_candidates_in_gts
<br><br>

## select_highest_overlaps
---
### ::: ultralytics.yolo.utils.tal.select_highest_overlaps
<br><br>

## make_anchors
---
### ::: ultralytics.yolo.utils.tal.make_anchors
<br><br>

## dist2bbox
---
### ::: ultralytics.yolo.utils.tal.dist2bbox
<br><br>

## bbox2dist
---
### ::: ultralytics.yolo.utils.tal.bbox2dist
<br><br>
