{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "Ultralytics HUB", "provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "FIzICjaph_Wy"}, "source": ["<a align=\"center\" href=\"https://hub.ultralytics.com\" target=\"_blank\">\n", "<img width=\"1024\", src=\"https://github.com/ultralytics/assets/raw/main/im/ultralytics-hub.png\"></a>\n", "\n", "<div align=\"center\">\n", "  <a href=\"https://github.com/ultralytics/ultralytics/actions/workflows/ci.yaml\">\n", "    <img src=\"https://github.com/ultralytics/ultralytics/actions/workflows/ci.yaml/badge.svg\" alt=\"CI CPU\"></a>\n", "  <a href=\"https://colab.research.google.com/github/ultralytics/ultralytics/blob/main/examples/hub.ipynb\">\n", "    <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"></a>\n", "\n", "Welcome to the [Ultralytics](https://ultralytics.com/) HUB notebook! \n", "\n", "This notebook allows you to train [YOLOv5](https://github.com/ultralytics/yolov5) and [YOLOv8](https://github.com/ultralytics/ultralytics) 🚀 models using [HUB](https://hub.ultralytics.com/). Please browse the YOLOv8 <a href=\"https://docs.ultralytics.com\">Docs</a> for details, raise an issue on <a href=\"https://github.com/ultralytics/ultralytics/issues/new/choose\">GitHub</a> for support, and join our <a href=\"https://discord.gg/bNc8wwx7Qy\">Discord</a> community for questions and discussions!\n", "</div>"]}, {"cell_type": "markdown", "metadata": {"id": "eRQ2ow94MiOv"}, "source": ["# Setup\n", "\n", "Pip install `ultralytics` and [dependencies](https://github.com/ultralytics/ultralytics/blob/main/requirements.txt) and check software and hardware."]}, {"cell_type": "code", "metadata": {"id": "FyDnXd-n4c7Y", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "22dcbc27-9c6f-44fb-9745-620431f93793"}, "source": ["%pip install ultralytics  # install\n", "from ultralytics import YOLO, checks, hub\n", "checks()  # checks"], "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["Ultralytics YOLOv8.0.64 🚀 Python-3.9.16 torch-2.0.0+cu118 CUDA:0 (Tesla T4, 15102MiB)\n", "Setup complete ✅ (2 CPUs, 12.7 GB RAM, 28.3/166.8 GB disk)\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "cQ9BwaAqxAm4"}, "source": ["# Start\n", "\n", "Login with your [API key](https://hub.ultralytics.com/settings?tab=api+keys), select your YOLO 🚀 model and start training!"]}, {"cell_type": "code", "metadata": {"id": "XSlZaJ9Iw_iZ"}, "source": ["hub.login('API_KEY')  # use your API key\n", "\n", "model = YOLO('https://hub.ultralytics.com/MODEL_ID')  # use your model URL\n", "model.train()  # train model"], "execution_count": null, "outputs": []}]}