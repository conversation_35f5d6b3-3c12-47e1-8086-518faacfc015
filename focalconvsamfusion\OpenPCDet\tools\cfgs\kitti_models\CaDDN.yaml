CLASS_NAMES: ['Car', 'Pedestrian', 'Cyclist']

DATA_CONFIG:
    _BASE_CONFIG_: cfgs/dataset_configs/kitti_dataset.yaml
    POINT_CLOUD_RANGE: [2, -30.08, -3.0, 46.8, 30.08, 1.0]
    GET_ITEM_LIST: ["images", "depth_maps", "calib_matricies", "gt_boxes2d"]

    DATA_PROCESSOR:
        - NAME: mask_points_and_boxes_outside_range
          REMOVE_OUTSIDE_BOXES: True

        - NAME: calculate_grid_size
          VOXEL_SIZE: [0.16, 0.16, 0.16]

        - NAME: downsample_depth_map
          DOWNSAMPLE_FACTOR: 4

    DATA_AUGMENTOR:
        DISABLE_AUG_LIST: ['placeholder']
        AUG_CONFIG_LIST:
            - NAME: random_image_flip
              ALONG_AXIS_LIST: ['horizontal']

MODEL:
    NAME: CaDDN

    VFE:
        NAME: ImageVFE
        FFN:
            NAME: DepthFFN
            DDN:
                NAME: DDNDeepLabV3
                BACKBONE_NAME: ResNet101
                ARGS: {
                    "feat_extract_layer": "layer1",
                    "pretrained_path": "../checkpoints/deeplabv3_resnet101_coco-586e9e4e.pth"
                }
            CHANNEL_REDUCE: {
                "in_channels": 256,
                "out_channels": 64,
                "kernel_size": 1,
                "stride": 1,
                "bias": False
            }
            DISCRETIZE: {
                "mode": LID,
                "num_bins": 80,
                "depth_min": 2.0,
                "depth_max": 46.8
            }
            LOSS:
                NAME: DDNLoss
                ARGS: {
                    'weight': 3.0,
                    'alpha': 0.25,
                    'gamma': 2.0,
                    'fg_weight': 13,
                    'bg_weight': 1
                }
        F2V:
            NAME: FrustumToVoxel
            SAMPLER: {
                "mode": "bilinear",
                "padding_mode": "zeros"
            }


    MAP_TO_BEV:
        NAME: Conv2DCollapse
        NUM_BEV_FEATURES: 64
        ARGS: {
            "kernel_size": 1,
            "stride": 1,
            "bias": False
        }

    BACKBONE_2D:
        NAME: BaseBEVBackbone
        LAYER_NUMS: [10, 10, 10]
        LAYER_STRIDES: [2, 2, 2]
        NUM_FILTERS: [64, 128, 256]
        UPSAMPLE_STRIDES: [1, 2, 4]
        NUM_UPSAMPLE_FILTERS: [128, 128, 128]

    DENSE_HEAD:
        NAME: AnchorHeadSingle
        CLASS_AGNOSTIC: False

        USE_DIRECTION_CLASSIFIER: True
        DIR_OFFSET: 0.78539
        DIR_LIMIT_OFFSET: 0.0
        NUM_DIR_BINS: 2

        ANCHOR_GENERATOR_CONFIG: [
            {
                'class_name': 'Car',
                'anchor_sizes': [[3.9, 1.6, 1.56]],
                'anchor_rotations': [0, 1.57],
                'anchor_bottom_heights': [-1.78],
                'align_center': False,
                'feature_map_stride': 2,
                'matched_threshold': 0.6,
                'unmatched_threshold': 0.45
            },
            {
                'class_name': 'Pedestrian',
                'anchor_sizes': [[0.8, 0.6, 1.73]],
                'anchor_rotations': [0, 1.57],
                'anchor_bottom_heights': [-0.6],
                'align_center': False,
                'feature_map_stride': 2,
                'matched_threshold': 0.5,
                'unmatched_threshold': 0.35
            },
            {
                'class_name': 'Cyclist',
                'anchor_sizes': [[1.76, 0.6, 1.73]],
                'anchor_rotations': [0, 1.57],
                'anchor_bottom_heights': [-0.6],
                'align_center': False,
                'feature_map_stride': 2,
                'matched_threshold': 0.5,
                'unmatched_threshold': 0.35
            }
        ]

        TARGET_ASSIGNER_CONFIG:
            NAME: AxisAlignedTargetAssigner
            POS_FRACTION: -1.0
            SAMPLE_SIZE: 512
            NORM_BY_NUM_EXAMPLES: False
            MATCH_HEIGHT: False
            BOX_CODER: ResidualCoder

        LOSS_CONFIG:
            LOSS_WEIGHTS: {
                'cls_weight': 1.0,
                'loc_weight': 2.0,
                'dir_weight': 0.2,
                'code_weights': [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
            }

    POST_PROCESSING:
        RECALL_THRESH_LIST: [0.3, 0.5, 0.7]
        SCORE_THRESH: 0.1
        OUTPUT_RAW_SCORE: False

        EVAL_METRIC: kitti

        NMS_CONFIG:
            MULTI_CLASSES_NMS: False
            NMS_TYPE: nms_gpu
            NMS_THRESH: 0.01
            NMS_PRE_MAXSIZE: 4096
            NMS_POST_MAXSIZE: 500


OPTIMIZATION:
    BATCH_SIZE_PER_GPU: 4
    NUM_EPOCHS: 80

    OPTIMIZER: adam_onecycle
    LR: 0.001
    WEIGHT_DECAY: 0.01
    MOMENTUM: 0.9

    MOMS: [0.95, 0.85]
    PCT_START: 0.4
    DIV_FACTOR: 10
    DECAY_STEP_LIST: [35, 45]
    LR_DECAY: 0.1
    LR_CLIP: 0.0000001

    LR_WARMUP: False
    WARMUP_EPOCH: 1

    GRAD_NORM_CLIP: 10
