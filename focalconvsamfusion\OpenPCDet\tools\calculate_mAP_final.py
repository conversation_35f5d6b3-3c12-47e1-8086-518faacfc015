#!/usr/bin/env python3
"""
最终版本的mAP计算脚本
使用正确的KITTI评估模块
"""

import _init_path
import argparse
import pickle
import copy
from pathlib import Path

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import build_dataloader
from pcdet.datasets.kitti.kitti_object_eval_python import eval as kitti_eval


def parse_args():
    parser = argparse.ArgumentParser(description='Final mAP calculation with correct KITTI eval')
    parser.add_argument('--result_file', type=str, required=True,
                       help='path to result.pkl file')
    parser.add_argument('--corruption', type=str, default='sun',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml',
                       help='config file')
    
    return parser.parse_args()


def main():
    args = parse_args()
    
    print("🎯 最终版mAP计算")
    print(f"📂 结果文件: {args.result_file}")
    print(f"📂 腐败类型: {args.corruption}")
    
    # 检查结果文件是否存在
    result_file = Path(args.result_file)
    if not result_file.exists():
        print(f"❌ 结果文件不存在: {result_file}")
        return
    
    # 加载配置
    cfg_from_yaml_file(args.cfg_file, cfg)
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption
    
    # 检查info文件
    info_file_path = Path(cfg.DATA_CONFIG.DATA_PATH) / f'kitti_c_infos_val_{args.corruption}.pkl'
    if not info_file_path.exists():
        print(f"❌ Info文件不存在: {info_file_path}")
        print("请先运行 fix_kitti_c_infos.py 生成包含标注的info文件")
        return
    
    print(f"✅ Info文件存在: {info_file_path}")
    
    try:
        # 构建数据集
        print("📊 构建数据集...")
        test_set, _, _ = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=1,
            dist=False, workers=1, logger=None, training=False
        )
        
        print(f"✅ 数据集构建成功，样本数量: {len(test_set)}")
        
        # 检查数据集是否有ground truth
        if not hasattr(test_set, 'kitti_infos') or len(test_set.kitti_infos) == 0:
            print("❌ 数据集没有加载info信息")
            return
        
        first_info = test_set.kitti_infos[0]
        if 'annos' not in first_info:
            print("❌ 数据集缺少ground truth标注")
            return
        
        print(f"✅ 数据集包含ground truth标注")
        
        # 加载检测结果
        print("📥 加载检测结果...")
        with open(result_file, 'rb') as f:
            det_annos = pickle.load(f)
        
        print(f"✅ 加载了 {len(det_annos)} 个检测结果")
        
        # 提取ground truth标注
        print("📋 提取ground truth标注...")
        gt_annos = []
        for info in test_set.kitti_infos:
            gt_annos.append(copy.deepcopy(info['annos']))
        
        print(f"✅ 提取了 {len(gt_annos)} 个ground truth标注")
        
        # 确保数量匹配
        if len(gt_annos) != len(det_annos):
            print(f"⚠️ GT数量({len(gt_annos)}) != 检测结果数量({len(det_annos)})")
            min_len = min(len(gt_annos), len(det_annos))
            gt_annos = gt_annos[:min_len]
            det_annos = det_annos[:min_len]
            print(f"📏 截取到相同长度: {min_len}")
        
        # 使用KITTI官方评估
        print("📈 使用KITTI官方评估计算mAP...")
        print("=" * 80)
        
        # 调用正确的KITTI评估函数
        ap_result_str, ap_dict = kitti_eval.get_official_eval_result(
            gt_annos, det_annos, cfg.CLASS_NAMES
        )
        
        print("🎉 mAP计算完成!")
        print("📊 评估结果:")
        print("=" * 80)
        print(ap_result_str)
        print("=" * 80)
        
        # 保存结果
        output_dir = result_file.parent
        mAP_text_path = output_dir / f'mAP_results_final_{args.corruption}.txt'
        with open(mAP_text_path, 'w') as f:
            f.write(f"KITTI-C {args.corruption.upper()} mAP Results (Final)\n")
            f.write("=" * 80 + "\n")
            f.write(ap_result_str)
            f.write("\n" + "=" * 80 + "\n")
            
            # 添加详细结果
            f.write("\nDetailed Results Dictionary:\n")
            f.write("-" * 40 + "\n")
            for key, value in ap_dict.items():
                f.write(f"{key}: {value}\n")
        
        print(f"\n💾 结果已保存到: {mAP_text_path}")
        
        # 显示关键指标
        print(f"\n📋 关键指标 ({args.corruption.upper()}):")
        print("-" * 60)
        
        # 显示所有结果
        for key, value in ap_dict.items():
            if isinstance(value, (int, float)):
                print(f"  {key}: {value:.4f}")
            elif isinstance(value, (list, tuple)):
                if len(value) == 3:  # easy, moderate, hard
                    print(f"  {key}: [Easy: {value[0]:.4f}, Moderate: {value[1]:.4f}, Hard: {value[2]:.4f}]")
                else:
                    formatted_values = [f'{v:.4f}' if isinstance(v, (int, float)) else str(v) for v in value]
                    print(f"  {key}: [{', '.join(formatted_values)}]")
            else:
                print(f"  {key}: {value}")
        
        # 特别提取Car相关的3D AP结果
        print(f"\n🚗 Car 3D AP结果 (这就是论文中的关键指标):")
        print("-" * 50)
        
        # 查找Car相关的AP
        car_3d_keys = [k for k in ap_dict.keys() if 'Car' in k and '3d' in k.lower()]
        if car_3d_keys:
            for key in car_3d_keys:
                value = ap_dict[key]
                if isinstance(value, (list, tuple)) and len(value) == 3:
                    print(f"  {key}:")
                    print(f"    Easy: {value[0]:.4f}")
                    print(f"    Moderate: {value[1]:.4f}")
                    print(f"    Hard: {value[2]:.4f}")
                else:
                    print(f"  {key}: {value}")
        
        # 查找所有AP结果
        all_ap_keys = [k for k in ap_dict.keys() if 'AP' in k]
        if all_ap_keys:
            print(f"\n📈 所有AP指标:")
            print("-" * 40)
            for key in sorted(all_ap_keys):
                value = ap_dict[key]
                if isinstance(value, (list, tuple)) and len(value) == 3:
                    print(f"  {key}: [{value[0]:.4f}, {value[1]:.4f}, {value[2]:.4f}]")
                elif isinstance(value, (int, float)):
                    print(f"  {key}: {value:.4f}")
                else:
                    print(f"  {key}: {value}")
        
        print(f"\n🎯 成功！这就是论文中需要的mAP结果！")
        print(f"📊 类似论文表格中的数值，例如：")
        print(f"   RoboFusion-L {args.corruption}: XX.XX (3D AP)")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        print("🔍 错误详情:")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
