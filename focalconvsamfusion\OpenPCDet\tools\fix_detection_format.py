#!/usr/bin/env python3
"""
修复检测结果格式的脚本
解决坐标系不匹配和类别过滤问题
"""

import _init_path
import argparse
import pickle
import copy
import numpy as np
from pathlib import Path

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import build_dataloader
from pcdet.datasets.kitti.kitti_object_eval_python import eval as kitti_eval


def parse_args():
    parser = argparse.ArgumentParser(description='Fix detection format and calculate mAP')
    parser.add_argument('--result_file', type=str, required=True,
                       help='path to result.pkl file')
    parser.add_argument('--corruption', type=str, default='sun',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml',
                       help='config file')
    
    return parser.parse_args()


def filter_gt_by_class(gt_annos, target_classes=['Car']):
    """过滤GT标注，只保留目标类别"""
    
    filtered_gt_annos = []
    
    for gt in gt_annos:
        if not isinstance(gt, dict) or 'name' not in gt:
            # 保持空标注
            filtered_gt_annos.append({
                'name': np.array([]),
                'truncated': np.array([]),
                'occluded': np.array([]),
                'alpha': np.array([]),
                'bbox': np.zeros((0, 4)),
                'dimensions': np.zeros((0, 3)),
                'location': np.zeros((0, 3)),
                'rotation_y': np.array([]),
                'difficulty': np.array([])
            })
            continue
        
        # 找到目标类别的索引
        names = gt['name']
        target_indices = []
        
        for i, name in enumerate(names):
            if name in target_classes:
                target_indices.append(i)
        
        if len(target_indices) == 0:
            # 没有目标类别，创建空标注
            filtered_gt = {
                'name': np.array([]),
                'truncated': np.array([]),
                'occluded': np.array([]),
                'alpha': np.array([]),
                'bbox': np.zeros((0, 4)),
                'dimensions': np.zeros((0, 3)),
                'location': np.zeros((0, 3)),
                'rotation_y': np.array([]),
                'difficulty': np.array([])
            }
        else:
            # 过滤出目标类别
            filtered_gt = {}
            for key in gt.keys():
                if key in ['name', 'truncated', 'occluded', 'alpha', 'rotation_y', 'difficulty']:
                    filtered_gt[key] = gt[key][target_indices]
                elif key in ['bbox', 'dimensions', 'location']:
                    filtered_gt[key] = gt[key][target_indices]
                else:
                    filtered_gt[key] = gt[key]
        
        filtered_gt_annos.append(filtered_gt)
    
    return filtered_gt_annos


def check_coordinate_system(det_annos, gt_annos):
    """检查并修复坐标系问题"""
    
    print("🔍 坐标系分析:")
    
    # 统计检测结果的坐标范围
    det_locations = []
    for det in det_annos:
        if isinstance(det, dict) and 'location' in det and len(det['location']) > 0:
            det_locations.extend(det['location'])
    
    if det_locations:
        det_locations = np.array(det_locations)
        print(f"  检测结果坐标范围:")
        print(f"    X: [{det_locations[:, 0].min():.2f}, {det_locations[:, 0].max():.2f}]")
        print(f"    Y: [{det_locations[:, 1].min():.2f}, {det_locations[:, 1].max():.2f}]")
        print(f"    Z: [{det_locations[:, 2].min():.2f}, {det_locations[:, 2].max():.2f}]")
    
    # 统计GT的坐标范围
    gt_locations = []
    for gt in gt_annos:
        if isinstance(gt, dict) and 'location' in gt and len(gt['location']) > 0:
            gt_locations.extend(gt['location'])
    
    if gt_locations:
        gt_locations = np.array(gt_locations)
        print(f"  GT标注坐标范围:")
        print(f"    X: [{gt_locations[:, 0].min():.2f}, {gt_locations[:, 0].max():.2f}]")
        print(f"    Y: [{gt_locations[:, 1].min():.2f}, {gt_locations[:, 1].max():.2f}]")
        print(f"    Z: [{gt_locations[:, 2].min():.2f}, {gt_locations[:, 2].max():.2f}]")
    
    # 检查坐标系是否匹配
    if det_locations.size > 0 and gt_locations.size > 0:
        det_x_range = det_locations[:, 0].max() - det_locations[:, 0].min()
        gt_x_range = gt_locations[:, 0].max() - gt_locations[:, 0].min()
        
        print(f"  坐标范围比较:")
        print(f"    检测结果X范围: {det_x_range:.2f}")
        print(f"    GT标注X范围: {gt_x_range:.2f}")
        
        if abs(det_x_range - gt_x_range) > 50:  # 如果范围差异很大
            print("  ⚠️ 坐标系可能不匹配！")
            return False
        else:
            print("  ✅ 坐标系看起来匹配")
            return True
    
    return True


def apply_confidence_threshold(det_annos, threshold=0.3):
    """应用置信度阈值过滤"""
    
    filtered_det_annos = []
    total_before = 0
    total_after = 0
    
    for det in det_annos:
        if not isinstance(det, dict) or 'score' not in det:
            filtered_det_annos.append(det)
            continue
        
        scores = det['score']
        total_before += len(scores)
        
        # 找到高置信度的检测
        high_conf_indices = scores >= threshold
        
        if np.sum(high_conf_indices) == 0:
            # 没有高置信度检测，创建空结果
            filtered_det = {
                'name': np.array([]),
                'truncated': np.array([]),
                'occluded': np.array([]),
                'alpha': np.array([]),
                'bbox': np.zeros((0, 4)),
                'dimensions': np.zeros((0, 3)),
                'location': np.zeros((0, 3)),
                'rotation_y': np.array([]),
                'score': np.array([]),
                'boxes_lidar': np.zeros((0, 7)),
                'frame_id': det.get('frame_id', '')
            }
        else:
            # 过滤出高置信度检测
            filtered_det = {}
            for key in det.keys():
                if key in ['name', 'truncated', 'occluded', 'alpha', 'rotation_y', 'score']:
                    filtered_det[key] = det[key][high_conf_indices]
                elif key in ['bbox', 'dimensions', 'location']:
                    filtered_det[key] = det[key][high_conf_indices]
                elif key == 'boxes_lidar':
                    filtered_det[key] = det[key][high_conf_indices]
                else:
                    filtered_det[key] = det[key]
        
        total_after += len(filtered_det['score'])
        filtered_det_annos.append(filtered_det)
    
    print(f"📈 置信度过滤 (阈值={threshold}):")
    print(f"  过滤前: {total_before} 个检测")
    print(f"  过滤后: {total_after} 个检测")
    print(f"  保留率: {total_after/total_before*100:.1f}%")
    
    return filtered_det_annos


def main():
    args = parse_args()
    
    print("🔧 修复检测结果格式并计算mAP")
    print(f"📂 结果文件: {args.result_file}")
    print(f"📂 腐败类型: {args.corruption}")
    
    # 检查结果文件是否存在
    result_file = Path(args.result_file)
    if not result_file.exists():
        print(f"❌ 结果文件不存在: {result_file}")
        return
    
    # 加载配置
    cfg_from_yaml_file(args.cfg_file, cfg)
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption
    
    try:
        # 构建数据集
        print("📊 构建数据集...")
        test_set, _, _ = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=1,
            dist=False, workers=1, logger=None, training=False
        )
        
        print(f"✅ 数据集构建成功，样本数量: {len(test_set)}")
        
        # 加载检测结果
        print("📥 加载检测结果...")
        with open(result_file, 'rb') as f:
            det_annos = pickle.load(f)
        
        print(f"✅ 加载了 {len(det_annos)} 个检测结果")
        
        # 提取GT标注
        print("📋 提取GT标注...")
        gt_annos = []
        for info in test_set.kitti_infos:
            gt_annos.append(copy.deepcopy(info['annos']))
        
        print(f"✅ 提取了 {len(gt_annos)} 个GT标注")
        
        # 过滤GT标注，只保留Car类别
        print("🔍 过滤GT标注，只保留Car类别...")
        filtered_gt_annos = filter_gt_by_class(gt_annos, target_classes=['Car'])
        
        # 统计过滤后的GT
        total_gt_cars = sum(len(gt['name']) for gt in filtered_gt_annos if isinstance(gt, dict))
        print(f"✅ 过滤后GT中Car数量: {total_gt_cars}")
        
        # 检查坐标系
        coord_ok = check_coordinate_system(det_annos, filtered_gt_annos)
        
        # 尝试不同的置信度阈值
        thresholds = [0.1, 0.3, 0.5]
        
        for threshold in thresholds:
            print(f"\n{'='*60}")
            print(f"🧪 测试置信度阈值: {threshold}")
            print(f"{'='*60}")
            
            # 应用置信度阈值
            filtered_det_annos = apply_confidence_threshold(det_annos, threshold)
            
            # 计算mAP
            try:
                ap_result_str, ap_dict = kitti_eval.get_official_eval_result(
                    filtered_gt_annos, filtered_det_annos, cfg.CLASS_NAMES
                )
                
                print(f"🎉 mAP计算完成 (阈值={threshold})!")
                print("📊 评估结果:")
                print("-" * 40)
                print(ap_result_str)
                print("-" * 40)
                
                # 显示关键指标
                car_3d_keys = [k for k in ap_dict.keys() if 'Car' in k and '3d' in k.lower()]
                if car_3d_keys:
                    print(f"🚗 Car 3D AP结果:")
                    for key in car_3d_keys:
                        value = ap_dict[key]
                        if isinstance(value, (list, tuple)) and len(value) == 3:
                            print(f"  {key}: Easy={value[0]:.4f}, Moderate={value[1]:.4f}, Hard={value[2]:.4f}")
                        else:
                            print(f"  {key}: {value}")
                
                # 如果有非零结果，保存
                has_nonzero = any(v > 0 for v in ap_dict.values() if isinstance(v, (int, float)))
                if has_nonzero:
                    output_dir = result_file.parent
                    result_path = output_dir / f'mAP_results_fixed_{args.corruption}_thresh_{threshold}.txt'
                    with open(result_path, 'w') as f:
                        f.write(f"KITTI-C {args.corruption.upper()} mAP Results (Fixed, threshold={threshold})\n")
                        f.write("=" * 80 + "\n")
                        f.write(ap_result_str)
                        f.write("\n" + "=" * 80 + "\n")
                    
                    print(f"💾 结果已保存到: {result_path}")
                    break  # 找到有效结果就停止
                
            except Exception as e:
                print(f"❌ mAP计算失败 (阈值={threshold}): {e}")
        
        print(f"\n💡 修复完成！如果仍然是0，可能需要检查模型训练或数据预处理。")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
