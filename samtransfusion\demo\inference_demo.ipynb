{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {"pycharm": {"is_executing": false}}, "outputs": [], "source": ["from mmdet3d.apis import init_detector, inference_detector, show_result_meshlab"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"pycharm": {"is_executing": false}}, "outputs": [], "source": ["config_file = '../configs/second/hv_second_secfpn_6x8_80e_kitti-3d-car.py'\n", "# download the checkpoint from model zoo and put it in `checkpoints/`\n", "checkpoint_file = '../work_dirs/second/epoch_40.pth'"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"pycharm": {"is_executing": false}}, "outputs": [], "source": ["# build the model from a config file and a checkpoint file\n", "model = init_detector(config_file, checkpoint_file, device='cuda:0')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"pycharm": {"is_executing": false}}, "outputs": [], "source": ["# test a single sample\n", "pcd = 'kitti_000008.bin'\n", "result, data = inference_detector(model, pcd)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"pycharm": {"is_executing": false}}, "outputs": [], "source": ["# show the results\n", "out_dir = './'\n", "show_result_meshlab(data, result, out_dir)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.7"}, "pycharm": {"stem_cell": {"cell_type": "raw", "source": [], "metadata": {"collapsed": false}}}}, "nbformat": 4, "nbformat_minor": 4}