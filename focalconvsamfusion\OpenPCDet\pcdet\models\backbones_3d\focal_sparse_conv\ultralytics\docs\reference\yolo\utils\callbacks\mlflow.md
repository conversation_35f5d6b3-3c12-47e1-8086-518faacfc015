---
description: Track model performance and metrics with MLflow in YOLOv5. Use callbacks like on_pretrain_routine_end or on_train_end to log information.
keywords: Ultralytics, YOLO, Utils, MLflow, callbacks, on_pretrain_routine_end, on_train_end, Tracking, Model Management, training
---

## on_pretrain_routine_end
---
### ::: ultralytics.yolo.utils.callbacks.mlflow.on_pretrain_routine_end
<br><br>

## on_fit_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.mlflow.on_fit_epoch_end
<br><br>

## on_train_end
---
### ::: ultralytics.yolo.utils.callbacks.mlflow.on_train_end
<br><br>
