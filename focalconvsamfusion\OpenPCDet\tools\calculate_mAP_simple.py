#!/usr/bin/env python3
"""
简化版mAP计算脚本，专门处理JSON序列化问题
"""

import _init_path
import argparse
import pickle
from pathlib import Path

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import build_dataloader


def parse_args():
    parser = argparse.ArgumentParser(description='Simple mAP calculation')
    parser.add_argument('--result_file', type=str, required=True,
                       help='path to result.pkl file')
    parser.add_argument('--corruption', type=str, default='sun',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml',
                       help='config file')
    
    return parser.parse_args()


def main():
    args = parse_args()
    
    print("🧮 简化版mAP计算")
    print(f"📂 结果文件: {args.result_file}")
    print(f"📂 腐败类型: {args.corruption}")
    
    # 检查结果文件是否存在
    result_file = Path(args.result_file)
    if not result_file.exists():
        print(f"❌ 结果文件不存在: {result_file}")
        return
    
    # 加载配置
    cfg_from_yaml_file(args.cfg_file, cfg)
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption
    
    # 检查info文件
    info_file_path = Path(cfg.DATA_CONFIG.DATA_PATH) / f'kitti_c_infos_val_{args.corruption}.pkl'
    if not info_file_path.exists():
        print(f"❌ Info文件不存在: {info_file_path}")
        print("请先运行 fix_kitti_c_infos.py 生成包含标注的info文件")
        return
    
    print(f"✅ Info文件存在: {info_file_path}")
    
    try:
        # 构建数据集（只需要用于evaluation）
        print("📊 构建数据集...")
        test_set, _, _ = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=1,
            dist=False, workers=1, logger=None, training=False
        )
        
        print(f"✅ 数据集构建成功，样本数量: {len(test_set)}")
        
        # 加载检测结果
        print("📥 加载检测结果...")
        with open(result_file, 'rb') as f:
            det_annos = pickle.load(f)
        
        print(f"✅ 加载了 {len(det_annos)} 个检测结果")
        
        # 计算mAP
        print("📈 计算mAP...")
        print("=" * 80)
        
        try:
            eval_results = test_set.evaluation(det_annos, cfg.CLASS_NAMES)
            
            if isinstance(eval_results, tuple):
                ap_result_str, ap_dict = eval_results
            else:
                ap_result_str = str(eval_results)
                ap_dict = eval_results if isinstance(eval_results, dict) else {}
            
            print("🎉 mAP计算完成!")
            print("📊 评估结果:")
            print("=" * 80)
            print(ap_result_str)
            print("=" * 80)
            
            # 保存文本结果（避免JSON序列化问题）
            output_dir = result_file.parent
            mAP_text_path = output_dir / f'mAP_results_{args.corruption}.txt'
            with open(mAP_text_path, 'w') as f:
                f.write(f"KITTI-C {args.corruption.upper()} mAP Results\n")
                f.write("=" * 80 + "\n")
                f.write(ap_result_str)
                f.write("\n" + "=" * 80 + "\n")
                
                # 添加详细的ap_dict信息
                f.write("\nDetailed Results:\n")
                f.write("-" * 40 + "\n")
                for key, value in ap_dict.items():
                    f.write(f"{key}: {value}\n")
            
            print(f"\n💾 结果已保存到: {mAP_text_path}")
            
            # 提取并显示关键指标
            print(f"\n📋 关键指标 ({args.corruption.upper()}):")
            print("-" * 50)
            
            # 查找AP相关的指标
            ap_keys = [k for k in ap_dict.keys() if 'AP' in k.upper()]
            if ap_keys:
                for key in sorted(ap_keys):
                    value = ap_dict[key]
                    if isinstance(value, (int, float)):
                        print(f"  {key}: {value:.4f}")
                    elif isinstance(value, (list, tuple)) and len(value) > 0:
                        if all(isinstance(v, (int, float)) for v in value):
                            formatted_values = [f'{v:.4f}' for v in value]
                            print(f"  {key}: [{', '.join(formatted_values)}]")
                        else:
                            print(f"  {key}: {value}")
                    else:
                        print(f"  {key}: {value}")
            else:
                # 如果没有找到AP键，显示所有数值型结果
                print("  所有数值结果:")
                for key, value in ap_dict.items():
                    if isinstance(value, (int, float)):
                        print(f"    {key}: {value:.4f}")
            
            # 特别查找Car相关的AP结果
            car_keys = [k for k in ap_dict.keys() if 'Car' in k and 'AP' in k]
            if car_keys:
                print(f"\n🚗 Car类别AP结果:")
                print("-" * 30)
                for key in sorted(car_keys):
                    value = ap_dict[key]
                    if isinstance(value, (int, float)):
                        print(f"  {key}: {value:.4f}")
                    elif isinstance(value, (list, tuple)):
                        formatted_values = [f'{v:.4f}' if isinstance(v, (int, float)) else str(v) for v in value]
                        print(f"  {key}: [{', '.join(formatted_values)}]")
            
            print(f"\n🎯 这就是你要的mAP结果！类似论文中的数值。")
            
        except Exception as e:
            print(f"❌ mAP计算失败: {e}")
            print("🔍 错误详情:")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
