---
description: Learn how to use Ultralytics YOLO's built-in callbacks `on_pretrain_routine_start` and `on_train_epoch_end` for improved training performance.
keywords: Ultralytics, YOLO, callbacks, weights, biases, training
---

## _log_plots
---
### ::: ultralytics.yolo.utils.callbacks.wb._log_plots
<br><br>

## on_pretrain_routine_start
---
### ::: ultralytics.yolo.utils.callbacks.wb.on_pretrain_routine_start
<br><br>

## on_fit_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.wb.on_fit_epoch_end
<br><br>

## on_train_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.wb.on_train_epoch_end
<br><br>

## on_train_end
---
### ::: ultralytics.yolo.utils.callbacks.wb.on_train_end
<br><br>
