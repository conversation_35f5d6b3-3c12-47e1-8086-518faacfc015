#!/usr/bin/env python3
"""
Kornia诊断脚本
用于检查kornia导入问题的详细信息
"""

import sys
import traceback

print("🔍 Kornia 诊断开始...")
print(f"📍 Python版本: {sys.version}")
print(f"📍 Python路径: {sys.executable}")

# 1. 检查基础导入
print("\n1️⃣ 检查基础库导入...")
try:
    import torch
    print(f"✅ PyTorch版本: {torch.__version__}")
except Exception as e:
    print(f"❌ PyTorch导入失败: {e}")
    sys.exit(1)

try:
    import numpy as np
    print(f"✅ NumPy版本: {np.__version__}")
except Exception as e:
    print(f"❌ NumPy导入失败: {e}")

# 2. 检查kornia导入
print("\n2️⃣ 检查kornia导入...")
try:
    import kornia
    print(f"✅ kornia成功导入，版本: {kornia.__version__}")
    print(f"📦 kornia安装路径: {kornia.__file__}")
    
    # 检查kornia的关键功能
    print("\n3️⃣ 检查kornia关键功能...")
    try:
        from kornia.utils import image_to_tensor
        print("✅ kornia.utils.image_to_tensor 可用")
        
        # 测试功能
        import numpy as np
        test_image = np.random.rand(2, 375, 1242, 3).astype(np.float32)
        print(f"🧪 测试图像形状: {test_image.shape}")
        
        tensor_result = image_to_tensor(test_image)
        print(f"✅ 转换成功，结果形状: {tensor_result.shape}")
        
    except Exception as e:
        print(f"❌ kornia.utils.image_to_tensor 测试失败: {e}")
        traceback.print_exc()
        
except ImportError as e:
    print(f"❌ kornia导入失败 (ImportError): {e}")
    traceback.print_exc()
    
    # 尝试更详细的诊断
    print("\n🔍 详细诊断...")
    try:
        import pkg_resources
        installed_packages = [d.project_name for d in pkg_resources.working_set]
        kornia_packages = [pkg for pkg in installed_packages if 'kornia' in pkg.lower()]
        print(f"📦 已安装的kornia相关包: {kornia_packages}")
    except:
        pass
        
except Exception as e:
    print(f"❌ kornia导入失败 (其他错误): {type(e).__name__}: {e}")
    traceback.print_exc()

# 4. 检查可能的依赖问题
print("\n4️⃣ 检查依赖库...")
dependencies = ['opencv-python', 'pillow', 'scipy']
for dep in dependencies:
    try:
        __import__(dep.replace('-', '_'))
        print(f"✅ {dep} 可用")
    except ImportError:
        print(f"⚠️ {dep} 不可用")

print("\n🏁 诊断完成")
