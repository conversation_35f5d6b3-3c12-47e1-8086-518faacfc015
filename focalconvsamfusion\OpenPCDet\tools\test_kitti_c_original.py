#!/usr/bin/env python3
"""
使用原始KittiDataset进行KITTI-C测试
这应该能正确计算mAP，因为原始KittiDataset的evaluation方法是正确的
"""

import _init_path
import argparse
import datetime
import os
import time
import gc
import torch
from pathlib import Path

import numpy as np

from eval_utils import eval_utils
from pcdet.config import cfg, cfg_from_yaml_file, log_config_to_file
from pcdet.datasets import build_dataloader
from pcdet.models import build_network
from pcdet.utils import common_utils


def parse_config():
    parser = argparse.ArgumentParser(description='KITTI-C test with original KittiDataset')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_original.yaml',
                       help='specify the config for testing')
    parser.add_argument('--ckpt', type=str, 
                       default='../output/kitti_models/sam_onlinev3/robofusion_tmux/ckpt/checkpoint_epoch_80.pth',
                       help='checkpoint to start from')
    parser.add_argument('--corruption', type=str, default='sun',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type to test')
    parser.add_argument('--batch_size', type=int, default=1, help='batch size for testing')
    parser.add_argument('--workers', type=int, default=0, help='number of workers for dataloader')
    parser.add_argument('--save_to_file', action='store_true', default=True, help='save results to file')
    
    args = parser.parse_args()
    
    # 加载配置文件
    cfg_from_yaml_file(args.cfg_file, cfg)
    
    # 更新配置以使用对应的info文件
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption

    # 设置标签
    cfg.TAG = f"sam_onlinev3_kitti_c_{args.corruption}_adapted"
    cfg.EXP_GROUP_PATH = 'kitti_models'
    
    np.random.seed(1024)
    
    return args, cfg


def clear_memory():
    """清理GPU内存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()


def main():
    args, cfg = parse_config()
    
    print("🚀 使用适配的KittiCAdaptedDataset进行KITTI-C测试")
    print(f"📂 腐败类型: {args.corruption}")
    print(f"📂 数据路径: {cfg.DATA_CONFIG.DATA_PATH}")
    print(f"📂 Info文件: {cfg.DATA_CONFIG.INFO_PATH['test']}")
    print(f"📂 模型文件: {args.ckpt}")
    
    # 检查info文件是否存在
    info_file_path = Path(cfg.DATA_CONFIG.DATA_PATH) / f'kitti_c_infos_val_{args.corruption}.pkl'
    if not info_file_path.exists():
        print(f"❌ Info文件不存在: {info_file_path}")
        print("请先运行 fix_kitti_c_infos.py 生成包含标注的info文件")
        return
    else:
        print(f"✅ Info文件存在: {info_file_path}")
    
    # 创建输出目录
    output_dir = cfg.ROOT_DIR / 'output' / cfg.EXP_GROUP_PATH / cfg.TAG / 'original_test'
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    log_file = output_dir / f'log_original_{args.corruption}_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    logger = common_utils.create_logger(log_file, rank=cfg.LOCAL_RANK)
    
    logger.info('**********************开始原始KittiDataset测试**********************')
    logger.info(f'腐败类型: {args.corruption}')
    logger.info(f'数据路径: {cfg.DATA_CONFIG.DATA_PATH}')
    logger.info(f'Info文件: {cfg.DATA_CONFIG.INFO_PATH["test"]}')
    logger.info(f'模型文件: {args.ckpt}')
    
    try:
        # 构建数据集
        print("📊 构建数据集...")
        test_set, test_loader, _ = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=args.batch_size,
            dist=False, workers=args.workers, logger=logger, training=False
        )
        
        logger.info(f'数据集样本数量: {len(test_set)}')
        print(f"✅ 数据集构建成功，样本数量: {len(test_set)}")
        
        # 检查数据集类型
        print(f"📋 数据集类型: {type(test_set).__name__}")
        logger.info(f'数据集类型: {type(test_set).__name__}')
        
        # 检查是否有evaluation方法
        if hasattr(test_set, 'evaluation'):
            print("✅ 数据集有evaluation方法")
            logger.info('数据集有evaluation方法')
        else:
            print("❌ 数据集没有evaluation方法")
            logger.error('数据集没有evaluation方法')
            return
        
        # 构建模型
        print("🤖 构建模型...")
        model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=test_set)
        
        # 加载checkpoint
        print("📥 加载模型权重...")
        model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=False)
        model = model.cuda()
        
        # 清理内存
        clear_memory()
        
        # 运行推理和评估
        print(f"🧪 开始推理和评估 {args.corruption.upper()}...")
        logger.info(f'*************** 开始推理和评估 {args.corruption.upper()} *****************')
        
        # 使用eval_utils进行推理和评估
        with torch.no_grad():
            eval_utils.eval_one_epoch(
                cfg, args, model, test_loader, 0, logger, dist_test=False,
                result_dir=output_dir, save_to_file=args.save_to_file
            )
        
        print(f"\n🎉 {args.corruption.upper()} 测试完成!")
        print(f"📂 所有结果保存在: {output_dir}")
        print(f"📄 日志文件: {log_file}")
        
        # 检查是否生成了结果文件
        result_pkl_path = output_dir / 'result.pkl'
        if result_pkl_path.exists():
            print(f"✅ 检测结果文件: {result_pkl_path}")
        else:
            print(f"⚠️ 未找到检测结果文件")
        
        # 查找mAP结果
        print(f"\n📊 查找mAP结果...")
        log_content = log_file.read_text()
        
        # 查找包含AP的行
        ap_lines = []
        for line in log_content.split('\n'):
            if 'AP@' in line or 'bbox AP:' in line or 'bev AP:' in line or '3d AP:' in line:
                ap_lines.append(line.strip())
        
        if ap_lines:
            print("🎯 找到mAP结果:")
            for line in ap_lines:
                print(f"  {line}")
        else:
            print("⚠️ 日志中未找到明显的mAP结果，请检查日志文件")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
