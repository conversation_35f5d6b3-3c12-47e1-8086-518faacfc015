---
description: Learn how to match and fuse object detections for accurate target tracking using Ultralytics' YOLO merge_matches, iou_distance, and embedding_distance.
keywords: Ultralytics, multi-object tracking, object tracking, detection, recognition, matching, indices, iou distance, gate cost matrix, fuse iou, bbox ious
---

## merge_matches
---
### ::: ultralytics.tracker.utils.matching.merge_matches
<br><br>

## _indices_to_matches
---
### ::: ultralytics.tracker.utils.matching._indices_to_matches
<br><br>

## linear_assignment
---
### ::: ultralytics.tracker.utils.matching.linear_assignment
<br><br>

## ious
---
### ::: ultralytics.tracker.utils.matching.ious
<br><br>

## iou_distance
---
### ::: ultralytics.tracker.utils.matching.iou_distance
<br><br>

## v_iou_distance
---
### ::: ultralytics.tracker.utils.matching.v_iou_distance
<br><br>

## embedding_distance
---
### ::: ultralytics.tracker.utils.matching.embedding_distance
<br><br>

## gate_cost_matrix
---
### ::: ultralytics.tracker.utils.matching.gate_cost_matrix
<br><br>

## fuse_motion
---
### ::: ultralytics.tracker.utils.matching.fuse_motion
<br><br>

## fuse_iou
---
### ::: ultralytics.tracker.utils.matching.fuse_iou
<br><br>

## fuse_score
---
### ::: ultralytics.tracker.utils.matching.fuse_score
<br><br>

## bbox_ious
---
### ::: ultralytics.tracker.utils.matching.bbox_ious
<br><br>
