#!/usr/bin/env python3
"""
简单的测试脚本，避免腐败依赖问题
"""

import _init_path
import argparse
import datetime
import os
from pathlib import Path

import numpy as np
import torch

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import build_dataloader
from pcdet.models import build_network
from pcdet.utils import common_utils


def parse_config():
    parser = argparse.ArgumentParser(description='Simple test script')
    parser.add_argument('--cfg_file', type=str, required=True, help='specify the config for testing')
    parser.add_argument('--ckpt', type=str, required=True, help='checkpoint to test')
    parser.add_argument('--batch_size', type=int, default=1, help='batch size for testing')
    parser.add_argument('--workers', type=int, default=4, help='number of workers for dataloader')
    parser.add_argument('--extra_tag', type=str, default='default', help='extra tag for this experiment')

    args = parser.parse_args()

    cfg_from_yaml_file(args.cfg_file, cfg)
    cfg.TAG = Path(args.cfg_file).stem
    cfg.EXP_GROUP_PATH = '/'.join(args.cfg_file.split('/')[1:-1])

    return args, cfg


def test_model(model, test_loader, args, logger):
    """测试模型并输出基本统计信息"""
    
    # 加载模型权重
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=False)
    model.cuda()
    model.eval()

    logger.info('**********************开始测试**********************')
    
    total_samples = 0
    total_detections = 0
    all_scores = []
    
    with torch.no_grad():
        for i, batch_dict in enumerate(test_loader):
            # 限制测试样本数量以快速验证
            if i >= 10:
                break
                
            # 加载数据到GPU（使用统一的load_data_to_gpu函数处理图像维度）
            from pcdet.models import load_data_to_gpu
            load_data_to_gpu(batch_dict)

            # 模型推理
            try:
                pred_dicts, _ = model.forward(batch_dict)
                
                # 分析结果
                frame_id = batch_dict.get('frame_id', [f'sample_{i}'])[0]
                total_samples += 1
                
                if pred_dicts and len(pred_dicts) > 0:
                    pred = pred_dicts[0]
                    pred_boxes = pred.get('pred_boxes', torch.tensor([]))
                    pred_scores = pred.get('pred_scores', torch.tensor([]))
                    pred_labels = pred.get('pred_labels', torch.tensor([]))
                    
                    num_detections = len(pred_boxes)
                    total_detections += num_detections
                    
                    logger.info(f'样本 {i} (ID: {frame_id}): {num_detections} 个检测')
                    
                    if num_detections > 0:
                        scores = pred_scores.cpu().numpy()
                        all_scores.extend(scores.tolist())
                        logger.info(f'  置信度范围: [{scores.min():.3f}, {scores.max():.3f}]')
                        
                        # 检查坐标
                        if len(pred_boxes) > 0:
                            boxes = pred_boxes.cpu().numpy()
                            x_coords = boxes[:, 0]
                            y_coords = boxes[:, 1]
                            z_coords = boxes[:, 2]
                            
                            logger.info(f'  位置范围: X[{x_coords.min():.1f}, {x_coords.max():.1f}], '
                                       f'Y[{y_coords.min():.1f}, {y_coords.max():.1f}], '
                                       f'Z[{z_coords.min():.1f}, {z_coords.max():.1f}]')
                else:
                    logger.info(f'样本 {i} (ID: {frame_id}): ❌ 没有检测结果')
                    
            except Exception as e:
                logger.error(f'样本 {i} 推理失败: {e}')
                continue

    # 输出总结
    logger.info(f'\n📊 测试总结:')
    logger.info(f'  测试样本数: {total_samples}')
    logger.info(f'  总检测数量: {total_detections}')
    logger.info(f'  平均每样本检测数: {total_detections / max(total_samples, 1):.2f}')
    
    if all_scores:
        all_scores = np.array(all_scores)
        logger.info(f'  置信度统计:')
        logger.info(f'    最小值: {all_scores.min():.3f}')
        logger.info(f'    最大值: {all_scores.max():.3f}')
        logger.info(f'    平均值: {all_scores.mean():.3f}')
        logger.info(f'    中位数: {np.median(all_scores):.3f}')
        
        # 高置信度检测统计
        high_conf = all_scores[all_scores > 0.5]
        logger.info(f'    高置信度(>0.5): {len(high_conf)} / {len(all_scores)}')
    
    # 诊断
    if total_detections == 0:
        logger.error('❌ 严重问题：模型没有任何检测输出！')
        logger.error('可能原因：')
        logger.error('  1. 模型权重与配置不匹配')
        logger.error('  2. 数据预处理有问题')
        logger.error('  3. 后处理阈值过高')
        logger.error('  4. 模型推理失败')
    elif total_detections < total_samples * 0.1:
        logger.warning('⚠️ 警告：检测数量过少，可能有问题')
    else:
        logger.info('✅ 模型检测输出正常')

    logger.info('**********************测试完成**********************')


def main():
    args, cfg = parse_config()

    # 创建输出目录
    output_dir = Path('./output') / 'simple_test' / args.extra_tag
    output_dir.mkdir(parents=True, exist_ok=True)

    # 创建日志
    log_file = output_dir / ('log_test_%s.txt' % datetime.datetime.now().strftime('%Y%m%d_%H%M%S'))
    logger = common_utils.create_logger(log_file, rank=0)

    # 记录配置信息
    logger.info('**********************开始日志记录**********************')
    for key, val in vars(args).items():
        logger.info('{:16} {}'.format(key, val))

    # 构建数据加载器
    test_set, test_loader, sampler = build_dataloader(
        dataset_cfg=cfg.DATA_CONFIG,
        class_names=cfg.CLASS_NAMES,
        batch_size=args.batch_size,
        dist=False, workers=args.workers, logger=logger, training=False
    )

    # 构建模型
    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=test_set)
    
    # 测试模型
    test_model(model, test_loader, args, logger)


if __name__ == '__main__':
    main()
