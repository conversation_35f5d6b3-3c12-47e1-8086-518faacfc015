from collections import namedtuple

import numpy as np
import torch

from pcdet.models import build_detector

try:
    import kornia
    KORNIA_AVAILABLE = True
    print(f'✅ kornia successfully imported, version: {kornia.__version__}')
except Exception as e:
    KORNIA_AVAILABLE = False
    print(f'❌ kornia import failed: {type(e).__name__}: {e}')
    print('🔄 Using fallback implementation for image tensor conversion.')

    # 尝试更详细的诊断
    try:
        import sys
        print(f'📍 Python path: {sys.executable}')
        print(f'📦 Available packages in sys.modules: {[pkg for pkg in sys.modules.keys() if "kornia" in pkg.lower()]}')
    except:
        pass



def build_network(model_cfg, num_class, dataset):
    model = build_detector(
        model_cfg=model_cfg, num_class=num_class, dataset=dataset
    )
    return model


def load_data_to_gpu(batch_dict):
    for key, val in batch_dict.items():
        if not isinstance(val, np.ndarray):
            continue
        elif key in ['frame_id', 'metadata', 'calib']:
            continue
        elif key in ['images']:
            if KORNIA_AVAILABLE:
                batch_dict[key] = kornia.image_to_tensor(val).float().cuda().contiguous()
            else:
                # 备用实现：手动转换维度从(B, H, W, C)到(B, C, H, W)
                tensor_val = torch.from_numpy(val).float()
                if len(tensor_val.shape) == 4:  # (B, H, W, C)
                    tensor_val = tensor_val.permute(0, 3, 1, 2)  # 转换为(B, C, H, W)
                batch_dict[key] = tensor_val.cuda().contiguous()
        elif key in ['image_shape']:
            batch_dict[key] = torch.from_numpy(val).int().cuda()
        else:
            batch_dict[key] = torch.from_numpy(val).float().cuda()


def model_fn_decorator():
    ModelReturn = namedtuple('ModelReturn', ['loss', 'tb_dict', 'disp_dict'])

    def model_func(model, batch_dict):
        load_data_to_gpu(batch_dict)
        ret_dict, tb_dict, disp_dict = model(batch_dict)

        loss = ret_dict['loss'].mean()
        if hasattr(model, 'update_global_step'):
            model.update_global_step()
        else:
            model.module.update_global_step()

        return ModelReturn(loss, tb_dict, disp_dict)

    return model_func
