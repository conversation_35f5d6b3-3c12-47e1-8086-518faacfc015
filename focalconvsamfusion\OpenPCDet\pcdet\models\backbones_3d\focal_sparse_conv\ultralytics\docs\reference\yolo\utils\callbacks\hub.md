---
description: Improve YOLOv5 model training with Ultralytics' on-train callbacks. Boost performance on-pretrain-routine-end, model-save, train/predict start.
keywords: Ultralytics, YOLO, callbacks, on_pretrain_routine_end, on_fit_epoch_end, on_train_start, on_val_start, on_predict_start, on_export_start
---

## on_pretrain_routine_end
---
### ::: ultralytics.yolo.utils.callbacks.hub.on_pretrain_routine_end
<br><br>

## on_fit_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.hub.on_fit_epoch_end
<br><br>

## on_model_save
---
### ::: ultralytics.yolo.utils.callbacks.hub.on_model_save
<br><br>

## on_train_end
---
### ::: ultralytics.yolo.utils.callbacks.hub.on_train_end
<br><br>

## on_train_start
---
### ::: ultralytics.yolo.utils.callbacks.hub.on_train_start
<br><br>

## on_val_start
---
### ::: ultralytics.yolo.utils.callbacks.hub.on_val_start
<br><br>

## on_predict_start
---
### ::: ultralytics.yolo.utils.callbacks.hub.on_predict_start
<br><br>

## on_export_start
---
### ::: ultralytics.yolo.utils.callbacks.hub.on_export_start
<br><br>
