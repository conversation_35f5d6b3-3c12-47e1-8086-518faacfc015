---
description: Optimize your PyTorch models with Ultralytics YOLO's torch_utils functions such as ModelEMA, select_device, and is_parallel.
keywords: Ultralytics YOLO, Torch, Utils, Pytorch, Object Detection
---

## ModelEMA
---
### ::: ultralytics.yolo.utils.torch_utils.ModelEMA
<br><br>

## EarlyStopping
---
### ::: ultralytics.yolo.utils.torch_utils.EarlyStopping
<br><br>

## torch_distributed_zero_first
---
### ::: ultralytics.yolo.utils.torch_utils.torch_distributed_zero_first
<br><br>

## smart_inference_mode
---
### ::: ultralytics.yolo.utils.torch_utils.smart_inference_mode
<br><br>

## select_device
---
### ::: ultralytics.yolo.utils.torch_utils.select_device
<br><br>

## time_sync
---
### ::: ultralytics.yolo.utils.torch_utils.time_sync
<br><br>

## fuse_conv_and_bn
---
### ::: ultralytics.yolo.utils.torch_utils.fuse_conv_and_bn
<br><br>

## fuse_deconv_and_bn
---
### ::: ultralytics.yolo.utils.torch_utils.fuse_deconv_and_bn
<br><br>

## model_info
---
### ::: ultralytics.yolo.utils.torch_utils.model_info
<br><br>

## get_num_params
---
### ::: ultralytics.yolo.utils.torch_utils.get_num_params
<br><br>

## get_num_gradients
---
### ::: ultralytics.yolo.utils.torch_utils.get_num_gradients
<br><br>

## model_info_for_loggers
---
### ::: ultralytics.yolo.utils.torch_utils.model_info_for_loggers
<br><br>

## get_flops
---
### ::: ultralytics.yolo.utils.torch_utils.get_flops
<br><br>

## get_flops_with_torch_profiler
---
### ::: ultralytics.yolo.utils.torch_utils.get_flops_with_torch_profiler
<br><br>

## initialize_weights
---
### ::: ultralytics.yolo.utils.torch_utils.initialize_weights
<br><br>

## scale_img
---
### ::: ultralytics.yolo.utils.torch_utils.scale_img
<br><br>

## make_divisible
---
### ::: ultralytics.yolo.utils.torch_utils.make_divisible
<br><br>

## copy_attr
---
### ::: ultralytics.yolo.utils.torch_utils.copy_attr
<br><br>

## get_latest_opset
---
### ::: ultralytics.yolo.utils.torch_utils.get_latest_opset
<br><br>

## intersect_dicts
---
### ::: ultralytics.yolo.utils.torch_utils.intersect_dicts
<br><br>

## is_parallel
---
### ::: ultralytics.yolo.utils.torch_utils.is_parallel
<br><br>

## de_parallel
---
### ::: ultralytics.yolo.utils.torch_utils.de_parallel
<br><br>

## one_cycle
---
### ::: ultralytics.yolo.utils.torch_utils.one_cycle
<br><br>

## init_seeds
---
### ::: ultralytics.yolo.utils.torch_utils.init_seeds
<br><br>

## strip_optimizer
---
### ::: ultralytics.yolo.utils.torch_utils.strip_optimizer
<br><br>

## profile
---
### ::: ultralytics.yolo.utils.torch_utils.profile
<br><br>
