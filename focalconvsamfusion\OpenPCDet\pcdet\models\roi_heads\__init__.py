from .partA2_head import PartA2<PERSON><PERSON><PERSON>
from .pointrcnn_head import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .pvrcnn_head import P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .second_head import SECONDHead
from .voxelrcnn_head import VoxelRCN<PERSON>Head
from .roi_head_template import RoIHeadTemplate


__all__ = {
    'RoIHeadTemplate': RoIHeadTemplate,
    'PartA2FCHead': PartA2FCHead,
    'PVRCNNHead': PVR<PERSON><PERSON><PERSON>Head,
    'SECONDHead': SECONDHead,
    'PointRCNNHead': PointRCNNHead,
    'VoxelRCNNHead': VoxelRCNNHead
}
