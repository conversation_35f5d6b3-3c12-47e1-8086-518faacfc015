#!/usr/bin/env python3
"""
简单的数据集测试脚本
"""

import _init_path
import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent.parent))

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets.kitti.kitti_c_adapted_dataset import KittiCAdaptedDataset


def test_simple():
    """简单测试"""
    
    print("🔍 简单数据集测试")
    
    # 加载配置
    cfg_file = 'cfgs/kitti_models/sam_onlinev3_kitti_c_original.yaml'
    cfg_from_yaml_file(cfg_file, cfg)
    
    # 设置腐败类型
    corruption = 'sun'
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = corruption
    
    print(f"📂 配置文件: {cfg_file}")
    print(f"📂 数据路径: {cfg.DATA_CONFIG.DATA_PATH}")
    print(f"📂 腐败类型: {corruption}")
    
    try:
        # 直接创建数据集实例
        print("\n📊 创建数据集实例...")
        dataset = KittiCAdaptedDataset(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            training=False,
            root_path=Path(cfg.DATA_CONFIG.DATA_PATH),
            logger=None
        )
        
        print(f"✅ 数据集创建成功")
        print(f"📋 数据集类型: {type(dataset).__name__}")
        print(f"📊 样本数量: {len(dataset)}")
        print(f"📂 腐败类型: {dataset.corruption_type}")
        print(f"📂 根路径: {dataset.root_path}")
        print(f"📂 分割路径: {dataset.root_split_path}")
        
        # 检查evaluation方法
        if hasattr(dataset, 'evaluation'):
            print("✅ 数据集有evaluation方法")
        else:
            print("❌ 数据集没有evaluation方法")
        
        # 检查第一个样本的info
        if len(dataset.kitti_infos) > 0:
            first_info = dataset.kitti_infos[0]
            print(f"\n📋 第一个样本info:")
            print(f"  - 字段: {list(first_info.keys())}")
            if 'point_cloud' in first_info:
                pc_info = first_info['point_cloud']
                print(f"  - 点云信息: {pc_info}")
            if 'annos' in first_info:
                annos = first_info['annos']
                print(f"  - 标注字段: {list(annos.keys())}")
                if 'name' in annos:
                    print(f"  - 对象类别: {annos['name']}")
        
        # 测试文件路径
        if len(dataset.kitti_infos) > 0:
            sample_idx = dataset.kitti_infos[0]['point_cloud']['lidar_idx']
            print(f"\n🧪 测试文件路径 (样本ID: {sample_idx}):")
            
            # 测试标定文件
            try:
                calib = dataset.get_calib(sample_idx)
                print(f"  ✅ 标定文件加载成功")
            except Exception as e:
                print(f"  ❌ 标定文件加载失败: {e}")
            
            # 测试点云文件
            try:
                points = dataset.get_lidar(sample_idx)
                print(f"  ✅ 点云文件加载成功，形状: {points.shape}")
            except Exception as e:
                print(f"  ❌ 点云文件加载失败: {e}")
            
            # 测试图像文件
            try:
                img_path = dataset.get_image(sample_idx)
                print(f"  ✅ 图像文件路径: {img_path}")
            except Exception as e:
                print(f"  ❌ 图像文件加载失败: {e}")
        
        # 测试加载第一个样本
        print(f"\n🧪 测试加载第一个样本...")
        try:
            first_sample = dataset[0]
            print("✅ 成功加载第一个样本")
            print(f"📋 样本字段: {list(first_sample.keys())}")
            
            # 检查关键字段
            if 'points' in first_sample:
                points = first_sample['points']
                print(f"📊 点云形状: {points.shape}")
            
            if 'gt_boxes' in first_sample:
                gt_boxes = first_sample['gt_boxes']
                print(f"📊 GT boxes形状: {gt_boxes.shape}")
            
            if 'gt_names' in first_sample:
                gt_names = first_sample['gt_names']
                print(f"📊 GT类别: {gt_names}")
            
            print("\n🎉 数据集测试完全成功！")
            return True
                
        except Exception as e:
            print(f"❌ 加载第一个样本失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = test_simple()
    if success:
        print("\n✅ 简单测试通过，数据集工作正常")
    else:
        print("\n❌ 简单测试失败，需要修复问题")
