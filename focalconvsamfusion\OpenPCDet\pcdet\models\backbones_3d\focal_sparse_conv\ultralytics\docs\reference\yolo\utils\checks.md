---
description: 'Check functions for YOLO utils: image size, version, font, requirements, filename suffix, YAML file, YOLO, and Git version.'
keywords: YOLO, Ultralytics, Utils, Checks, image sizing, version updates, font compatibility, Python requirements, file suffixes, YAML syntax, image showing, AMP
---

## is_ascii
---
### ::: ultralytics.yolo.utils.checks.is_ascii
<br><br>

## check_imgsz
---
### ::: ultralytics.yolo.utils.checks.check_imgsz
<br><br>

## check_version
---
### ::: ultralytics.yolo.utils.checks.check_version
<br><br>

## check_latest_pypi_version
---
### ::: ultralytics.yolo.utils.checks.check_latest_pypi_version
<br><br>

## check_pip_update_available
---
### ::: ultralytics.yolo.utils.checks.check_pip_update_available
<br><br>

## check_font
---
### ::: ultralytics.yolo.utils.checks.check_font
<br><br>

## check_python
---
### ::: ultralytics.yolo.utils.checks.check_python
<br><br>

## check_requirements
---
### ::: ultralytics.yolo.utils.checks.check_requirements
<br><br>

## check_suffix
---
### ::: ultralytics.yolo.utils.checks.check_suffix
<br><br>

## check_yolov5u_filename
---
### ::: ultralytics.yolo.utils.checks.check_yolov5u_filename
<br><br>

## check_file
---
### ::: ultralytics.yolo.utils.checks.check_file
<br><br>

## check_yaml
---
### ::: ultralytics.yolo.utils.checks.check_yaml
<br><br>

## check_imshow
---
### ::: ultralytics.yolo.utils.checks.check_imshow
<br><br>

## check_yolo
---
### ::: ultralytics.yolo.utils.checks.check_yolo
<br><br>

## check_amp
---
### ::: ultralytics.yolo.utils.checks.check_amp
<br><br>

## git_describe
---
### ::: ultralytics.yolo.utils.checks.git_describe
<br><br>

## print_args
---
### ::: ultralytics.yolo.utils.checks.print_args
<br><br>
