#!/usr/bin/env python3
"""
Kornia版本兼容性测试脚本
测试项目中使用的kornia功能在不同版本下的兼容性
"""

import sys
import traceback
import numpy as np

def test_kornia_functions():
    """测试项目中实际使用的kornia功能"""
    
    print("🔍 测试kornia功能兼容性...")
    
    try:
        import kornia
        print(f"✅ kornia版本: {kornia.__version__}")
        
        # 测试1: image_to_tensor (OpenPCDet使用)
        print("\n1️⃣ 测试 kornia.utils.image_to_tensor")
        try:
            from kornia.utils import image_to_tensor
            test_image = np.random.rand(2, 375, 1242, 3).astype(np.float32)
            result = image_to_tensor(test_image)
            print(f"✅ image_to_tensor 成功，输入: {test_image.shape}, 输出: {result.shape}")
        except Exception as e:
            print(f"❌ image_to_tensor 失败: {e}")
            
        # 测试2: normalize (CenterPoint使用)
        print("\n2️⃣ 测试 kornia normalize")
        try:
            version_num = float(kornia.__version__.lstrip('0.'))
            if version_num < 3:
                from kornia.color.normalize import normalize
                print(f"✅ 使用 kornia.color.normalize (版本 < 0.3)")
            else:
                from kornia.enhance.normalize import normalize
                print(f"✅ 使用 kornia.enhance.normalize (版本 >= 0.3)")
        except Exception as e:
            print(f"❌ normalize 导入失败: {e}")
            
        # 测试3: 3D网格和变换 (CenterPoint使用)
        print("\n3️⃣ 测试 kornia 3D功能")
        try:
            from kornia.utils.grid import create_meshgrid3d
            from kornia.geometry.linalg import transform_points
            print("✅ 3D网格和变换功能可用")
        except Exception as e:
            print(f"❌ 3D功能失败: {e}")
            
        # 测试4: 检查是否有torch.linalg.inv_ex依赖
        print("\n4️⃣ 测试 torch.linalg.inv_ex 依赖")
        try:
            from torch.linalg import inv_ex
            print("✅ torch.linalg.inv_ex 可用")
        except ImportError:
            print("❌ torch.linalg.inv_ex 不可用 (PyTorch < 1.11)")
            
        return True
        
    except ImportError as e:
        print(f"❌ kornia导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        traceback.print_exc()
        return False

def check_pytorch_version():
    """检查PyTorch版本"""
    try:
        import torch
        print(f"📍 PyTorch版本: {torch.__version__}")
        
        # 检查关键功能
        major, minor = torch.__version__.split('.')[:2]
        major, minor = int(major), int(minor)
        
        if major == 1 and minor >= 11:
            print("✅ PyTorch版本支持最新kornia功能")
        elif major == 1 and minor >= 8:
            print("⚠️ PyTorch版本较旧，可能需要兼容版本的kornia")
        else:
            print("❌ PyTorch版本过旧")
            
    except Exception as e:
        print(f"❌ PyTorch检查失败: {e}")

def main():
    print("🚀 Kornia兼容性测试开始")
    print("=" * 50)
    
    check_pytorch_version()
    print()
    
    success = test_kornia_functions()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 兼容性测试完成")
    else:
        print("❌ 发现兼容性问题")
        
    print("\n💡 建议:")
    print("- 如果所有测试通过，当前kornia版本可用")
    print("- 如果image_to_tensor失败，尝试 kornia==0.6.0")
    print("- 如果3D功能失败，尝试 kornia==0.5.0")
    print("- 如果torch.linalg.inv_ex不可用，必须使用 kornia<=0.6.0")

if __name__ == "__main__":
    main()
