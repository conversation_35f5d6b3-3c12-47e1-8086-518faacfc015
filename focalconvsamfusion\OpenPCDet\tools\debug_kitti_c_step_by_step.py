#!/usr/bin/env python3
"""
逐步调试KITTI-C测试脚本
找出在哪一步卡住了
"""

import _init_path
import argparse
import datetime
import os
import re
import time
import gc
import torch
from pathlib import Path

import numpy as np

from eval_utils import eval_utils
from pcdet.config import cfg, cfg_from_yaml_file, log_config_to_file
from pcdet.datasets import build_dataloader
from pcdet.models import build_network
from pcdet.utils import common_utils

def parse_config():
    print("🔧 步骤1: 解析配置...")
    parser = argparse.ArgumentParser(description='Step by step KITTI-C debug')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml',
                       help='specify the config for testing')
    parser.add_argument('--ckpt', type=str, 
                       default='../output/kitti_models/sam_onlinev3/robofusion_tmux/ckpt/checkpoint_epoch_80.pth',
                       help='checkpoint to start from')
    parser.add_argument('--corruption', type=str, default='fog',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type to test')
    parser.add_argument('--batch_size', type=int, default=1, help='batch size for testing')
    parser.add_argument('--workers', type=int, default=0, help='number of workers for dataloader')
    
    args = parser.parse_args()
    print(f"✅ 参数解析完成: corruption={args.corruption}")
    
    # 加载配置文件
    print("🔧 步骤2: 加载配置文件...")
    cfg_from_yaml_file(args.cfg_file, cfg)
    print(f"✅ 配置文件加载完成: {args.cfg_file}")
    
    # 更新配置以使用对应的info文件
    print("🔧 步骤3: 更新配置...")
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption
    
    # 设置标签
    cfg.TAG = f"sam_onlinev3_kitti_c_{args.corruption}_debug"
    cfg.EXP_GROUP_PATH = 'kitti_models'
    
    np.random.seed(1024)
    print("✅ 配置更新完成")
    
    return args, cfg

def check_dataset_files(data_path, corruption_type):
    """检查数据集文件是否存在"""
    print("🔧 步骤4: 检查数据集文件...")
    
    data_path = Path(data_path)
    corruption_path = data_path / corruption_type
    
    print(f"🔍 检查 {corruption_type} 数据集文件...")
    
    # 检查目录结构
    dirs_to_check = ['images/val', 'velodyne/val', 'calib', 'labels/val']
    for dir_name in dirs_to_check:
        dir_path = corruption_path / dir_name
        if dir_path.exists():
            file_count = len(list(dir_path.glob('*')))
            print(f"  ✅ {dir_name}: {file_count} 个文件")
        else:
            print(f"  ❌ {dir_name}: 目录不存在")
            return False
    
    # 检查info文件
    info_file = data_path / f'kitti_c_infos_val_{corruption_type}.pkl'
    if info_file.exists():
        print(f"  ✅ Info文件: {info_file.name}")
    else:
        print(f"  ❌ Info文件: {info_file.name} 不存在")
        return False
    
    print("✅ 数据集文件检查完成")
    return True

def main():
    try:
        args, cfg = parse_config()
        
        print("🔧 步骤5: 显示配置信息...")
        print(f"📂 腐败类型: {args.corruption}")
        print(f"📂 数据路径: {cfg.DATA_CONFIG.DATA_PATH}")
        print(f"📂 Info文件: {cfg.DATA_CONFIG.INFO_PATH['test']}")
        
        # 检查数据集文件
        print("🔧 步骤6: 检查数据集...")
        if not check_dataset_files(cfg.DATA_CONFIG.DATA_PATH, args.corruption):
            print("❌ 数据集检查失败，请先修复数据集问题")
            return
        
        # 创建输出目录
        print("🔧 步骤7: 创建输出目录...")
        output_dir = cfg.ROOT_DIR / 'output' / cfg.EXP_GROUP_PATH / cfg.TAG / 'debug_test'
        output_dir.mkdir(parents=True, exist_ok=True)
        
        eval_output_dir = output_dir / 'eval'
        
        # 从checkpoint文件名提取epoch信息
        num_list = re.findall(r'\d+', args.ckpt) if args.ckpt is not None else []
        epoch_id = num_list[-1] if num_list.__len__() > 0 else 'no_number'
        eval_output_dir = eval_output_dir / ('epoch_%s' % epoch_id) / cfg.DATA_CONFIG.DATA_SPLIT['test']
        
        eval_output_dir.mkdir(parents=True, exist_ok=True)
        print(f"✅ 输出目录创建完成: {eval_output_dir}")
        
        # 设置日志
        print("🔧 步骤8: 设置日志...")
        log_file = eval_output_dir / ('log_debug_%s' % datetime.datetime.now().strftime('%Y%m%d-%H%M%S'))
        logger = common_utils.create_logger(log_file, rank=cfg.LOCAL_RANK)
        print(f"✅ 日志设置完成: {log_file}")
        
        # 记录配置
        print("🔧 步骤9: 记录配置...")
        logger.info('**********************Start Debug KITTI-C Test**********************')
        logger.info(f'Corruption type: {args.corruption}')
        logger.info(f'Data path: {cfg.DATA_CONFIG.DATA_PATH}')
        logger.info(f'Info file: {cfg.DATA_CONFIG.INFO_PATH["test"]}')
        
        for key, val in vars(cfg).items():
            logger.info('{:16} {}'.format(key, val))
        log_config_to_file(cfg, logger=logger)
        print("✅ 配置记录完成")
        
        # 构建数据集 - 这里可能是卡住的地方
        print("🔧 步骤10: 构建数据集...")
        print("⏳ 这一步可能需要一些时间，请耐心等待...")
        
        start_time = time.time()
        test_set, test_loader, sampler = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=args.batch_size,
            dist=False, workers=args.workers, logger=logger, training=False
        )
        end_time = time.time()
        
        print(f"✅ 数据集构建完成，耗时: {end_time - start_time:.2f}秒")
        print(f"📊 数据集大小: {len(test_set)} 个样本")
        
        # 构建模型
        print("🔧 步骤11: 构建模型...")
        start_time = time.time()
        model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=test_set)
        end_time = time.time()
        print(f"✅ 模型构建完成，耗时: {end_time - start_time:.2f}秒")
        
        # 加载权重
        print("🔧 步骤12: 加载模型权重...")
        start_time = time.time()
        model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=False)
        model.cuda()
        model.eval()
        end_time = time.time()
        print(f"✅ 模型权重加载完成，耗时: {end_time - start_time:.2f}秒")
        
        print("🎉 所有步骤完成！模型已准备好进行推理")
        print(f"📊 准备测试 {len(test_set)} 个 {args.corruption} 样本")
        
        # 简单测试一个batch
        print("🔧 步骤13: 测试一个batch...")
        with torch.no_grad():
            for i, batch_dict in enumerate(test_loader):
                print(f"  📦 Batch {i+1}: {batch_dict['batch_size']} 个样本")
                if i >= 0:  # 只测试第一个batch
                    break
        
        print("✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 在某个步骤发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
