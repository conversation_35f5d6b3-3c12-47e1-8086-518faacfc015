---
description: Improve YOLOv5 training with <PERSON>, a powerful logging tool. Track metrics like images, plots, and epochs for better model performance.
keywords: Ultralytics, YOLO, Neptune, Callbacks, log scalars, log images, log plots, training, validation
---

## _log_scalars
---
### ::: ultralytics.yolo.utils.callbacks.neptune._log_scalars
<br><br>

## _log_images
---
### ::: ultralytics.yolo.utils.callbacks.neptune._log_images
<br><br>

## _log_plot
---
### ::: ultralytics.yolo.utils.callbacks.neptune._log_plot
<br><br>

## on_pretrain_routine_start
---
### ::: ultralytics.yolo.utils.callbacks.neptune.on_pretrain_routine_start
<br><br>

## on_train_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.neptune.on_train_epoch_end
<br><br>

## on_fit_epoch_end
---
### ::: ultralytics.yolo.utils.callbacks.neptune.on_fit_epoch_end
<br><br>

## on_val_end
---
### ::: ultralytics.yolo.utils.callbacks.neptune.on_val_end
<br><br>

## on_train_end
---
### ::: ultralytics.yolo.utils.callbacks.neptune.on_train_end
<br><br>
