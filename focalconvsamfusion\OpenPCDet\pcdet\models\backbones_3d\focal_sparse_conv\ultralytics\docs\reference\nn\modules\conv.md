---
description: Explore convolutional neural network modules & techniques such as LightConv, DWConv, ConvTranspose, GhostConv, CBAM & autopad with Ultralytics Docs.
keywords: Ultralytics, Convolutional Neural Network, Conv2, DWConv, ConvTranspose, GhostConv, ChannelAttention, CBAM, autopad
---

## Conv
---
### ::: ultralytics.nn.modules.conv.Conv
<br><br>

## Conv2
---
### ::: ultralytics.nn.modules.conv.Conv2
<br><br>

## LightConv
---
### ::: ultralytics.nn.modules.conv.LightConv
<br><br>

## DWConv
---
### ::: ultralytics.nn.modules.conv.DWConv
<br><br>

## DWConvTranspose2d
---
### ::: ultralytics.nn.modules.conv.DWConvTranspose2d
<br><br>

## ConvTranspose
---
### ::: ultralytics.nn.modules.conv.ConvTranspose
<br><br>

## Focus
---
### ::: ultralytics.nn.modules.conv.Focus
<br><br>

## GhostConv
---
### ::: ultralytics.nn.modules.conv.GhostConv
<br><br>

## RepConv
---
### ::: ultralytics.nn.modules.conv.RepConv
<br><br>

## ChannelAttention
---
### ::: ultralytics.nn.modules.conv.ChannelAttention
<br><br>

## SpatialAttention
---
### ::: ultralytics.nn.modules.conv.SpatialAttention
<br><br>

## CBAM
---
### ::: ultralytics.nn.modules.conv.CBAM
<br><br>

## Concat
---
### ::: ultralytics.nn.modules.conv.Concat
<br><br>

## autopad
---
### ::: ultralytics.nn.modules.conv.autopad
<br><br>
