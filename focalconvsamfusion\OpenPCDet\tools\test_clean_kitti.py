#!/usr/bin/env python3
"""
测试模型在干净KITTI数据上的表现
"""

import _init_path
import argparse
import datetime
import glob
import os
from pathlib import Path

import torch
import torch.distributed as dist
from tensorboardX import SummaryWriter

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import build_dataloader
from pcdet.models import build_network
from pcdet.utils import common_utils


def parse_config():
    parser = argparse.ArgumentParser(description='arg parser')
    parser.add_argument('--cfg_file', type=str, default='cfgs/kitti_models/sam_onlinev3.yaml',
                        help='specify the config for training')
    parser.add_argument('--ckpt', type=str, default=None, help='checkpoint to start from')
    parser.add_argument('--workers', type=int, default=4, help='number of workers for dataloader')

    args = parser.parse_args()

    cfg_from_yaml_file(args.cfg_file, cfg)

    return args, cfg


def main():
    args, cfg = parse_config()

    if args.ckpt is not None:
        args.ckpt = os.path.abspath(args.ckpt)

    output_dir = Path('./output') / 'clean_kitti_test'
    output_dir.mkdir(parents=True, exist_ok=True)

    # log to file
    log_file = output_dir / ('log_clean_test_%s.txt' % datetime.datetime.now().strftime('%Y%m%d_%H%M%S'))
    logger = common_utils.create_logger(log_file, rank=0)

    # log to console
    logger.info('**********************Start logging**********************')
    gpu_list = os.environ['CUDA_VISIBLE_DEVICES'] if 'CUDA_VISIBLE_DEVICES' in os.environ.keys() else 'ALL'
    logger.info('CUDA_VISIBLE_DEVICES=%s' % gpu_list)

    for key, val in vars(args).items():
        logger.info('{:16} {}'.format(key, val))
    common_utils.log_config_to_file(cfg, logger=logger)

    # 创建测试数据集 - 使用原始KITTI配置
    test_set, test_loader, sampler = build_dataloader(
        dataset_cfg=cfg.DATA_CONFIG,
        class_names=cfg.CLASS_NAMES,
        batch_size=1,
        dist=False, workers=args.workers, logger=logger, training=False
    )

    # 构建模型
    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=test_set)
    
    if args.ckpt is not None:
        model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=False)
    
    model.cuda()
    model.eval()

    logger.info('**********************Start evaluation**********************')
    
    # 测试前几个样本
    test_samples = 5
    total_detections = 0
    
    with torch.no_grad():
        for i, batch_dict in enumerate(test_loader):
            if i >= test_samples:
                break
                
            # 加载数据到GPU
            for key, val in batch_dict.items():
                if not isinstance(val, np.ndarray):
                    continue
                if key in ['frame_id', 'metadata', 'calib']:
                    continue
                batch_dict[key] = torch.from_numpy(val).float().cuda()

            # 模型推理
            pred_dicts, _ = model.forward(batch_dict)
            
            # 分析结果
            frame_id = batch_dict['frame_id'][0] if 'frame_id' in batch_dict else f'sample_{i}'
            
            if pred_dicts and len(pred_dicts) > 0:
                pred = pred_dicts[0]
                pred_boxes = pred.get('pred_boxes', torch.tensor([]))
                pred_scores = pred.get('pred_scores', torch.tensor([]))
                pred_labels = pred.get('pred_labels', torch.tensor([]))
                
                num_detections = len(pred_boxes)
                total_detections += num_detections
                
                logger.info(f'样本 {i} (ID: {frame_id}):')
                logger.info(f'  检测数量: {num_detections}')
                
                if num_detections > 0:
                    logger.info(f'  置信度范围: [{pred_scores.min():.3f}, {pred_scores.max():.3f}]')
                    logger.info(f'  平均置信度: {pred_scores.mean():.3f}')
                    
                    # 检查坐标
                    if len(pred_boxes) > 0:
                        x_coords = pred_boxes[:, 0]
                        y_coords = pred_boxes[:, 1]
                        z_coords = pred_boxes[:, 2]
                        
                        logger.info(f'  位置范围:')
                        logger.info(f'    X: [{x_coords.min():.2f}, {x_coords.max():.2f}]')
                        logger.info(f'    Y: [{y_coords.min():.2f}, {y_coords.max():.2f}]')
                        logger.info(f'    Z: [{z_coords.min():.2f}, {z_coords.max():.2f}]')
                else:
                    logger.info(f'  ❌ 没有检测结果')
            else:
                logger.info(f'样本 {i} (ID: {frame_id}): ❌ 模型没有输出')

    logger.info(f'\n📊 测试总结:')
    logger.info(f'  测试样本数: {test_samples}')
    logger.info(f'  总检测数量: {total_detections}')
    logger.info(f'  平均每样本检测数: {total_detections / test_samples:.2f}')
    
    if total_detections == 0:
        logger.info(f'  ❌ 严重问题：模型在干净KITTI数据上也没有检测输出！')
        logger.info(f'  这说明模型本身有问题，不是KITTI-C数据的问题')
    else:
        logger.info(f'  ✅ 模型在干净KITTI数据上正常工作')
        logger.info(f'  问题可能出在KITTI-C数据处理上')

    logger.info('**********************End evaluation**********************')


if __name__ == '__main__':
    main()
