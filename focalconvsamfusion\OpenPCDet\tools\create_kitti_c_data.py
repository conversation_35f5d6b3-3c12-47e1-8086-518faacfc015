#!/usr/bin/env python3
"""
创建KITTI-C数据集的预处理文件
专门为路径 /data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C 设计
"""

import pickle
import numpy as np
from pathlib import Path
import sys
import os

# 添加项目路径
sys.path.append(str(Path(__file__).resolve().parent.parent))

try:
    from pcdet.config import cfg, cfg_from_yaml_file
    from pcdet.utils import common_utils
except ImportError:
    print("Warning: Could not import pcdet modules, using basic functionality")


def create_kitti_c_infos(data_path, corruption_type='fog'):
    """创建KITTI-C数据集的info文件"""

    print(f'Creating KITTI-C info files for corruption type: {corruption_type}')
    print(f'Data path: {data_path}')

    # 获取样本列表
    corruption_path = data_path / corruption_type

    # KITTI-C的结构是: corruption_type/images/val/*.png
    image_dir = corruption_path / 'images' / 'val'
    velodyne_dir = corruption_path / 'velodyne' / 'val'

    # 如果val子目录不存在，尝试直接目录
    if not image_dir.exists():
        image_dir = corruption_path / 'images'
    if not velodyne_dir.exists():
        velodyne_dir = corruption_path / 'velodyne'

    if not image_dir.exists() or not velodyne_dir.exists():
        raise FileNotFoundError(f"Cannot find images or velodyne directory in {corruption_path}")

    # 获取所有样本ID（保留完整的文件名，包括后缀）
    sample_ids = [f.stem for f in image_dir.glob('*.png')]
    sample_ids.sort()

    print(f"Sample files found: {sample_ids[:5]}...")  # 显示前5个样本用于调试

    print(f'Found {len(sample_ids)} samples')

    # 分割训练集和验证集 (80/20)
    split_idx = int(len(sample_ids) * 0.8)
    train_sample_ids = sample_ids[:split_idx]
    val_sample_ids = sample_ids[split_idx:]

    print(f'Train samples: {len(train_sample_ids)}')
    print(f'Val samples: {len(val_sample_ids)}')

    # 创建训练集info
    train_infos = []
    for i, sample_id in enumerate(train_sample_ids):
        if i % 100 == 0:
            print(f'Processing train sample {i}/{len(train_sample_ids)}')
        info = create_sample_info(sample_id, corruption_path)
        train_infos.append(info)

    # 创建验证集info
    val_infos = []
    for i, sample_id in enumerate(val_sample_ids):
        if i % 100 == 0:
            print(f'Processing val sample {i}/{len(val_sample_ids)}')
        info = create_sample_info(sample_id, corruption_path)
        val_infos.append(info)

    # 保存info文件
    train_filename = data_path / 'kitti_c_infos_train.pkl'
    val_filename = data_path / 'kitti_c_infos_val.pkl'

    with open(train_filename, 'wb') as f:
        pickle.dump(train_infos, f)
    print(f'Train info saved to: {train_filename}')

    with open(val_filename, 'wb') as f:
        pickle.dump(val_infos, f)
    print(f'Val info saved to: {val_filename}')

    return train_infos, val_infos


def create_sample_info(sample_id, corruption_path):
    """为单个样本创建info结构"""

    # 检查文件是否存在 - 首先尝试val子目录
    image_file = corruption_path / 'images' / 'val' / f'{sample_id}.png'
    velodyne_file = corruption_path / 'velodyne' / 'val' / f'{sample_id}.npy'

    # 如果val子目录不存在，尝试直接目录
    if not image_file.exists():
        image_file = corruption_path / 'images' / f'{sample_id}.png'
    if not velodyne_file.exists():
        velodyne_file = corruption_path / 'velodyne' / f'{sample_id}.npy'

    if not image_file.exists():
        raise FileNotFoundError(f"Image file not found: {image_file}")
    if not velodyne_file.exists():
        raise FileNotFoundError(f"Velodyne file not found: {velodyne_file}")
    
    # 获取图像尺寸
    try:
        from PIL import Image
        with Image.open(image_file) as img:
            image_shape = [img.height, img.width]
    except Exception:
        # 默认KITTI尺寸
        image_shape = [375, 1242]

    # 获取点云信息
    try:
        points = np.load(velodyne_file)
        num_points = len(points)
        num_features = points.shape[1] if points.ndim > 1 else 4
    except Exception:
        num_points = 0
        num_features = 4
    
    info = {
        'point_cloud': {
            'num_features': num_features,
            'lidar_idx': sample_id,
            'num_points': num_points
        },
        'image': {
            'image_idx': sample_id,
            'image_shape': image_shape
        },
        'calib': {
            # 使用默认标定参数（如果没有标定文件）
            'P2': np.array([[721.5377, 0., 609.5593, 44.85728],
                           [0., 721.5377, 172.854, 0.2163791],
                           [0., 0., 1., 0.002745884]], dtype=np.float32),
            'R0_rect': np.eye(4, dtype=np.float32),
            'Tr_velo_to_cam': np.array([[7.533745e-03, -9.999714e-01, -6.166020e-04, -4.069766e-03],
                                       [1.480249e-02, 7.280733e-04, -9.998902e-01, -7.631618e-02],
                                       [9.998621e-01, 7.523790e-03, 1.480755e-02, -2.717806e-01],
                                       [0., 0., 0., 1.]], dtype=np.float32)
        },
        'annos': {
            'name': np.array([], dtype='<U10'),
            'truncated': np.array([], dtype=np.float32),
            'occluded': np.array([], dtype=np.int32),
            'alpha': np.array([], dtype=np.float32),
            'bbox': np.zeros([0, 4], dtype=np.float32),
            'dimensions': np.zeros([0, 3], dtype=np.float32),
            'location': np.zeros([0, 3], dtype=np.float32),
            'rotation_y': np.array([], dtype=np.float32),
            'score': np.array([], dtype=np.float32),
            'difficulty': np.array([], dtype=np.int32),
            'num_points_in_gt': np.array([], dtype=np.int32)
        }
    }
    
    return info


def create_dummy_dbinfos(save_path):
    """创建一个空的数据库info文件，避免gt_sampling错误"""
    
    dummy_dbinfos = {
        'Car': [],
        'Pedestrian': [],
        'Cyclist': []
    }
    
    dbinfo_filename = save_path / 'kitti_c_dbinfos_train.pkl'
    with open(dbinfo_filename, 'wb') as f:
        pickle.dump(dummy_dbinfos, f)
    
    print(f'Dummy dbinfos saved to: {dbinfo_filename}')


def main():
    """主函数 - 为指定路径创建KITTI-C预处理文件"""

    # 固定的数据路径
    data_path = Path('/data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C')

    print("=== KITTI-C数据集预处理工具 ===")
    print(f"数据路径: {data_path}")

    # 检查数据路径是否存在
    if not data_path.exists():
        print(f"错误: 数据路径不存在: {data_path}")
        return

    # 检查可用的腐败类型
    available_corruptions = []
    for corruption in ['fog', 'rain', 'snow', 'sun']:
        corruption_path = data_path / corruption
        if corruption_path.exists():
            available_corruptions.append(corruption)
            print(f"找到腐败类型: {corruption}")

    if not available_corruptions:
        print("错误: 没有找到任何腐败类型目录")
        return

    # 为每种腐败类型创建info文件
    for corruption_type in available_corruptions:
        print(f"\n=== 处理腐败类型: {corruption_type} ===")
        try:
            train_infos, val_infos = create_kitti_c_infos(data_path, corruption_type)
            print(f'{corruption_type}: 创建了 {len(train_infos)} 个训练样本和 {len(val_infos)} 个验证样本')
        except Exception as e:
            print(f'处理 {corruption_type} 时出错: {e}')
            continue

    # 创建dummy数据库文件
    print("\n=== 创建数据库文件 ===")
    try:
        create_dummy_dbinfos(data_path)
        print("数据库文件创建成功")
    except Exception as e:
        print(f'创建数据库文件时出错: {e}')

    print("\n=== KITTI-C数据预处理完成! ===")


def simple_main():
    """简化版主函数 - 只处理fog类型"""

    # 固定的数据路径
    data_path = Path('/data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C')
    corruption_type = 'fog'  # 默认使用fog

    print("=== KITTI-C数据集预处理工具 (简化版) ===")
    print(f"数据路径: {data_path}")
    print(f"腐败类型: {corruption_type}")

    try:
        # 创建info文件
        train_infos, val_infos = create_kitti_c_infos(data_path, corruption_type)

        # 创建dummy数据库文件
        create_dummy_dbinfos(data_path)

        print('KITTI-C数据预处理完成!')
        print(f'创建了 {len(train_infos)} 个训练样本和 {len(val_infos)} 个验证样本')

    except Exception as e:
        print(f'预处理过程中出错: {e}')
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    # 使用简化版本，只处理fog类型
    simple_main()

    # 如果需要处理所有腐败类型，取消注释下面这行
    # main()
