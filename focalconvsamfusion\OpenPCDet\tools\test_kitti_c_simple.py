#!/usr/bin/env python3
"""
简化的KITTI-C测试脚本
专门处理文件路径匹配问题
"""

import _init_path
import argparse
import datetime
import os
import re
import time
import gc
import torch
from pathlib import Path

import numpy as np

from eval_utils import eval_utils
from pcdet.config import cfg, cfg_from_yaml_file, log_config_to_file
from pcdet.datasets import build_dataloader
from pcdet.models import build_network
from pcdet.utils import common_utils


def parse_config():
    parser = argparse.ArgumentParser(description='Simple KITTI-C test with path fixes')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml',
                       help='specify the config for testing')
    parser.add_argument('--ckpt', type=str, 
                       default='../output/kitti_models/sam_onlinev3/robofusion_tmux/ckpt/checkpoint_epoch_80.pth',
                       help='checkpoint to start from')
    parser.add_argument('--corruption', type=str, default='fog',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type to test')
    parser.add_argument('--batch_size', type=int, default=1, help='batch size for testing')
    parser.add_argument('--workers', type=int, default=1, help='number of workers for dataloader')
    
    args = parser.parse_args()
    
    # 加载配置文件
    cfg_from_yaml_file(args.cfg_file, cfg)
    
    # 更新配置以使用对应的info文件
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption
    
    # 设置标签
    cfg.TAG = f"sam_onlinev3_kitti_c_{args.corruption}_simple"
    cfg.EXP_GROUP_PATH = 'kitti_models'
    
    np.random.seed(1024)
    
    return args, cfg


def check_dataset_files(data_path, corruption_type):
    """检查数据集文件是否存在"""
    
    data_path = Path(data_path)
    corruption_path = data_path / corruption_type
    
    print(f"🔍 检查 {corruption_type} 数据集文件...")
    
    # 检查目录结构
    dirs_to_check = ['images/val', 'velodyne/val', 'calib', 'labels/val']
    for dir_name in dirs_to_check:
        dir_path = corruption_path / dir_name
        if dir_path.exists():
            file_count = len(list(dir_path.glob('*')))
            print(f"  ✅ {dir_name}: {file_count} 个文件")
        else:
            print(f"  ❌ {dir_name}: 目录不存在")
    
    # 检查info文件
    info_file = data_path / f'kitti_c_infos_val_{corruption_type}.pkl'
    if info_file.exists():
        print(f"  ✅ Info文件: {info_file.name}")
    else:
        print(f"  ❌ Info文件: {info_file.name} 不存在")
        return False
    
    # 检查文件命名格式
    images_val_dir = corruption_path / 'images' / 'val'
    if images_val_dir.exists():
        image_files = list(images_val_dir.glob('*.png'))[:3]  # 检查前3个文件
        print(f"  📄 图像文件示例:")
        for img_file in image_files:
            print(f"    - {img_file.name}")
    
    velodyne_val_dir = corruption_path / 'velodyne' / 'val'
    if velodyne_val_dir.exists():
        velodyne_files = list(velodyne_val_dir.glob('*.npy'))[:3]  # 检查前3个文件
        print(f"  📄 点云文件示例:")
        for vel_file in velodyne_files:
            print(f"    - {vel_file.name}")
    
    return True


def main():
    args, cfg = parse_config()
    
    print("🚀 简化KITTI-C测试")
    print(f"📂 腐败类型: {args.corruption}")
    print(f"📂 数据路径: {cfg.DATA_CONFIG.DATA_PATH}")
    print(f"📂 Info文件: {cfg.DATA_CONFIG.INFO_PATH['test']}")
    
    # 检查数据集文件
    if not check_dataset_files(cfg.DATA_CONFIG.DATA_PATH, args.corruption):
        print("❌ 数据集检查失败，请先修复数据集问题")
        return
    
    # 创建输出目录
    output_dir = cfg.ROOT_DIR / 'output' / cfg.EXP_GROUP_PATH / cfg.TAG / 'simple_test'
    output_dir.mkdir(parents=True, exist_ok=True)
    
    eval_output_dir = output_dir / 'eval'
    
    # 从checkpoint文件名提取epoch信息
    num_list = re.findall(r'\d+', args.ckpt) if args.ckpt is not None else []
    epoch_id = num_list[-1] if num_list.__len__() > 0 else 'no_number'
    eval_output_dir = eval_output_dir / ('epoch_%s' % epoch_id) / cfg.DATA_CONFIG.DATA_SPLIT['test']
    
    eval_output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    log_file = eval_output_dir / ('log_eval_%s' % datetime.datetime.now().strftime('%Y%m%d-%H%M%S'))
    logger = common_utils.create_logger(log_file, rank=cfg.LOCAL_RANK)
    
    # 记录配置
    logger.info('**********************Start Simple KITTI-C Test**********************')
    logger.info(f'Corruption type: {args.corruption}')
    logger.info(f'Data path: {cfg.DATA_CONFIG.DATA_PATH}')
    logger.info(f'Info file: {cfg.DATA_CONFIG.INFO_PATH["test"]}')
    
    for key, val in vars(cfg).items():
        logger.info('{:16} {}'.format(key, val))
    log_config_to_file(cfg, logger=logger)
    
    try:
        # 构建数据集
        print("📊 构建数据集...")
        test_set, test_loader, sampler = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=args.batch_size,
            dist=False, workers=args.workers, logger=logger, training=False
        )
        
        logger.info(f'数据集样本数量: {len(test_set)}')
        print(f"✅ 数据集构建成功，样本数量: {len(test_set)}")
        
        # 构建模型
        print("🤖 构建模型...")
        model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=test_set)
        
        # 加载checkpoint
        print("📥 加载模型权重...")
        model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=False)
        model = model.cuda()
        model.eval()
        
        # 清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        logger.info(f'*************** 开始评估 {args.corruption.upper()} *****************')
        print(f"🧪 开始评估 {args.corruption.upper()}...")
        
        # 开始评估
        with torch.no_grad():
            eval_utils.eval_one_epoch(
                cfg, args, model, test_loader, epoch_id, logger, dist_test=False,
                result_dir=eval_output_dir, save_to_file=True
            )
        
        logger.info(f'*************** 完成评估 {args.corruption.upper()} *****************')
        print(f"🎉 {args.corruption.upper()} 测试完成!")
        
        # 显示结果
        print(f"📊 结果保存在: {eval_output_dir}")
        print(f"📄 日志文件: {log_file}")
        
        # 显示日志中的关键结果
        if log_file.exists():
            with open(log_file, 'r') as f:
                lines = f.readlines()
            
            print(f"\n📊 关键结果:")
            for line in lines[-30:]:  # 显示最后30行
                if any(keyword in line for keyword in ['AP@', 'bbox AP:', 'bev  AP:', '3d   AP:', 'recall']):
                    print(f"  {line.strip()}")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
