#!/usr/bin/env python3
"""
调试KITTI info文件的详细脚本
帮你找出具体问题所在
"""

import pickle
import os

def debug_kitti_info():
    # 你的数据集路径
    data_path = '/data/liufeifei/project/robofusion/mmdetection3d/data/kitti'
    info_file = os.path.join(data_path, 'kitti_infos_train.pkl')
    
    print("=== KITTI Info文件调试 ===")
    print(f"检查文件: {info_file}")
    
    try:
        with open(info_file, 'rb') as f:
            infos = pickle.load(f)
        
        print(f"✓ 成功加载，包含 {len(infos)} 条记录")
        
        # 检查前几条记录
        for i in range(min(5, len(infos))):
            print(f"\n--- 第 {i} 条记录 ---")
            info = infos[i]
            
            print(f"记录类型: {type(info)}")
            print(f"记录keys: {list(info.keys()) if isinstance(info, dict) else 'Not a dict'}")
            
            # 重点检查point_cloud字段
            if 'point_cloud' in info:
                pc_info = info['point_cloud']
                print(f"point_cloud类型: {type(pc_info)}")
                print(f"point_cloud内容: {pc_info}")
                
                # 如果是字符串，打印具体内容
                if isinstance(pc_info, str):
                    print(f"⚠️ point_cloud是字符串: '{pc_info}'")
                    print("这就是问题所在！应该是字典类型")
                    
                    # 尝试分析字符串内容
                    if pc_info.startswith('{') and pc_info.endswith('}'):
                        print("看起来像是JSON字符串，尝试解析...")
                        try:
                            import json
                            parsed = json.loads(pc_info)
                            print(f"解析后的内容: {parsed}")
                        except:
                            print("JSON解析失败")
                    
                # 如果是字典，检查键
                elif isinstance(pc_info, dict):
                    print(f"✓ point_cloud是字典: {list(pc_info.keys())}")
                    if 'lidar_idx' in pc_info:
                        print(f"✓ 找到lidar_idx: {pc_info['lidar_idx']}")
                    else:
                        print(f"✗ 缺少lidar_idx，可用键: {list(pc_info.keys())}")
                        # 尝试找替代键
                        possible_keys = ['idx', 'index', 'sample_idx', 'frame_id']
                        for key in possible_keys:
                            if key in pc_info:
                                print(f"  找到可能的替代键: {key} = {pc_info[key]}")
                
                else:
                    print(f"⚠️ point_cloud是未知类型: {type(pc_info)}")
            else:
                print("✗ 没有找到point_cloud字段")
                print(f"可用字段: {list(info.keys())}")
        
        # 统计所有记录的point_cloud类型
        print(f"\n=== 统计分析 ===")
        string_count = 0
        dict_count = 0
        other_count = 0
        
        for info in infos:
            if 'point_cloud' in info:
                pc_info = info['point_cloud']
                if isinstance(pc_info, str):
                    string_count += 1
                elif isinstance(pc_info, dict):
                    dict_count += 1
                else:
                    other_count += 1
        
        print(f"point_cloud类型统计:")
        print(f"  字符串类型: {string_count}")
        print(f"  字典类型: {dict_count}")
        print(f"  其他类型: {other_count}")
        
        if string_count > 0:
            print(f"\n⚠️ 发现问题：有 {string_count} 条记录的point_cloud是字符串类型")
            print("这就是导致错误的原因！")
            
            # 建议解决方案
            print(f"\n=== 解决方案建议 ===")
            if string_count == len(infos):
                print("所有记录都有问题，建议重新生成info文件")
            else:
                print("部分记录有问题，可以尝试修复")
                
    except Exception as e:
        print(f"❌ 加载失败: {e}")

def check_dataset_versions():
    """检查是否是OpenPCDet版本兼容问题"""
    print("\n=== 版本兼容检查 ===")
    
    # 检查是否存在其他版本的info文件
    data_path = '/data/liufeifei/project/robofusion/mmdetection3d/data/kitti'
    
    all_files = os.listdir(data_path)
    info_files = [f for f in all_files if f.endswith('.pkl') and 'info' in f]
    
    print("找到的info文件:")
    for f in info_files:
        file_path = os.path.join(data_path, f)
        size = os.path.getsize(file_path) / (1024*1024)  # MB
        print(f"  {f}: {size:.2f}MB")
    
    # 检查是否有不同版本的OpenPCDet生成的文件
    print("\n建议:")
    print("1. 如果这些文件是其他项目生成的，可能存在格式不兼容")
    print("2. 尝试重新生成info文件以确保兼容性")
    print("3. 或者查看其他项目是如何处理这些info文件的")

def suggest_fix():
    """建议修复方案"""
    print(f"\n=== 修复建议 ===")
    print("基于调试结果，你可以:")
    print("1. 重新生成info文件 (推荐)")
    print("2. 手动修复现有info文件")
    print("3. 修改代码适配当前info文件格式")
    
    print(f"\n重新生成命令:")
    print("cd /path/to/OpenPCDet")
    print("python -m pcdet.datasets.kitti.kitti_dataset create_kitti_infos \\")
    print("    tools/cfgs/dataset_configs/kitti_dataset.yaml")

if __name__ == "__main__":
    debug_kitti_info()
    check_dataset_versions()
    suggest_fix()