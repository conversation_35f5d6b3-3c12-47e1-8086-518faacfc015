---
description: 'Learn about Ultralytics NN modules: get_clones, linear_init_, and multi_scale_deformable_attn_pytorch. Code examples and usage tips.'
keywords: Ultralytics, NN Utils, Docs, PyTorch, bias initialization, linear initialization, multi-scale deformable attention
---

## _get_clones
---
### ::: ultralytics.nn.modules.utils._get_clones
<br><br>

## bias_init_with_prob
---
### ::: ultralytics.nn.modules.utils.bias_init_with_prob
<br><br>

## linear_init_
---
### ::: ultralytics.nn.modules.utils.linear_init_
<br><br>

## inverse_sigmoid
---
### ::: ultralytics.nn.modules.utils.inverse_sigmoid
<br><br>

## multi_scale_deformable_attn_pytorch
---
### ::: ultralytics.nn.modules.utils.multi_scale_deformable_attn_pytorch
<br><br>
