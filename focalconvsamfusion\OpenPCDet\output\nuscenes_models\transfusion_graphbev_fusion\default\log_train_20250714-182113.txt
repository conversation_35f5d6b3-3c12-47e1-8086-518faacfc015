2025-07-14 18:21:13,823   INFO  **********************Start logging**********************
2025-07-14 18:21:13,823   INFO  CUDA_VISIBLE_DEVICES=ALL
2025-07-14 18:21:13,823   INFO  cfg_file         cfgs/nuscenes_models/transfusion_graphbev_fusion.yaml
2025-07-14 18:21:13,823   INFO  batch_size       4
2025-07-14 18:21:13,823   INFO  epochs           20
2025-07-14 18:21:13,823   INFO  workers          8
2025-07-14 18:21:13,823   INFO  extra_tag        default
2025-07-14 18:21:13,823   INFO  ckpt             None
2025-07-14 18:21:13,823   INFO  pretrained_model None
2025-07-14 18:21:13,823   INFO  launcher         none
2025-07-14 18:21:13,823   INFO  tcp_port         18888
2025-07-14 18:21:13,823   INFO  sync_bn          False
2025-07-14 18:21:13,823   INFO  fix_random_seed  False
2025-07-14 18:21:13,823   INFO  ckpt_save_interval 1
2025-07-14 18:21:13,823   INFO  local_rank       0
2025-07-14 18:21:13,823   INFO  max_ckpt_save_num 30
2025-07-14 18:21:13,823   INFO  merge_all_iters_to_one_epoch False
2025-07-14 18:21:13,823   INFO  set_cfgs         None
2025-07-14 18:21:13,823   INFO  cfg.ROOT_DIR: /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet
2025-07-14 18:21:13,823   INFO  cfg.LOCAL_RANK: 0
2025-07-14 18:21:13,824   INFO  cfg.CLASS_NAMES: ['car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone']
2025-07-14 18:21:13,824   INFO  
cfg.DATA_CONFIG = edict()
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.DATASET: NuScenesDataset
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.DATA_PATH: ../data/nuscenes
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.VERSION: v1.0-trainval
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.MAX_SWEEPS: 10
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.PRED_VELOCITY: True
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.SET_NAN_VELOCITY_TO_ZEROS: True
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.FILTER_MIN_POINTS_IN_GT: 1
2025-07-14 18:21:13,824   INFO  
cfg.DATA_CONFIG.DATA_SPLIT = edict()
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.DATA_SPLIT.train: train
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.DATA_SPLIT.test: val
2025-07-14 18:21:13,824   INFO  
cfg.DATA_CONFIG.INFO_PATH = edict()
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.INFO_PATH.train: ['nuscenes_infos_10sweeps_train.pkl']
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.INFO_PATH.test: ['nuscenes_infos_10sweeps_val.pkl']
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.POINT_CLOUD_RANGE: [-54.0, -54.0, -5.0, 54.0, 54.0, 3.0]
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.BALANCED_RESAMPLING: True
2025-07-14 18:21:13,824   INFO  
cfg.DATA_CONFIG.DATA_AUGMENTOR = edict()
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.DATA_AUGMENTOR.DISABLE_AUG_LIST: ['placeholder']
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.DATA_AUGMENTOR.AUG_CONFIG_LIST: [{'NAME': 'gt_sampling', 'USE_ROAD_PLANE': False, 'DB_INFO_PATH': ['nuscenes_dbinfos_10sweeps_withvelo.pkl'], 'PREPARE': {'filter_by_min_points': ['car:5', 'truck:5', 'construction_vehicle:5', 'bus:5', 'trailer:5', 'barrier:5', 'motorcycle:5', 'bicycle:5', 'pedestrian:5', 'traffic_cone:5']}, 'SAMPLE_GROUPS': ['car:2', 'truck:3', 'construction_vehicle:7', 'bus:4', 'trailer:6', 'barrier:2', 'motorcycle:6', 'bicycle:6', 'pedestrian:2', 'traffic_cone:2'], 'NUM_POINT_FEATURES': 5, 'DATABASE_WITH_FAKELIDAR': False, 'REMOVE_EXTRA_WIDTH': [0.0, 0.0, 0.0], 'LIMIT_WHOLE_SCENE': True}, {'NAME': 'random_world_flip', 'ALONG_AXIS_LIST': ['x', 'y']}, {'NAME': 'random_world_rotation', 'WORLD_ROT_ANGLE': [-0.78539816, 0.78539816]}, {'NAME': 'random_world_scaling', 'WORLD_SCALE_RANGE': [0.9, 1.1]}]
2025-07-14 18:21:13,824   INFO  
cfg.DATA_CONFIG.POINT_FEATURE_ENCODING = edict()
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.POINT_FEATURE_ENCODING.encoding_type: absolute_coordinates_encoding
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.POINT_FEATURE_ENCODING.used_feature_list: ['x', 'y', 'z', 'intensity', 'timestamp']
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.POINT_FEATURE_ENCODING.src_feature_list: ['x', 'y', 'z', 'intensity', 'timestamp']
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG.DATA_PROCESSOR: [{'NAME': 'mask_points_and_boxes_outside_range', 'REMOVE_OUTSIDE_BOXES': True}, {'NAME': 'shuffle_points', 'SHUFFLE_ENABLED': {'train': True, 'test': False}}, {'NAME': 'transform_points_to_voxels', 'VOXEL_SIZE': [0.075, 0.075, 0.2], 'MAX_POINTS_PER_VOXEL': 10, 'MAX_NUMBER_OF_VOXELS': {'train': 120000, 'test': 160000}}]
2025-07-14 18:21:13,824   INFO  cfg.DATA_CONFIG._BASE_CONFIG_: cfgs/dataset_configs/nuscenes_dataset.yaml
2025-07-14 18:21:13,824   INFO  
cfg.MODEL = edict()
2025-07-14 18:21:13,824   INFO  cfg.MODEL.NAME: TransfusionWithGraphBEV
2025-07-14 18:21:13,825   INFO  
cfg.MODEL.VFE = edict()
2025-07-14 18:21:13,825   INFO  cfg.MODEL.VFE.NAME: MeanVFE
2025-07-14 18:21:13,825   INFO  
cfg.MODEL.BACKBONE_3D = edict()
2025-07-14 18:21:13,825   INFO  cfg.MODEL.BACKBONE_3D.NAME: VoxelResBackBone8x
2025-07-14 18:21:13,825   INFO  
cfg.MODEL.MAP_TO_BEV = edict()
2025-07-14 18:21:13,825   INFO  cfg.MODEL.MAP_TO_BEV.NAME: HeightCompression
2025-07-14 18:21:13,825   INFO  cfg.MODEL.MAP_TO_BEV.NUM_BEV_FEATURES: 256
2025-07-14 18:21:13,825   INFO  
cfg.MODEL.BACKBONE_2D = edict()
2025-07-14 18:21:13,825   INFO  cfg.MODEL.BACKBONE_2D.NAME: BaseBEVBackbone
2025-07-14 18:21:13,825   INFO  cfg.MODEL.BACKBONE_2D.LAYER_NUMS: [5, 5]
2025-07-14 18:21:13,825   INFO  cfg.MODEL.BACKBONE_2D.LAYER_STRIDES: [1, 2]
2025-07-14 18:21:13,825   INFO  cfg.MODEL.BACKBONE_2D.NUM_FILTERS: [128, 256]
2025-07-14 18:21:13,825   INFO  cfg.MODEL.BACKBONE_2D.UPSAMPLE_STRIDES: [1, 2]
2025-07-14 18:21:13,825   INFO  cfg.MODEL.BACKBONE_2D.NUM_UPSAMPLE_FILTERS: [256, 256]
2025-07-14 18:21:13,825   INFO  
cfg.MODEL.FUSION_MODULE = edict()
2025-07-14 18:21:13,825   INFO  cfg.MODEL.FUSION_MODULE.PC_CHANNELS: 4
2025-07-14 18:21:13,825   INFO  cfg.MODEL.FUSION_MODULE.IMG_CHANNELS: 256
2025-07-14 18:21:13,825   INFO  cfg.MODEL.FUSION_MODULE.BEV_CHANNELS: 256
2025-07-14 18:21:13,825   INFO  cfg.MODEL.FUSION_MODULE.OUTPUT_CHANNELS: 256
2025-07-14 18:21:13,825   INFO  cfg.MODEL.FUSION_MODULE.K_NEIGHBORS: 25
2025-07-14 18:21:13,825   INFO  cfg.MODEL.FUSION_MODULE.NUM_HEADS: 8
2025-07-14 18:21:13,825   INFO  
cfg.MODEL.DENSE_HEAD = edict()
2025-07-14 18:21:13,825   INFO  cfg.MODEL.DENSE_HEAD.NAME: TransFusionHead
2025-07-14 18:21:13,825   INFO  cfg.MODEL.DENSE_HEAD.CLASS_AGNOSTIC: False
2025-07-14 18:21:13,825   INFO  cfg.MODEL.DENSE_HEAD.CLASS_NAMES_EACH_HEAD: [['car', 'truck', 'construction_vehicle', 'bus', 'trailer'], ['barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone']]
2025-07-14 18:21:13,825   INFO  cfg.MODEL.DENSE_HEAD.SHARED_CONV_CHANNEL: 64
2025-07-14 18:21:13,825   INFO  cfg.MODEL.DENSE_HEAD.USE_BIAS_BEFORE_NORM: True
2025-07-14 18:21:13,825   INFO  cfg.MODEL.DENSE_HEAD.NUM_HM_CONV: 2
2025-07-14 18:21:13,825   INFO  
cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG = edict()
2025-07-14 18:21:13,825   INFO  cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_ORDER: ['center', 'height', 'dim', 'rot']
2025-07-14 18:21:13,825   INFO  
cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT = edict()
2025-07-14 18:21:13,825   INFO  
cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.center = edict()
2025-07-14 18:21:13,825   INFO  cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.center.out_channels: 2
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.center.num_conv: 2
2025-07-14 18:21:13,826   INFO  
cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.height = edict()
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.height.out_channels: 1
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.height.num_conv: 2
2025-07-14 18:21:13,826   INFO  
cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.dim = edict()
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.dim.out_channels: 3
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.dim.num_conv: 2
2025-07-14 18:21:13,826   INFO  
cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.rot = edict()
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.rot.out_channels: 2
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.SEPARATE_HEAD_CFG.HEAD_DICT.rot.num_conv: 2
2025-07-14 18:21:13,826   INFO  
cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG = edict()
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG.FEATURE_MAP_STRIDE: 8
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG.NUM_MAX_OBJS: 500
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG.GAUSSIAN_OVERLAP: 0.1
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG.MIN_RADIUS: 2
2025-07-14 18:21:13,826   INFO  
cfg.MODEL.DENSE_HEAD.LOSS_CONFIG = edict()
2025-07-14 18:21:13,826   INFO  
cfg.MODEL.DENSE_HEAD.LOSS_CONFIG.LOSS_WEIGHTS = edict()
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.cls_weight: 1.0
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.loc_weight: 0.25
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.code_weights: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2, 1.0, 1.0]
2025-07-14 18:21:13,826   INFO  
cfg.MODEL.DENSE_HEAD.POST_PROCESSING = edict()
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.POST_PROCESSING.SCORE_THRESH: 0.1
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.POST_PROCESSING.OUTPUT_RAW_SCORE: False
2025-07-14 18:21:13,826   INFO  
cfg.MODEL.DENSE_HEAD.POST_PROCESSING.NMS_CONFIG = edict()
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.POST_PROCESSING.NMS_CONFIG.MULTI_CLASSES_NMS: False
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.POST_PROCESSING.NMS_CONFIG.NMS_TYPE: nms_gpu
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.POST_PROCESSING.NMS_CONFIG.NMS_THRESH: 0.2
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.POST_PROCESSING.NMS_CONFIG.NMS_PRE_MAXSIZE: 1000
2025-07-14 18:21:13,826   INFO  cfg.MODEL.DENSE_HEAD.POST_PROCESSING.NMS_CONFIG.NMS_POST_MAXSIZE: 83
2025-07-14 18:21:13,826   INFO  
cfg.MODEL.POST_PROCESSING = edict()
2025-07-14 18:21:13,826   INFO  cfg.MODEL.POST_PROCESSING.RECALL_THRESH_LIST: [0.3, 0.5, 0.7]
2025-07-14 18:21:13,826   INFO  cfg.MODEL.POST_PROCESSING.SCORE_THRESH: 0.1
2025-07-14 18:21:13,827   INFO  cfg.MODEL.POST_PROCESSING.OUTPUT_RAW_SCORE: False
2025-07-14 18:21:13,827   INFO  cfg.MODEL.POST_PROCESSING.EVAL_METRIC: kitti
2025-07-14 18:21:13,827   INFO  
cfg.MODEL.POST_PROCESSING.NMS_CONFIG = edict()
2025-07-14 18:21:13,827   INFO  cfg.MODEL.POST_PROCESSING.NMS_CONFIG.MULTI_CLASSES_NMS: False
2025-07-14 18:21:13,827   INFO  cfg.MODEL.POST_PROCESSING.NMS_CONFIG.NMS_TYPE: nms_gpu
2025-07-14 18:21:13,827   INFO  cfg.MODEL.POST_PROCESSING.NMS_CONFIG.NMS_THRESH: 0.01
2025-07-14 18:21:13,827   INFO  cfg.MODEL.POST_PROCESSING.NMS_CONFIG.NMS_PRE_MAXSIZE: 4096
2025-07-14 18:21:13,827   INFO  cfg.MODEL.POST_PROCESSING.NMS_CONFIG.NMS_POST_MAXSIZE: 500
2025-07-14 18:21:13,827   INFO  
cfg.OPTIMIZATION = edict()
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.BATCH_SIZE_PER_GPU: 4
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.NUM_EPOCHS: 20
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.OPTIMIZER: adam_onecycle
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.LR: 0.003
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.WEIGHT_DECAY: 0.01
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.MOMENTUM: 0.9
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.MOMS: [0.95, 0.85]
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.PCT_START: 0.4
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.DIV_FACTOR: 10
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.DECAY_STEP_LIST: [35, 45]
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.LR_DECAY: 0.1
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.LR_CLIP: 1e-07
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.LR_WARMUP: False
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.WARMUP_EPOCH: 1
2025-07-14 18:21:13,827   INFO  cfg.OPTIMIZATION.GRAD_NORM_CLIP: 10
2025-07-14 18:21:13,827   INFO  cfg.TAG: transfusion_graphbev_fusion
2025-07-14 18:21:13,827   INFO  cfg.EXP_GROUP_PATH: nuscenes_models
