#!/usr/bin/env python3
"""
一键设置KITTI-C标注文件的完整脚本
包含：复制标注文件 + 重新生成info文件 + 验证

专门为用户路径定制：
- 原始KITTI: /data/liufeifei/project/robofusion/mmdetection3d/data/kitti/training/label_2
- 目标KITTI-C: /data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C
"""

import os
import shutil
import sys
import pickle
import numpy as np
from pathlib import Path


def extract_sample_id(filename):
    """从KITTI-C文件名提取原始样本ID"""
    return filename.split('_')[0]


def copy_labels_for_corruption(kitti_labels_path, kitti_c_path, corruption_type):
    """为特定腐败类型复制标注文件"""
    
    print(f"🔄 处理 {corruption_type}...")
    
    corruption_path = kitti_c_path / corruption_type
    if not corruption_path.exists():
        print(f"❌ 腐败文件夹不存在: {corruption_path}")
        return 0
    
    # 创建labels/val目录
    labels_val_path = corruption_path / 'labels' / 'val'
    labels_val_path.mkdir(parents=True, exist_ok=True)
    
    # 获取图像文件的样本ID
    images_val_path = corruption_path / 'images' / 'val'
    if not images_val_path.exists():
        print(f"❌ 图像目录不存在: {images_val_path}")
        return 0
    
    image_files = list(images_val_path.glob('*.png'))
    sample_ids = set(extract_sample_id(f.name) for f in image_files)
    
    # 复制标注文件
    copied_count = 0
    for sample_id in sample_ids:
        src_file = kitti_labels_path / f"{sample_id}.txt"
        dst_file = labels_val_path / f"{sample_id}.txt"
        
        if src_file.exists():
            shutil.copy2(src_file, dst_file)
            copied_count += 1
    
    print(f"  ✅ 复制了 {copied_count} 个标注文件")
    return copied_count


def get_calib_info(calib_path):
    """读取标定信息"""
    calib_info = {}
    
    with open(calib_path, 'r') as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if len(line) == 0 or line[0] == '#':
            continue
        
        key, value = line.split(':', 1)
        calib_info[key] = np.array([float(x) for x in value.split()])
    
    # 重塑矩阵
    if 'P2' in calib_info:
        calib_info['P2'] = calib_info['P2'].reshape(3, 4)
    if 'R0_rect' in calib_info:
        calib_info['R0_rect'] = calib_info['R0_rect'].reshape(3, 3)
    if 'Tr_velo_to_cam' in calib_info:
        calib_info['Tr_velo_to_cam'] = calib_info['Tr_velo_to_cam'].reshape(3, 4)
    
    return calib_info


def get_label_info(label_path):
    """读取标注信息"""
    with open(label_path, 'r') as f:
        lines = f.readlines()
    
    objects = []
    for line in lines:
        line = line.strip()
        if len(line) == 0:
            continue
        
        parts = line.split(' ')
        if len(parts) < 15:
            continue
        
        obj = {
            'name': parts[0],
            'truncated': float(parts[1]),
            'occluded': int(parts[2]),
            'alpha': float(parts[3]),
            'bbox': [float(parts[4]), float(parts[5]), float(parts[6]), float(parts[7])],
            'dimensions': [float(parts[8]), float(parts[9]), float(parts[10])],
            'location': [float(parts[11]), float(parts[12]), float(parts[13])],
            'rotation_y': float(parts[14])
        }
        objects.append(obj)
    
    # 转换为数组格式
    if objects:
        annos = {
            'name': np.array([obj['name'] for obj in objects]),
            'truncated': np.array([obj['truncated'] for obj in objects]),
            'occluded': np.array([obj['occluded'] for obj in objects]),
            'alpha': np.array([obj['alpha'] for obj in objects]),
            'bbox': np.array([obj['bbox'] for obj in objects]),
            'dimensions': np.array([obj['dimensions'] for obj in objects]),
            'location': np.array([obj['location'] for obj in objects]),
            'rotation_y': np.array([obj['rotation_y'] for obj in objects])
        }
        annos['num_points_in_gt'] = np.array([0] * len(objects))
        annos['difficulty'] = np.array([0] * len(objects))
    else:
        # 空标注
        annos = {
            'name': np.array([]),
            'truncated': np.array([]),
            'occluded': np.array([]),
            'alpha': np.array([]),
            'bbox': np.zeros((0, 4)),
            'dimensions': np.zeros((0, 3)),
            'location': np.zeros((0, 3)),
            'rotation_y': np.array([]),
            'num_points_in_gt': np.array([]),
            'difficulty': np.array([])
        }
    
    return annos


def create_info_for_corruption(data_path, corruption_type):
    """为特定腐败类型创建info文件"""
    
    corruption_path = data_path / corruption_type
    labels_path = corruption_path / 'labels' / 'val'
    
    if not labels_path.exists():
        print(f"❌ 标注目录不存在: {labels_path}")
        return []
    
    label_files = list(labels_path.glob('*.txt'))
    sample_ids = [f.stem for f in label_files]
    sample_ids.sort()
    
    kitti_infos = []
    for sample_idx in sample_ids:
        # 基本信息
        info = {
            'image': {
                'image_idx': sample_idx,
                'image_shape': [375, 1242, 3]
            },
            'point_cloud': {
                'num_features': 4,
                'lidar_idx': sample_idx  # 添加lidar_idx字段
            }
        }
        
        # 查找对应的文件
        image_files = list((corruption_path / 'images' / 'val').glob(f"{sample_idx}_*"))
        velodyne_files = list((corruption_path / 'velodyne' / 'val').glob(f"{sample_idx}_*"))
        
        if image_files:
            info['image']['image_path'] = str(image_files[0].relative_to(data_path))
        
        if velodyne_files:
            info['point_cloud']['velodyne_path'] = str(velodyne_files[0].relative_to(data_path))
        
        # 标定信息
        calib_path = corruption_path / 'calib' / f"{sample_idx}.txt"
        if calib_path.exists():
            calib_info = get_calib_info(calib_path)
            info.update(calib_info)
        
        # 标注信息
        label_path = labels_path / f"{sample_idx}.txt"
        if label_path.exists():
            annos = get_label_info(label_path)
            info['annos'] = annos
        
        kitti_infos.append(info)
    
    return kitti_infos


def main():
    # 固定路径
    kitti_labels_path = Path("/data/liufeifei/project/robofusion/mmdetection3d/data/kitti/training/label_2")
    kitti_c_path = Path("/data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C")
    
    print("=" * 80)
    print("🚀 KITTI-C标注文件一键设置工具")
    print("=" * 80)
    print(f"📂 原始KITTI标注: {kitti_labels_path}")
    print(f"📂 目标KITTI-C: {kitti_c_path}")
    print("=" * 80)
    
    # 检查路径
    if not kitti_labels_path.exists():
        print(f"❌ 原始KITTI标注路径不存在!")
        sys.exit(1)
    
    if not kitti_c_path.exists():
        print(f"❌ KITTI-C路径不存在!")
        sys.exit(1)
    
    # 步骤1: 复制标注文件
    print("\n📋 步骤1: 复制标注文件")
    print("-" * 40)
    
    corruption_types = ['fog', 'snow', 'rain', 'sun']
    total_copied = 0
    
    for corruption in corruption_types:
        copied = copy_labels_for_corruption(kitti_labels_path, kitti_c_path, corruption)
        total_copied += copied
    
    if total_copied == 0:
        print("❌ 没有复制任何文件!")
        sys.exit(1)
    
    print(f"✅ 总共复制了 {total_copied} 个标注文件")
    
    # 步骤2: 重新生成info文件
    print("\n📋 步骤2: 重新生成info文件")
    print("-" * 40)
    
    for corruption in corruption_types:
        print(f"🔄 生成 {corruption} info文件...")
        
        kitti_infos = create_info_for_corruption(kitti_c_path, corruption)
        
        if kitti_infos:
            # 保存info文件
            output_file = kitti_c_path / f'kitti_c_infos_val_{corruption}.pkl'
            with open(output_file, 'wb') as f:
                pickle.dump(kitti_infos, f)
            
            total_objects = sum(len(info.get('annos', {}).get('name', [])) for info in kitti_infos)
            print(f"  ✅ 保存到: {output_file.name}")
            print(f"  📊 {len(kitti_infos)} 个样本, {total_objects} 个标注对象")
    
    # 步骤3: 验证
    print("\n📋 步骤3: 验证结果")
    print("-" * 40)
    
    for corruption in corruption_types:
        info_file = kitti_c_path / f'kitti_c_infos_val_{corruption}.pkl'
        labels_dir = kitti_c_path / corruption / 'labels' / 'val'
        
        if info_file.exists() and labels_dir.exists():
            label_count = len(list(labels_dir.glob('*.txt')))
            print(f"  ✅ {corruption}: {label_count} 个标注文件 + info文件")
        else:
            print(f"  ❌ {corruption}: 设置不完整")
    
    print("\n🎉 设置完成!")
    print("💡 现在可以重新运行测试来获得mAP结果了!")
    print("=" * 80)


if __name__ == '__main__':
    main()
