---
description: Explore Ultralytics YOLO's configuration functions and tools. Handle settings, manage defaults, and deal with deprecations in your YOLO configuration.
keywords: Ultralytics, YOLO, configuration, cfg2dict, get_cfg, handle_deprecation, check_cfg_mismatch, merge_equals_args, handle_yolo_hub, handle_yolo_settings, entrypoint, copy_default_cfg
---

## cfg2dict
---
### ::: ultralytics.yolo.cfg.cfg2dict
<br><br>

## get_cfg
---
### ::: ultralytics.yolo.cfg.get_cfg
<br><br>

## _handle_deprecation
---
### ::: ultralytics.yolo.cfg._handle_deprecation
<br><br>

## check_cfg_mismatch
---
### ::: ultralytics.yolo.cfg.check_cfg_mismatch
<br><br>

## merge_equals_args
---
### ::: ultralytics.yolo.cfg.merge_equals_args
<br><br>

## handle_yolo_hub
---
### ::: ultralytics.yolo.cfg.handle_yolo_hub
<br><br>

## handle_yolo_settings
---
### ::: ultralytics.yolo.cfg.handle_yolo_settings
<br><br>

## entrypoint
---
### ::: ultralytics.yolo.cfg.entrypoint
<br><br>

## copy_default_cfg
---
### ::: ultralytics.yolo.cfg.copy_default_cfg
<br><br>
