2025-07-15 21:07:34,621   INFO  **********************Start logging**********************
2025-07-15 21:07:34,621   INFO  CUDA_VISIBLE_DEVICES=ALL
2025-07-15 21:07:34,621   INFO  cfg_file         cfgs/kitti_models/sam_onlinev3.yaml
2025-07-15 21:07:34,621   INFO  batch_size       2
2025-07-15 21:07:34,622   INFO  epochs           80
2025-07-15 21:07:34,622   INFO  workers          4
2025-07-15 21:07:34,622   INFO  extra_tag        default
2025-07-15 21:07:34,622   INFO  ckpt             None
2025-07-15 21:07:34,622   INFO  pretrained_model None
2025-07-15 21:07:34,622   INFO  launcher         none
2025-07-15 21:07:34,622   INFO  tcp_port         34830
2025-07-15 21:07:34,622   INFO  master_port      2333
2025-07-15 21:07:34,622   INFO  sync_bn          False
2025-07-15 21:07:34,622   INFO  fix_random_seed  False
2025-07-15 21:07:34,622   INFO  ckpt_save_interval 1
2025-07-15 21:07:34,622   INFO  local_rank       0
2025-07-15 21:07:34,622   INFO  max_ckpt_save_num 10
2025-07-15 21:07:34,622   INFO  merge_all_iters_to_one_epoch False
2025-07-15 21:07:34,622   INFO  set_cfgs         None
2025-07-15 21:07:34,622   INFO  max_waiting_mins 0
2025-07-15 21:07:34,622   INFO  start_epoch      0
2025-07-15 21:07:34,622   INFO  save_to_file     False
2025-07-15 21:07:34,622   INFO  cfg.ROOT_DIR: /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet
2025-07-15 21:07:34,622   INFO  cfg.LOCAL_RANK: 0
2025-07-15 21:07:34,622   INFO  cfg.CLASS_NAMES: ['Car']
2025-07-15 21:07:34,622   INFO  
cfg.DATA_CONFIG = edict()
2025-07-15 21:07:34,622   INFO  cfg.DATA_CONFIG.DATASET: KittiDataset
2025-07-15 21:07:34,622   INFO  cfg.DATA_CONFIG.DATA_PATH: /data/liufeifei/project/robofusion/mmdetection3d/data/kitti
2025-07-15 21:07:34,622   INFO  cfg.DATA_CONFIG.POINT_CLOUD_RANGE: [0, -40, -3, 70.4, 40, 1]
2025-07-15 21:07:34,622   INFO  
cfg.DATA_CONFIG.DATA_SPLIT = edict()
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.DATA_SPLIT.train: train
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.DATA_SPLIT.test: val
2025-07-15 21:07:34,623   INFO  
cfg.DATA_CONFIG.INFO_PATH = edict()
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.INFO_PATH.train: ['kitti_infos_train.pkl']
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.INFO_PATH.test: ['kitti_infos_val.pkl']
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.GET_ITEM_LIST: ['images', 'points', 'calib_matricies', 'gt_boxes2d', 'sparsedepth']
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.FOV_POINTS_ONLY: True
2025-07-15 21:07:34,623   INFO  
cfg.DATA_CONFIG.DATA_AUGMENTOR = edict()
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.DATA_AUGMENTOR.DISABLE_AUG_LIST: ['placeholder']
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.DATA_AUGMENTOR.AUG_CONFIG_LIST: [{'NAME': 'gt_sampling', 'AUG_WITH_IMAGE': True, 'JOINT_SAMPLE': True, 'KEEP_RAW': False, 'POINT_REFINE': True, 'BOX_IOU_THRES': 0.5, 'IMG_AUG_TYPE': 'by_depth', 'AUG_USE_TYPE': 'annotation', 'IMG_ROOT_PATH': 'training/image_2', 'USE_ROAD_PLANE': True, 'DB_INFO_PATH': ['kitti_dbinfos_train.pkl'], 'PREPARE': {'filter_by_min_points': ['Car:5'], 'filter_by_difficulty': [-1]}, 'SAMPLE_GROUPS': ['Car:15'], 'NUM_POINT_FEATURES': 4, 'DATABASE_WITH_FAKELIDAR': False, 'REMOVE_EXTRA_WIDTH': [0.0, 0.0, 0.0], 'LIMIT_WHOLE_SCENE': False}, {'NAME': 'random_world_flip', 'ALONG_AXIS_LIST': ['x']}, {'NAME': 'random_world_rotation', 'WORLD_ROT_ANGLE': [-0.78539816, 0.78539816]}, {'NAME': 'random_world_scaling', 'WORLD_SCALE_RANGE': [0.95, 1.05]}]
2025-07-15 21:07:34,623   INFO  
cfg.DATA_CONFIG.POINT_FEATURE_ENCODING = edict()
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.POINT_FEATURE_ENCODING.encoding_type: absolute_coordinates_encoding
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.POINT_FEATURE_ENCODING.used_feature_list: ['x', 'y', 'z', 'intensity']
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.POINT_FEATURE_ENCODING.src_feature_list: ['x', 'y', 'z', 'intensity']
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG.DATA_PROCESSOR: [{'NAME': 'mask_points_and_boxes_outside_range', 'REMOVE_OUTSIDE_BOXES': True}, {'NAME': 'shuffle_points', 'SHUFFLE_ENABLED': {'train': True, 'test': False}}, {'NAME': 'transform_points_to_voxels', 'VOXEL_SIZE': [0.05, 0.05, 0.1], 'MAX_POINTS_PER_VOXEL': 5, 'MAX_NUMBER_OF_VOXELS': {'train': 16000, 'test': 40000}}]
2025-07-15 21:07:34,623   INFO  cfg.DATA_CONFIG._BASE_CONFIG_: cfgs/dataset_configs/kitti_dataset.yaml
2025-07-15 21:07:34,623   INFO  
cfg.MODEL = edict()
2025-07-15 21:07:34,623   INFO  cfg.MODEL.NAME: VoxelRCNN
2025-07-15 21:07:34,623   INFO  
cfg.MODEL.VFE = edict()
2025-07-15 21:07:34,623   INFO  cfg.MODEL.VFE.NAME: MeanVFE
2025-07-15 21:07:34,623   INFO  
cfg.MODEL.BACKBONE_3D = edict()
2025-07-15 21:07:34,623   INFO  cfg.MODEL.BACKBONE_3D.NAME: VoxelBackBone8xFocal
2025-07-15 21:07:34,623   INFO  cfg.MODEL.BACKBONE_3D.USE_IMG: True
2025-07-15 21:07:34,623   INFO  cfg.MODEL.BACKBONE_3D.USE_SAM_ONLINEV2: True
2025-07-15 21:07:34,623   INFO  cfg.MODEL.BACKBONE_3D.USE_SAM_FPN: True
2025-07-15 21:07:34,623   INFO  cfg.MODEL.BACKBONE_3D.ZGX_CODE: False
2025-07-15 21:07:34,624   INFO  cfg.MODEL.BACKBONE_3D.USE_WAVE: True
2025-07-15 21:07:34,624   INFO  cfg.MODEL.BACKBONE_3D.IMG_PRETRAIN: /data/liufeifei/project/RoboFusion-master/RoboFusion-master/sam_image_encoder_b.pth
2025-07-15 21:07:34,624   INFO  
cfg.MODEL.MAP_TO_BEV = edict()
2025-07-15 21:07:34,624   INFO  cfg.MODEL.MAP_TO_BEV.NAME: HeightCompression
2025-07-15 21:07:34,624   INFO  cfg.MODEL.MAP_TO_BEV.NUM_BEV_FEATURES: 256
2025-07-15 21:07:34,624   INFO  
cfg.MODEL.BACKBONE_2D = edict()
2025-07-15 21:07:34,624   INFO  cfg.MODEL.BACKBONE_2D.NAME: BaseBEVBackbone
2025-07-15 21:07:34,624   INFO  cfg.MODEL.BACKBONE_2D.LAYER_NUMS: [5, 5]
2025-07-15 21:07:34,624   INFO  cfg.MODEL.BACKBONE_2D.LAYER_STRIDES: [1, 2]
2025-07-15 21:07:34,624   INFO  cfg.MODEL.BACKBONE_2D.NUM_FILTERS: [64, 128]
2025-07-15 21:07:34,624   INFO  cfg.MODEL.BACKBONE_2D.UPSAMPLE_STRIDES: [1, 2]
2025-07-15 21:07:34,624   INFO  cfg.MODEL.BACKBONE_2D.NUM_UPSAMPLE_FILTERS: [128, 128]
2025-07-15 21:07:34,624   INFO  
cfg.MODEL.DENSE_HEAD = edict()
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.NAME: AnchorHeadSingle
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.CLASS_AGNOSTIC: False
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.USE_DIRECTION_CLASSIFIER: True
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.DIR_OFFSET: 0.78539
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.DIR_LIMIT_OFFSET: 0.0
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.NUM_DIR_BINS: 2
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.ANCHOR_GENERATOR_CONFIG: [{'class_name': 'Car', 'anchor_sizes': [[3.9, 1.6, 1.56]], 'anchor_rotations': [0, 1.57], 'anchor_bottom_heights': [-1.78], 'align_center': False, 'feature_map_stride': 8, 'matched_threshold': 0.6, 'unmatched_threshold': 0.45}]
2025-07-15 21:07:34,624   INFO  
cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG = edict()
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG.NAME: AxisAlignedTargetAssigner
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG.POS_FRACTION: -1.0
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG.SAMPLE_SIZE: 512
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG.NORM_BY_NUM_EXAMPLES: False
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG.MATCH_HEIGHT: False
2025-07-15 21:07:34,624   INFO  cfg.MODEL.DENSE_HEAD.TARGET_ASSIGNER_CONFIG.BOX_CODER: ResidualCoder
2025-07-15 21:07:34,624   INFO  
cfg.MODEL.DENSE_HEAD.LOSS_CONFIG = edict()
2025-07-15 21:07:34,624   INFO  
cfg.MODEL.DENSE_HEAD.LOSS_CONFIG.LOSS_WEIGHTS = edict()
2025-07-15 21:07:34,625   INFO  cfg.MODEL.DENSE_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.cls_weight: 1.0
2025-07-15 21:07:34,625   INFO  cfg.MODEL.DENSE_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.loc_weight: 2.0
2025-07-15 21:07:34,625   INFO  cfg.MODEL.DENSE_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.dir_weight: 0.2
2025-07-15 21:07:34,625   INFO  cfg.MODEL.DENSE_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.code_weights: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
2025-07-15 21:07:34,625   INFO  
cfg.MODEL.ROI_HEAD = edict()
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NAME: VoxelRCNNHead
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.CLASS_AGNOSTIC: True
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.SHARED_FC: [256, 256]
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.CLS_FC: [256, 256]
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.REG_FC: [256, 256]
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.DP_RATIO: 0.3
2025-07-15 21:07:34,625   INFO  
cfg.MODEL.ROI_HEAD.NMS_CONFIG = edict()
2025-07-15 21:07:34,625   INFO  
cfg.MODEL.ROI_HEAD.NMS_CONFIG.TRAIN = edict()
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TRAIN.NMS_TYPE: nms_gpu
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TRAIN.MULTI_CLASSES_NMS: False
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TRAIN.NMS_PRE_MAXSIZE: 9000
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TRAIN.NMS_POST_MAXSIZE: 512
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TRAIN.NMS_THRESH: 0.8
2025-07-15 21:07:34,625   INFO  
cfg.MODEL.ROI_HEAD.NMS_CONFIG.TEST = edict()
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TEST.NMS_TYPE: nms_gpu
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TEST.MULTI_CLASSES_NMS: False
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TEST.USE_FAST_NMS: False
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TEST.SCORE_THRESH: 0.0
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TEST.NMS_PRE_MAXSIZE: 2048
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TEST.NMS_POST_MAXSIZE: 100
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.NMS_CONFIG.TEST.NMS_THRESH: 0.7
2025-07-15 21:07:34,625   INFO  
cfg.MODEL.ROI_HEAD.ROI_GRID_POOL = edict()
2025-07-15 21:07:34,625   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.FEATURES_SOURCE: ['x_conv2', 'x_conv3', 'x_conv4']
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.PRE_MLP: True
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.GRID_SIZE: 6
2025-07-15 21:07:34,626   INFO  
cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS = edict()
2025-07-15 21:07:34,626   INFO  
cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv2 = edict()
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv2.MLPS: [[32, 32]]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv2.QUERY_RANGES: [[4, 4, 4]]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv2.POOL_RADIUS: [0.4]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv2.NSAMPLE: [16]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv2.POOL_METHOD: max_pool
2025-07-15 21:07:34,626   INFO  
cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv3 = edict()
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv3.MLPS: [[32, 32]]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv3.QUERY_RANGES: [[4, 4, 4]]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv3.POOL_RADIUS: [0.8]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv3.NSAMPLE: [16]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv3.POOL_METHOD: max_pool
2025-07-15 21:07:34,626   INFO  
cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv4 = edict()
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv4.MLPS: [[32, 32]]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv4.QUERY_RANGES: [[4, 4, 4]]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv4.POOL_RADIUS: [1.6]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv4.NSAMPLE: [16]
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.ROI_GRID_POOL.POOL_LAYERS.x_conv4.POOL_METHOD: max_pool
2025-07-15 21:07:34,626   INFO  
cfg.MODEL.ROI_HEAD.TARGET_CONFIG = edict()
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.TARGET_CONFIG.BOX_CODER: ResidualCoder
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.TARGET_CONFIG.ROI_PER_IMAGE: 128
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.TARGET_CONFIG.FG_RATIO: 0.5
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.TARGET_CONFIG.SAMPLE_ROI_BY_EACH_CLASS: True
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.TARGET_CONFIG.CLS_SCORE_TYPE: roi_iou
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.TARGET_CONFIG.CLS_FG_THRESH: 0.75
2025-07-15 21:07:34,626   INFO  cfg.MODEL.ROI_HEAD.TARGET_CONFIG.CLS_BG_THRESH: 0.25
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.TARGET_CONFIG.CLS_BG_THRESH_LO: 0.1
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.TARGET_CONFIG.HARD_BG_RATIO: 0.8
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.TARGET_CONFIG.REG_FG_THRESH: 0.55
2025-07-15 21:07:34,627   INFO  
cfg.MODEL.ROI_HEAD.LOSS_CONFIG = edict()
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.LOSS_CONFIG.CLS_LOSS: BinaryCrossEntropy
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.LOSS_CONFIG.REG_LOSS: smooth-l1
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.LOSS_CONFIG.CORNER_LOSS_REGULARIZATION: True
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.LOSS_CONFIG.GRID_3D_IOU_LOSS: False
2025-07-15 21:07:34,627   INFO  
cfg.MODEL.ROI_HEAD.LOSS_CONFIG.LOSS_WEIGHTS = edict()
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.rcnn_cls_weight: 1.0
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.rcnn_reg_weight: 1.0
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.rcnn_corner_weight: 1.0
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.rcnn_iou3d_weight: 1.0
2025-07-15 21:07:34,627   INFO  cfg.MODEL.ROI_HEAD.LOSS_CONFIG.LOSS_WEIGHTS.code_weights: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
2025-07-15 21:07:34,627   INFO  
cfg.MODEL.POST_PROCESSING = edict()
2025-07-15 21:07:34,627   INFO  cfg.MODEL.POST_PROCESSING.RECALL_THRESH_LIST: [0.3, 0.5, 0.7]
2025-07-15 21:07:34,627   INFO  cfg.MODEL.POST_PROCESSING.SCORE_THRESH: 0.3
2025-07-15 21:07:34,627   INFO  cfg.MODEL.POST_PROCESSING.OUTPUT_RAW_SCORE: False
2025-07-15 21:07:34,627   INFO  cfg.MODEL.POST_PROCESSING.EVAL_METRIC: kitti
2025-07-15 21:07:34,627   INFO  
cfg.MODEL.POST_PROCESSING.NMS_CONFIG = edict()
2025-07-15 21:07:34,627   INFO  cfg.MODEL.POST_PROCESSING.NMS_CONFIG.MULTI_CLASSES_NMS: False
2025-07-15 21:07:34,627   INFO  cfg.MODEL.POST_PROCESSING.NMS_CONFIG.NMS_TYPE: nms_gpu
2025-07-15 21:07:34,627   INFO  cfg.MODEL.POST_PROCESSING.NMS_CONFIG.NMS_THRESH: 0.1
2025-07-15 21:07:34,627   INFO  cfg.MODEL.POST_PROCESSING.NMS_CONFIG.NMS_PRE_MAXSIZE: 4096
2025-07-15 21:07:34,627   INFO  cfg.MODEL.POST_PROCESSING.NMS_CONFIG.NMS_POST_MAXSIZE: 500
2025-07-15 21:07:34,627   INFO  
cfg.OPTIMIZATION = edict()
2025-07-15 21:07:34,627   INFO  cfg.OPTIMIZATION.BATCH_SIZE_PER_GPU: 2
2025-07-15 21:07:34,627   INFO  cfg.OPTIMIZATION.NUM_EPOCHS: 80
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.OPTIMIZER: adam_onecycle
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.LR: 0.01
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.WEIGHT_DECAY: 0.01
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.MOMENTUM: 0.9
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.MOMS: [0.95, 0.85]
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.PCT_START: 0.4
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.DIV_FACTOR: 10
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.DECAY_STEP_LIST: [35, 45]
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.LR_DECAY: 0.1
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.LR_CLIP: 1e-07
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.LR_WARMUP: False
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.WARMUP_EPOCH: 1
2025-07-15 21:07:34,628   INFO  cfg.OPTIMIZATION.GRAD_NORM_CLIP: 10
2025-07-15 21:07:34,628   INFO  cfg.TAG: sam_onlinev3
2025-07-15 21:07:34,628   INFO  cfg.EXP_GROUP_PATH: kitti_models
2025-07-15 21:07:34,760   INFO  Database filter by min points Car: 14357 => 13532
2025-07-15 21:07:34,780   INFO  Database filter by difficulty Car: 13532 => 10759
2025-07-15 21:07:34,786   INFO  Loading KITTI dataset
2025-07-15 21:07:34,893   INFO  Total samples for KITTI dataset: 3712
2025-07-15 21:07:40,555   INFO  VoxelRCNN(
  (vfe): MeanVFE()
  (backbone_3d): VoxelBackBone8xFocal(
    (conv_input): SparseSequential(
      (0): SubMConv3d(4, 16, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[1, 1, 1], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
      (1): BatchNorm1d(16, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
      (2): ReLU(inplace=True)
    )
    (sam_encoder): SamImageEncoder(
      (image_encoder): packageImageEncoderViT(
        (image_encoder): ImageEncoderViT(
          (patch_embed): PatchEmbed(
            (proj): Conv2d(3, 768, kernel_size=(16, 16), stride=(16, 16))
          )
          (blocks): ModuleList(
            (0): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
            (1): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
            (2): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
            (3): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
            (4): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
            (5): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
            (6): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
            (7): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
            (8): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
            (9): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
            (10): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
            (11): Block(
              (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (attn): Attention(
                (qkv): Linear(in_features=768, out_features=2304, bias=True)
                (proj): Linear(in_features=768, out_features=768, bias=True)
              )
              (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
              (mlp): MLPBlock(
                (lin1): Linear(in_features=768, out_features=3072, bias=True)
                (lin2): Linear(in_features=3072, out_features=768, bias=True)
                (act): GELU()
              )
            )
          )
          (neck): Sequential(
            (0): Conv2d(768, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
            (1): LayerNorm2d()
            (2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (3): LayerNorm2d()
          )
        )
      )
    )
    (sam_upsample): SimpleFeaturePyramid(
      (simfp_4): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): LayerNorm()
        (2): GELU()
        (3): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
        (4): Conv2d(
          64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False
          (norm): LayerNorm()
        )
        (5): Conv2d(
          256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False
          (norm): LayerNorm()
        )
      )
      (simfp_5): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(
          128, 256, kernel_size=(1, 1), stride=(1, 1), bias=False
          (norm): LayerNorm()
        )
        (2): Conv2d(
          256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False
          (norm): LayerNorm()
        )
      )
      (simfp_6): Sequential(
        (0): Conv2d(
          256, 256, kernel_size=(1, 1), stride=(1, 1), bias=False
          (norm): LayerNorm()
        )
        (1): Conv2d(
          256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False
          (norm): LayerNorm()
        )
      )
      (simfp_7): Sequential(
        (0): MaxPool2d(kernel_size=2, stride=2, padding=0, dilation=1, ceil_mode=False)
        (1): Conv2d(
          256, 256, kernel_size=(1, 1), stride=(1, 1), bias=False
          (norm): LayerNorm()
        )
        (2): Conv2d(
          256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False
          (norm): LayerNorm()
        )
      )
      (fpn_output7): Conv2d(
        256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False
        (norm): LayerNorm()
      )
      (output_convs): ModuleList(
        (0): Conv2d(
          256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False
          (norm): LayerNorm()
        )
        (1): Conv2d(
          256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False
          (norm): LayerNorm()
        )
        (2): Conv2d(
          256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False
          (norm): LayerNorm()
        )
      )
    )
    (depthfilter): Depthfilter(
      (block1): Sequential(
        (0): Conv2d(2, 8, kernel_size=(3, 3), stride=(2, 2))
        (1): BatchNorm2d(8, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
      (maxpool1): MaxPool2d(kernel_size=4, stride=2, padding=2, dilation=1, ceil_mode=False)
      (block2): Sequential(
        (0): Conv2d(8, 16, kernel_size=(3, 3), stride=(2, 2))
        (1): BatchNorm2d(16, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
      (maxpool2): MaxPool2d(kernel_size=4, stride=2, padding=2, dilation=1, ceil_mode=False)
    )
    (wave_block): Block(
      (norm1): LayerNorm((16,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((16,), eps=1e-05, elementwise_affine=True)
      (attn): WaveAttention(
        (dwt): DWT_2D()
        (idwt): IDWT_2D()
        (reduce): Sequential(
          (0): Conv2d(16, 4, kernel_size=(1, 1), stride=(1, 1))
          (1): BatchNorm2d(4, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
        )
        (filter): Sequential(
          (0): Conv2d(16, 16, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(16, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
        )
        (kv_embed): Identity()
        (q): Linear(in_features=16, out_features=16, bias=True)
        (kv): Sequential(
          (0): LayerNorm((16,), eps=1e-05, elementwise_affine=True)
          (1): Linear(in_features=16, out_features=32, bias=True)
        )
        (proj): Linear(in_features=20, out_features=16, bias=True)
      )
      (mlp): PVT2FFN(
        (fc1): Linear(in_features=16, out_features=32, bias=True)
        (dwconv): DWConv(
          (dwconv): Conv2d(32, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=32)
        )
        (act): GELU()
        (fc2): Linear(in_features=32, out_features=16, bias=True)
      )
      (drop_path): Identity()
    )
    (reduce_block): BasicBlock2D(
      (conv): Conv2d(272, 16, kernel_size=(1, 1), stride=(1, 1), bias=False)
      (bn): BatchNorm2d(16, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (relu): ReLU(inplace=True)
    )
    (conv_focal_multimodal): FocalSparseConv(
      (conv): SubMConv3d(16, 16, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
      (bn1): BatchNorm1d(16, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
      (relu): ReLU(inplace=True)
      (focal_loss): FocalLoss()
      (conv_imp): SubMConv3d(32, 27, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[1, 1, 1], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
      (frequency_fuse): fusion(
        (FusionModel): Frequency_attention(
          (norm1): LayerNorm((16,), eps=1e-05, elementwise_affine=True)
          (drop_path): Identity()
          (norm2): LayerNorm((16,), eps=1e-05, elementwise_affine=True)
          (mlp): Mlp(
            (fc1): Linear(in_features=16, out_features=64, bias=True)
            (act): GELU()
            (fc2): Linear(in_features=64, out_features=16, bias=True)
            (drop): Dropout(p=0.0, inplace=False)
          )
          (attn): Attention(
            (qkv): Linear(in_features=16, out_features=48, bias=True)
            (attn_drop): Dropout(p=0.0, inplace=False)
            (proj): Linear(in_features=16, out_features=16, bias=True)
            (proj_drop): Dropout(p=0.0, inplace=False)
          )
        )
        (cluster1): AdaptiveAvgPool1d(output_size=2304)
      )
    )
    (conv1): SparseSequentialBatchdict(
      (0): SparseSequential(
        (0): SubMConv3d(16, 16, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (1): BatchNorm1d(16, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
      (1): FocalSparseConv(
        (conv): SubMConv3d(16, 16, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (bn1): BatchNorm1d(16, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (focal_loss): FocalLoss()
        (conv_imp): SubMConv3d(16, 27, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[1, 1, 1], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
      )
    )
    (conv2): SparseSequentialBatchdict(
      (0): SparseSequential(
        (0): SparseConv3d(16, 32, kernel_size=[3, 3, 3], stride=[2, 2, 2], padding=[1, 1, 1], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (1): BatchNorm1d(32, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
      (1): SparseSequential(
        (0): SubMConv3d(32, 32, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (1): BatchNorm1d(32, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
      (2): SparseSequential(
        (0): SubMConv3d(32, 32, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (1): BatchNorm1d(32, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
      (3): FocalSparseConv(
        (conv): SubMConv3d(32, 32, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (bn1): BatchNorm1d(32, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (focal_loss): FocalLoss()
        (conv_imp): SubMConv3d(32, 27, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[1, 1, 1], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
      )
    )
    (conv3): SparseSequentialBatchdict(
      (0): SparseSequential(
        (0): SparseConv3d(32, 64, kernel_size=[3, 3, 3], stride=[2, 2, 2], padding=[1, 1, 1], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (1): BatchNorm1d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
      (1): SparseSequential(
        (0): SubMConv3d(64, 64, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (1): BatchNorm1d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
      (2): SparseSequential(
        (0): SubMConv3d(64, 64, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (1): BatchNorm1d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
      (3): FocalSparseConv(
        (conv): SubMConv3d(64, 64, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (bn1): BatchNorm1d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (focal_loss): FocalLoss()
        (conv_imp): SubMConv3d(64, 27, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[1, 1, 1], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
      )
    )
    (conv4): SparseSequentialBatchdict(
      (0): SparseSequential(
        (0): SparseConv3d(64, 64, kernel_size=[3, 3, 3], stride=[2, 2, 2], padding=[0, 1, 1], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (1): BatchNorm1d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
      (1): SparseSequential(
        (0): SubMConv3d(64, 64, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (1): BatchNorm1d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
      (2): SparseSequential(
        (0): SubMConv3d(64, 64, kernel_size=[3, 3, 3], stride=[1, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
        (1): BatchNorm1d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
    )
    (conv_out): SparseSequential(
      (0): SparseConv3d(64, 128, kernel_size=[3, 1, 1], stride=[2, 1, 1], padding=[0, 0, 0], dilation=[1, 1, 1], output_padding=[0, 0, 0], bias=False, algo=ConvAlgo.MaskImplicitGemm)
      (1): BatchNorm1d(128, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
      (2): ReLU(inplace=True)
    )
  )
  (map_to_bev_module): HeightCompression()
  (pfe): None
  (backbone_2d): BaseBEVBackbone(
    (blocks): ModuleList(
      (0): Sequential(
        (0): ZeroPad2d(padding=(1, 1, 1, 1), value=0.0)
        (1): Conv2d(256, 64, kernel_size=(3, 3), stride=(1, 1), bias=False)
        (2): BatchNorm2d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (3): ReLU()
        (4): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (5): BatchNorm2d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (8): BatchNorm2d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (9): ReLU()
        (10): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (11): BatchNorm2d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (12): ReLU()
        (13): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (14): BatchNorm2d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (15): ReLU()
        (16): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (17): BatchNorm2d(64, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (18): ReLU()
      )
      (1): Sequential(
        (0): ZeroPad2d(padding=(1, 1, 1, 1), value=0.0)
        (1): Conv2d(64, 128, kernel_size=(3, 3), stride=(2, 2), bias=False)
        (2): BatchNorm2d(128, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (3): ReLU()
        (4): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (5): BatchNorm2d(128, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (8): BatchNorm2d(128, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (9): ReLU()
        (10): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (11): BatchNorm2d(128, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (12): ReLU()
        (13): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (14): BatchNorm2d(128, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (15): ReLU()
        (16): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (17): BatchNorm2d(128, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (18): ReLU()
      )
    )
    (deblocks): ModuleList(
      (0): Sequential(
        (0): ConvTranspose2d(64, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (1): BatchNorm2d(128, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU()
      )
      (1): Sequential(
        (0): ConvTranspose2d(128, 128, kernel_size=(2, 2), stride=(2, 2), bias=False)
        (1): BatchNorm2d(128, eps=0.001, momentum=0.01, affine=True, track_running_stats=True)
        (2): ReLU()
      )
    )
  )
  (dense_head): AnchorHeadSingle(
    (cls_loss_func): SigmoidFocalClassificationLoss()
    (reg_loss_func): WeightedSmoothL1Loss()
    (dir_loss_func): WeightedCrossEntropyLoss()
    (conv_cls): Conv2d(256, 2, kernel_size=(1, 1), stride=(1, 1))
    (conv_box): Conv2d(256, 14, kernel_size=(1, 1), stride=(1, 1))
    (conv_dir_cls): Conv2d(256, 4, kernel_size=(1, 1), stride=(1, 1))
  )
  (point_head): None
  (roi_head): VoxelRCNNHead(
    (proposal_target_layer): ProposalTargetLayer()
    (reg_loss_func): WeightedSmoothL1Loss()
    (roi_grid_pool_layers): ModuleList(
      (0): NeighborVoxelSAModuleMSG(
        (groupers): ModuleList(
          (0): VoxelQueryAndGrouping()
        )
        (mlps_in): ModuleList(
          (0): Sequential(
            (0): Conv1d(32, 32, kernel_size=(1,), stride=(1,), bias=False)
            (1): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          )
        )
        (mlps_pos): ModuleList(
          (0): Sequential(
            (0): Conv2d(3, 32, kernel_size=(1, 1), stride=(1, 1), bias=False)
            (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          )
        )
        (mlps_out): ModuleList(
          (0): Sequential(
            (0): Conv1d(32, 32, kernel_size=(1,), stride=(1,), bias=False)
            (1): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (2): ReLU()
          )
        )
        (relu): ReLU()
      )
      (1): NeighborVoxelSAModuleMSG(
        (groupers): ModuleList(
          (0): VoxelQueryAndGrouping()
        )
        (mlps_in): ModuleList(
          (0): Sequential(
            (0): Conv1d(64, 32, kernel_size=(1,), stride=(1,), bias=False)
            (1): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          )
        )
        (mlps_pos): ModuleList(
          (0): Sequential(
            (0): Conv2d(3, 32, kernel_size=(1, 1), stride=(1, 1), bias=False)
            (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          )
        )
        (mlps_out): ModuleList(
          (0): Sequential(
            (0): Conv1d(32, 32, kernel_size=(1,), stride=(1,), bias=False)
            (1): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (2): ReLU()
          )
        )
        (relu): ReLU()
      )
      (2): NeighborVoxelSAModuleMSG(
        (groupers): ModuleList(
          (0): VoxelQueryAndGrouping()
        )
        (mlps_in): ModuleList(
          (0): Sequential(
            (0): Conv1d(64, 32, kernel_size=(1,), stride=(1,), bias=False)
            (1): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          )
        )
        (mlps_pos): ModuleList(
          (0): Sequential(
            (0): Conv2d(3, 32, kernel_size=(1, 1), stride=(1, 1), bias=False)
            (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          )
        )
        (mlps_out): ModuleList(
          (0): Sequential(
            (0): Conv1d(32, 32, kernel_size=(1,), stride=(1,), bias=False)
            (1): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (2): ReLU()
          )
        )
        (relu): ReLU()
      )
    )
    (shared_fc_layer): Sequential(
      (0): Linear(in_features=20736, out_features=256, bias=False)
      (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU(inplace=True)
      (3): Dropout(p=0.3, inplace=False)
      (4): Linear(in_features=256, out_features=256, bias=False)
      (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (6): ReLU(inplace=True)
    )
    (cls_fc_layers): Sequential(
      (0): Linear(in_features=256, out_features=256, bias=False)
      (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Dropout(p=0.3, inplace=False)
      (4): Linear(in_features=256, out_features=256, bias=False)
      (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (6): ReLU()
    )
    (cls_pred_layer): Linear(in_features=256, out_features=1, bias=True)
    (reg_fc_layers): Sequential(
      (0): Linear(in_features=256, out_features=256, bias=False)
      (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Dropout(p=0.3, inplace=False)
      (4): Linear(in_features=256, out_features=256, bias=False)
      (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (6): ReLU()
    )
    (reg_pred_layer): Linear(in_features=256, out_features=7, bias=True)
  )
)
2025-07-15 21:07:40,560   INFO  **********************Start training kitti_models/sam_onlinev3(default)**********************
