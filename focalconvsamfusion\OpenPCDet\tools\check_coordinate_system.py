#!/usr/bin/env python3
"""
检查KITTI-C info文件中的坐标系
"""

import _init_path
import pickle
import numpy as np
from pathlib import Path

def check_info_coordinates():
    """检查info文件中的坐标系"""
    
    # 检查KITTI-C info文件
    info_file = Path('/data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C/kitti_c_infos_val_sun.pkl')
    
    if not info_file.exists():
        print(f"❌ Info文件不存在: {info_file}")
        return
    
    print(f"📂 检查info文件: {info_file}")
    
    with open(info_file, 'rb') as f:
        infos = pickle.load(f)
    
    print(f"📊 总样本数: {len(infos)}")
    
    # 检查前几个样本
    for i in range(min(5, len(infos))):
        info = infos[i]
        print(f"\n📋 样本 {i}:")
        print(f"  样本ID: {info['point_cloud']['lidar_idx']}")
        
        if 'annos' in info:
            annos = info['annos']
            print(f"  标注数量: {len(annos['name'])}")
            
            if len(annos['name']) > 0:
                # 检查第一个标注
                loc = annos['location'][0]
                dim = annos['dimensions'][0]
                rot = annos['rotation_y'][0]
                name = annos['name'][0]
                
                print(f"  第一个对象:")
                print(f"    类别: {name}")
                print(f"    位置: [{loc[0]:.2f}, {loc[1]:.2f}, {loc[2]:.2f}]")
                print(f"    尺寸: [{dim[0]:.2f}, {dim[1]:.2f}, {dim[2]:.2f}]")
                print(f"    旋转: {rot:.3f}")
                
                # 分析坐标范围
                all_locations = np.array(annos['location'])
                print(f"  所有位置范围:")
                print(f"    X: [{all_locations[:, 0].min():.2f}, {all_locations[:, 0].max():.2f}]")
                print(f"    Y: [{all_locations[:, 1].min():.2f}, {all_locations[:, 1].max():.2f}]")
                print(f"    Z: [{all_locations[:, 2].min():.2f}, {all_locations[:, 2].max():.2f}]")
                
                # 判断坐标系
                if all_locations[:, 1].min() < 0:
                    print("  🎯 判断: 可能是激光雷达坐标系 (Y可以为负)")
                else:
                    print("  🎯 判断: 可能是相机坐标系 (Y都为正)")
        else:
            print("  ❌ 没有标注信息")
    
    # 检查是否有异常值
    print(f"\n🔍 检查异常值:")
    all_locations = []
    all_dimensions = []
    
    for info in infos:
        if 'annos' in info and len(info['annos']['name']) > 0:
            all_locations.extend(info['annos']['location'])
            all_dimensions.extend(info['annos']['dimensions'])
    
    if all_locations:
        all_locations = np.array(all_locations)
        all_dimensions = np.array(all_dimensions)
        
        # 检查异常值
        invalid_loc = np.any(all_locations < -500, axis=1) | np.any(all_locations > 500, axis=1)
        invalid_dim = np.any(all_dimensions < 0, axis=1) | np.any(all_dimensions > 50, axis=1)
        
        print(f"  异常位置数量: {invalid_loc.sum()} / {len(all_locations)}")
        print(f"  异常尺寸数量: {invalid_dim.sum()} / {len(all_dimensions)}")
        
        if invalid_loc.sum() > 0:
            print(f"  异常位置示例: {all_locations[invalid_loc][:3]}")
        
        if invalid_dim.sum() > 0:
            print(f"  异常尺寸示例: {all_dimensions[invalid_dim][:3]}")


def check_original_kitti():
    """检查原始KITTI info文件作为对比"""
    
    # 尝试找原始KITTI info文件
    possible_paths = [
        '/data/liufeifei/project/robofusion/mmdetection3d/data/kitti/kitti_infos_val.pkl',
        '/data/liufeifei/project/robofusion/mmdetection3d/data/KITTI/kitti_infos_val.pkl',
    ]
    
    for info_file in possible_paths:
        info_file = Path(info_file)
        if info_file.exists():
            print(f"\n📂 对比原始KITTI: {info_file}")
            
            with open(info_file, 'rb') as f:
                infos = pickle.load(f)
            
            print(f"📊 总样本数: {len(infos)}")
            
            # 检查第一个样本
            if len(infos) > 0:
                info = infos[0]
                if 'annos' in info and len(info['annos']['name']) > 0:
                    loc = info['annos']['location'][0]
                    print(f"  第一个位置: [{loc[0]:.2f}, {loc[1]:.2f}, {loc[2]:.2f}]")
                    
                    all_locations = np.array(info['annos']['location'])
                    print(f"  Y范围: [{all_locations[:, 1].min():.2f}, {all_locations[:, 1].max():.2f}]")
            break
    else:
        print(f"\n❌ 没有找到原始KITTI info文件")


if __name__ == '__main__':
    check_info_coordinates()
    check_original_kitti()
