#!/usr/bin/env python3
"""
专门为用户定制的KITTI标注文件复制脚本
从 /data/liufeifei/project/robofusion/mmdetection3d/data/kitti/training/label_2
复制到 /data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C
"""

import os
import shutil
import sys
from pathlib import Path


def extract_sample_id(filename):
    """从KITTI-C文件名提取原始样本ID"""
    # 例如: 000000_fog_d1.png -> 000000
    return filename.split('_')[0]


def copy_labels_for_corruption(kitti_labels_path, kitti_c_path, corruption_type):
    """为特定腐败类型复制标注文件"""
    
    print(f"\n🔄 处理 {corruption_type} 腐败类型...")
    
    corruption_path = kitti_c_path / corruption_type
    if not corruption_path.exists():
        print(f"❌ KITTI-C腐败文件夹不存在: {corruption_path}")
        return 0
    
    # 创建labels/val目录
    labels_val_path = corruption_path / 'labels' / 'val'
    labels_val_path.mkdir(parents=True, exist_ok=True)
    print(f"📁 创建目录: {labels_val_path}")
    
    # 获取该腐败类型的图像文件列表
    images_val_path = corruption_path / 'images' / 'val'
    if not images_val_path.exists():
        print(f"❌ 图像目录不存在: {images_val_path}")
        return 0
    
    # 获取所有图像文件的样本ID
    image_files = list(images_val_path.glob('*.png'))
    sample_ids = set()
    
    for img_file in image_files:
        sample_id = extract_sample_id(img_file.name)
        sample_ids.add(sample_id)
    
    print(f"📊 找到 {len(sample_ids)} 个唯一样本ID")
    print(f"📊 样本ID示例: {sorted(list(sample_ids))[:5]}")
    
    # 复制对应的标注文件
    copied_count = 0
    missing_count = 0
    
    for sample_id in sorted(sample_ids):
        src_label_file = kitti_labels_path / f"{sample_id}.txt"
        dst_label_file = labels_val_path / f"{sample_id}.txt"
        
        if src_label_file.exists():
            shutil.copy2(src_label_file, dst_label_file)
            copied_count += 1
        else:
            print(f"⚠️  标注文件不存在: {src_label_file}")
            missing_count += 1
    
    print(f"✅ 成功复制: {copied_count} 个标注文件")
    if missing_count > 0:
        print(f"⚠️  缺失文件: {missing_count} 个")
    
    return copied_count


def verify_copied_labels(kitti_c_path):
    """验证复制的标注文件"""
    
    print(f"\n🔍 验证标注文件...")
    
    corruption_types = ['fog', 'snow', 'rain', 'sun']
    total_labels = 0
    
    for corruption in corruption_types:
        labels_path = kitti_c_path / corruption / 'labels' / 'val'
        if labels_path.exists():
            label_files = list(labels_path.glob('*.txt'))
            label_count = len(label_files)
            total_labels += label_count
            print(f"  ✅ {corruption}: {label_count} 个标注文件")
            
            # 检查第一个文件的内容
            if label_files:
                first_file = label_files[0]
                with open(first_file, 'r') as f:
                    lines = f.readlines()
                print(f"     示例文件 {first_file.name}: {len(lines)} 行标注")
        else:
            print(f"  ❌ {corruption}: 标注目录不存在")
    
    print(f"\n📊 总计: {total_labels} 个标注文件")
    return total_labels


def main():
    # 固定路径
    kitti_labels_path = Path("/data/liufeifei/project/robofusion/mmdetection3d/data/kitti/training/label_2")
    kitti_c_path = Path("/data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C")
    
    print("=" * 80)
    print("🚀 KITTI标注文件复制到KITTI-C (用户定制版)")
    print("=" * 80)
    print(f"📂 原始KITTI标注路径: {kitti_labels_path}")
    print(f"📂 目标KITTI-C路径: {kitti_c_path}")
    print("=" * 80)
    
    # 检查路径是否存在
    if not kitti_labels_path.exists():
        print(f"❌ 原始KITTI标注路径不存在: {kitti_labels_path}")
        print("请检查路径是否正确！")
        sys.exit(1)
    
    if not kitti_c_path.exists():
        print(f"❌ KITTI-C路径不存在: {kitti_c_path}")
        print("请检查路径是否正确！")
        sys.exit(1)
    
    # 检查原始标注文件数量
    original_labels = list(kitti_labels_path.glob('*.txt'))
    print(f"📊 原始KITTI标注文件数量: {len(original_labels)}")
    
    if len(original_labels) == 0:
        print("❌ 没有找到任何标注文件！")
        sys.exit(1)
    
    # 为每个腐败类型复制标注文件
    corruption_types = ['fog', 'snow', 'rain', 'sun']
    total_copied = 0
    
    for corruption in corruption_types:
        copied_count = copy_labels_for_corruption(kitti_labels_path, kitti_c_path, corruption)
        total_copied += copied_count
    
    # 验证结果
    if total_copied > 0:
        print(f"\n🎉 标注文件复制完成！")
        print(f"📊 总共复制了 {total_copied} 个标注文件")
        
        verify_copied_labels(kitti_c_path)
        
        print(f"\n📋 下一步操作:")
        print(f"1. 运行: python regenerate_kitti_c_infos.py")
        print(f"2. 重新运行测试来获得mAP结果")
    else:
        print(f"\n❌ 没有复制任何文件，请检查数据集结构！")
    
    print("=" * 80)


if __name__ == '__main__':
    main()
