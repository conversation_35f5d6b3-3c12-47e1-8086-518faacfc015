#!/usr/bin/env python3
"""
重新生成包含ground truth标注的KITTI-C info文件
解决标注文件存在但info文件中没有标注信息的问题
"""

import _init_path
import pickle
import numpy as np
from pathlib import Path
import sys
import os

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.utils import common_utils


def get_kitti_image_info(data_path, corruption_type, sample_idx, extend_matrix=True):
    """获取KITTI-C图像信息"""
    
    corruption_path = data_path / corruption_type
    
    # 图像信息
    image_path = corruption_path / 'images' / 'val'
    # 查找对应的图像文件
    image_files = list(image_path.glob(f"{sample_idx}_*"))
    if not image_files:
        return None
    
    image_file = image_files[0]
    
    info = {
        'image': {
            'image_idx': sample_idx,
            'image_path': str(image_file.relative_to(data_path)),
            'image_shape': [375, 1242, 3]  # KITTI标准尺寸
        }
    }
    
    # 标定信息
    calib_path = corruption_path / 'calib' / f"{sample_idx}.txt"
    if calib_path.exists():
        calib_info = get_calib_info(calib_path)
        info.update(calib_info)
    
    # 点云信息
    velodyne_path = corruption_path / 'velodyne' / 'val'
    velodyne_files = list(velodyne_path.glob(f"{sample_idx}_*"))
    if velodyne_files:
        velodyne_file = velodyne_files[0]
        info['point_cloud'] = {
            'num_features': 4,
            'velodyne_path': str(velodyne_file.relative_to(data_path))
        }
    
    # 标注信息
    labels_path = corruption_path / 'labels' / 'val' / f"{sample_idx}.txt"
    if labels_path.exists():
        annos = get_label_info(labels_path)
        info['annos'] = annos
    
    return info


def get_calib_info(calib_path):
    """读取标定信息"""
    calib_info = {}
    
    with open(calib_path, 'r') as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if len(line) == 0 or line[0] == '#':
            continue
        
        key, value = line.split(':', 1)
        calib_info[key] = np.array([float(x) for x in value.split()])
    
    # 重塑矩阵
    if 'P2' in calib_info:
        calib_info['P2'] = calib_info['P2'].reshape(3, 4)
    if 'R0_rect' in calib_info:
        calib_info['R0_rect'] = calib_info['R0_rect'].reshape(3, 3)
    if 'Tr_velo_to_cam' in calib_info:
        calib_info['Tr_velo_to_cam'] = calib_info['Tr_velo_to_cam'].reshape(3, 4)
    
    return calib_info


def get_label_info(label_path):
    """读取标注信息"""
    with open(label_path, 'r') as f:
        lines = f.readlines()
    
    objects = []
    for line in lines:
        line = line.strip()
        if len(line) == 0:
            continue
        
        parts = line.split(' ')
        if len(parts) < 15:
            continue
        
        obj = {
            'name': parts[0],
            'truncated': float(parts[1]),
            'occluded': int(parts[2]),
            'alpha': float(parts[3]),
            'bbox': [float(parts[4]), float(parts[5]), float(parts[6]), float(parts[7])],
            'dimensions': [float(parts[8]), float(parts[9]), float(parts[10])],
            'location': [float(parts[11]), float(parts[12]), float(parts[13])],
            'rotation_y': float(parts[14])
        }
        objects.append(obj)
    
    # 转换为数组格式
    if objects:
        annos = {
            'name': np.array([obj['name'] for obj in objects]),
            'truncated': np.array([obj['truncated'] for obj in objects]),
            'occluded': np.array([obj['occluded'] for obj in objects]),
            'alpha': np.array([obj['alpha'] for obj in objects]),
            'bbox': np.array([obj['bbox'] for obj in objects]),
            'dimensions': np.array([obj['dimensions'] for obj in objects]),
            'location': np.array([obj['location'] for obj in objects]),
            'rotation_y': np.array([obj['rotation_y'] for obj in objects])
        }
        annos['num_points_in_gt'] = np.array([0] * len(objects))  # 占位符
        annos['difficulty'] = np.array([0] * len(objects))  # 占位符
    else:
        # 空标注
        annos = {
            'name': np.array([]),
            'truncated': np.array([]),
            'occluded': np.array([]),
            'alpha': np.array([]),
            'bbox': np.zeros((0, 4)),
            'dimensions': np.zeros((0, 3)),
            'location': np.zeros((0, 3)),
            'rotation_y': np.array([]),
            'num_points_in_gt': np.array([]),
            'difficulty': np.array([])
        }
    
    return annos


def create_kitti_c_infos_with_labels(data_path, corruption_type='fog'):
    """创建包含标注的KITTI-C info文件"""
    
    data_path = Path(data_path)
    corruption_path = data_path / corruption_type
    
    print(f"为 {corruption_type} 创建info文件...")
    
    # 获取所有样本ID
    labels_path = corruption_path / 'labels' / 'val'
    if not labels_path.exists():
        print(f"❌ 标注目录不存在: {labels_path}")
        return []
    
    label_files = list(labels_path.glob('*.txt'))
    sample_ids = [f.stem for f in label_files]
    sample_ids.sort()
    
    print(f"找到 {len(sample_ids)} 个样本")
    
    # 生成info
    kitti_infos = []
    for sample_idx in sample_ids:
        info = get_kitti_image_info(data_path, corruption_type, sample_idx)
        if info is not None:
            kitti_infos.append(info)
    
    print(f"成功生成 {len(kitti_infos)} 个info条目")
    return kitti_infos


def main():
    # 使用固定路径
    data_path = Path("/data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C")

    print(f"📂 KITTI-C数据集路径: {data_path}")

    if not data_path.exists():
        print(f"❌ KITTI-C路径不存在: {data_path}")
        sys.exit(1)
    
    print("=" * 60)
    print("重新生成KITTI-C info文件（包含标注）")
    print("=" * 60)
    
    corruption_types = ['fog', 'snow', 'rain', 'sun']
    
    for corruption in corruption_types:
        print(f"\n处理 {corruption}...")
        
        kitti_infos = create_kitti_c_infos_with_labels(data_path, corruption)
        
        if kitti_infos:
            # 保存info文件
            output_file = data_path / f'kitti_c_infos_val_{corruption}.pkl'
            with open(output_file, 'wb') as f:
                pickle.dump(kitti_infos, f)
            
            print(f"✓ 保存到: {output_file}")
            
            # 检查标注统计
            total_objects = sum(len(info.get('annos', {}).get('name', [])) for info in kitti_infos)
            print(f"  包含 {total_objects} 个标注对象")
        else:
            print(f"❌ 没有找到有效的样本")
    
    print(f"\n🎉 完成！现在可以重新运行测试来获得mAP结果了。")
    print("=" * 60)


if __name__ == '__main__':
    main()
