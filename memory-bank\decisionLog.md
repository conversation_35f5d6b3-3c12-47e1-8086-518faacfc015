# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-17 17:58:16 - Log of updates made.

*

## Decision

*

## Rationale 

*

## Implementation Details

*

---
### Decision Record
[2025-07-17 21:40:57] - 解释学习率调整的原理和batch size与学习率的关系

**Decision Background:**
用户询问为什么在batch size从2减半到1时，需要将学习率从0.01降到0.005，需要解释学习率的含义和batch size与学习率的关系

**Available Options:**
- Option 1: 保持原学习率0.01不变
  - Pros: 简单，不需要调整超参数
  - Cons: 可能导致训练不稳定，梯度更新步长过大
- Option 2: 按batch size比例调整学习率到0.005
  - Pros: 保持训练稳定性，遵循深度学习最佳实践
  - Cons: 需要理解学习率调整原理

**Final Decision:**
采用Option 2，将学习率从0.01调整到0.005，并详细解释学习率与batch size的关系

**Implementation Plan:**
- Step 1: 解释学习率(LR)的基本概念和作用
- Step 2: 说明batch size对梯度估计的影响
- Step 3: 解释线性缩放规则的理论基础
- Step 4: 提供具体的数学公式和实例
- Validation Method: 通过理论解释和实际训练效果验证

**Risks and Mitigation:**
- Risk 1: 学习率过小导致收敛慢 → Mitigation: 可以通过增加训练epoch或使用学习率调度器
- Risk 2: 理论解释过于复杂 → Mitigation: 使用简单易懂的类比和实例

---
### Decision Record
[2025-07-24 15:07:46] - 修复test_simple.py中图像维度转换问题

**Decision Background:**
测试脚本运行时出现SAM编码器错误："The batch images should be 3 for RGB, but get 375"，需要分析并修复图像数据维度处理问题

**Available Options:**
- Option 1: 修改test_simple.py中的数据转换逻辑
  - Pros: 直接修复问题，不影响其他代码，简单有效
  - Cons: 只解决测试脚本问题，训练代码可能有类似问题
- Option 2: 修改SAM编码器的preprocess函数
  - Pros: 统一处理所有输入格式
  - Cons: 可能影响其他使用SAM编码器的地方，风险较大
- Option 3: 修改数据加载器的collate_batch函数
  - Pros: 从源头解决问题
  - Cons: 影响范围大，可能破坏训练流程

**Final Decision:**
采用Option 1，在test_simple.py中添加图像维度转换

**Implementation Plan:**
- Step 1: 分析数据流，确认问题根源在于维度顺序
- Step 2: 在test_simple.py的数据转换部分添加permute操作
- Step 3: 将(B, H, W, 3)转换为(B, 3, H, W)格式
- Validation Method: 运行测试脚本验证修复效果

**Risks and Mitigation:**
- Risk 1: 其他数据类型可能有类似问题 → Mitigation: 仔细检查所有数据转换逻辑
- Risk 2: 训练代码可能有相同问题 → Mitigation: 检查训练时的数据处理流程

---
### Decision Record
[2025-07-18 08:22:28] - 服务器磁盘空间不足，需要清理空间和优化存储策略

**Decision Background:**
训练过程中出现"No space left on device"错误，无法创建输出目录，导致训练失败。需要立即清理磁盘空间并制定存储优化策略。

**Available Options:**
- Option 1: 清理临时文件和缓存
  - Pros: 快速释放空间，不影响重要数据
  - Cons: 可能需要重新下载某些文件
- Option 2: 删除旧的训练输出和权重文件
  - Pros: 释放大量空间，清理无用数据
  - Cons: 可能丢失有价值的训练结果
- Option 3: 优化存储策略，减少权重保存频率
  - Pros: 长期解决方案，预防未来问题
  - Cons: 需要修改配置，可能影响训练监控

**Final Decision:**
采用综合方案：先清理临时文件和旧输出，然后优化存储策略

**Implementation Plan:**
- Step 1: 检查磁盘使用情况，识别大文件
- Step 2: 清理conda缓存、pip缓存和临时文件
- Step 3: 删除旧的训练输出和不必要的权重文件
- Step 4: 修改训练配置，减少权重保存频率
- Step 5: 设置自动清理脚本
- Validation Method: 确保有足够空间继续训练，监控磁盘使用

**Risks and Mitigation:**
- Risk 1: 误删重要文件 → Mitigation: 仔细检查文件内容，备份重要权重
- Risk 2: 清理后空间仍不足 → Mitigation: 考虑使用外部存储或更换更大磁盘