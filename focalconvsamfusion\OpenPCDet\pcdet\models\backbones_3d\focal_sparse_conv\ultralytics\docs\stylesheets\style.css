/* Table format like GitHub ----------------------------------------------------------------------------------------- */
th, td {
    border: 1px solid var(--md-typeset-table-color);
    border-spacing: 0;
    border-bottom: none;
    border-left: none;
    border-top: none;
}

.md-typeset__table {
    line-height: 1;
}

.md-typeset__table table:not([class]) {
    font-size: .74rem;
    border-right: none;
}

.md-typeset__table table:not([class]) td,
.md-typeset__table table:not([class]) th {
    padding: 9px;
}

/* light mode alternating table bg colors */
.md-typeset__table tr:nth-child(2n) {
    background-color: #f8f8f8;
}

/* dark mode alternating table bg colors */
[data-md-color-scheme="slate"] .md-typeset__table tr:nth-child(2n) {
    background-color: hsla(var(--md-hue),25%,25%,1)
}
/* Table format like GitHub ----------------------------------------------------------------------------------------- */

/* Code block vertical scroll */
div.highlight {
    max-height: 20rem;
    overflow-y: auto; /* for adding a scrollbar when needed */
}