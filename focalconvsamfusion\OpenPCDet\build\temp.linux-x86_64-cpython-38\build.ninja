ninja_required_version = 1.3
cxx = /data/liufeifei/conda_envs/robofusion-main/bin/x86_64-conda-linux-gnu-c++
nvcc = /data/liufeifei/conda_envs/robofusion-main/cuda-11.1/bin/nvcc

cflags = -Wno-unused-result -Wsign-compare -DNDEBUG -fwrapv -O2 -Wall -Wstrict-prototypes -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -pipe -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -pipe -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem /data/liufeifei/conda_envs/robofusion-main/include -DNDEBUG -D_FORTIFY_SOURCE=2 -O2 -isystem /data/liufeifei/conda_envs/robofusion-main/include -fPIC -I/data/liufeifei/conda_envs/agentformer/lib/python3.8/site-packages/torch/include -I/data/liufeifei/conda_envs/agentformer/lib/python3.8/site-packages/torch/include/torch/csrc/api/include -I/data/liufeifei/conda_envs/agentformer/lib/python3.8/site-packages/torch/include/TH -I/data/liufeifei/conda_envs/agentformer/lib/python3.8/site-packages/torch/include/THC -I/data/liufeifei/conda_envs/robofusion-main/cuda-11.1/include -I/data/liufeifei/conda_envs/robofusion-main/include/python3.8 -c
post_cflags = -DTORCH_API_INCLUDE_EXTENSION_H '-DPYBIND11_COMPILER_TYPE="_gcc"' '-DPYBIND11_STDLIB="_libstdcpp"' '-DPYBIND11_BUILD_ABI="_cxxabi1011"' -DTORCH_EXTENSION_NAME=pointnet2_batch_cuda -D_GLIBCXX_USE_CXX11_ABI=0 -std=c++14
cuda_cflags = -I/data/liufeifei/conda_envs/agentformer/lib/python3.8/site-packages/torch/include -I/data/liufeifei/conda_envs/agentformer/lib/python3.8/site-packages/torch/include/torch/csrc/api/include -I/data/liufeifei/conda_envs/agentformer/lib/python3.8/site-packages/torch/include/TH -I/data/liufeifei/conda_envs/agentformer/lib/python3.8/site-packages/torch/include/THC -I/data/liufeifei/conda_envs/robofusion-main/cuda-11.1/include -I/data/liufeifei/conda_envs/robofusion-main/include/python3.8 -c
cuda_post_cflags = -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr --compiler-options ''"'"'-fPIC'"'"'' -DTORCH_API_INCLUDE_EXTENSION_H '-DPYBIND11_COMPILER_TYPE="_gcc"' '-DPYBIND11_STDLIB="_libstdcpp"' '-DPYBIND11_BUILD_ABI="_cxxabi1011"' -DTORCH_EXTENSION_NAME=pointnet2_batch_cuda -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_86,code=sm_86 -ccbin /data/liufeifei/conda_envs/robofusion-main/bin/x86_64-conda-linux-gnu-cc -std=c++14
ldflags = 

rule compile
  command = $cxx -MMD -MF $out.d $cflags -c $in -o $out $post_cflags
  depfile = $out.d
  deps = gcc

rule cuda_compile
  depfile = $out.d
  deps = gcc
  command = $nvcc --generate-dependencies-with-compile --dependency-output $out.d $cuda_cflags -c $in -o $out $cuda_post_cflags



build /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/build/temp.linux-x86_64-cpython-38/pcdet/ops/pointnet2/pointnet2_batch/src/ball_query.o: compile /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/pcdet/ops/pointnet2/pointnet2_batch/src/ball_query.cpp
build /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/build/temp.linux-x86_64-cpython-38/pcdet/ops/pointnet2/pointnet2_batch/src/ball_query_gpu.o: cuda_compile /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/pcdet/ops/pointnet2/pointnet2_batch/src/ball_query_gpu.cu
build /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/build/temp.linux-x86_64-cpython-38/pcdet/ops/pointnet2/pointnet2_batch/src/group_points.o: compile /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/pcdet/ops/pointnet2/pointnet2_batch/src/group_points.cpp
build /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/build/temp.linux-x86_64-cpython-38/pcdet/ops/pointnet2/pointnet2_batch/src/group_points_gpu.o: cuda_compile /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/pcdet/ops/pointnet2/pointnet2_batch/src/group_points_gpu.cu
build /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/build/temp.linux-x86_64-cpython-38/pcdet/ops/pointnet2/pointnet2_batch/src/interpolate.o: compile /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/pcdet/ops/pointnet2/pointnet2_batch/src/interpolate.cpp
build /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/build/temp.linux-x86_64-cpython-38/pcdet/ops/pointnet2/pointnet2_batch/src/interpolate_gpu.o: cuda_compile /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/pcdet/ops/pointnet2/pointnet2_batch/src/interpolate_gpu.cu
build /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/build/temp.linux-x86_64-cpython-38/pcdet/ops/pointnet2/pointnet2_batch/src/pointnet2_api.o: compile /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/pcdet/ops/pointnet2/pointnet2_batch/src/pointnet2_api.cpp
build /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/build/temp.linux-x86_64-cpython-38/pcdet/ops/pointnet2/pointnet2_batch/src/sampling.o: compile /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/pcdet/ops/pointnet2/pointnet2_batch/src/sampling.cpp
build /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/build/temp.linux-x86_64-cpython-38/pcdet/ops/pointnet2/pointnet2_batch/src/sampling_gpu.o: cuda_compile /data/liufeifei/project/RoboFusion-master/RoboFusion-master/focalconvsamfusion/OpenPCDet/pcdet/ops/pointnet2/pointnet2_batch/src/sampling_gpu.cu





