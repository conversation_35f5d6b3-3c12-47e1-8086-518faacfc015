## Ultralytics YOLOv8 Example Applications

This repository features a collection of real-world applications and walkthroughs, provided as either Python files or notebooks. Explore the examples below to see how YOLOv8 can be integrated into various applications.

### Ultralytics YOLO Example Applications

| Title                                                                                                          | Format             | Contributor                                         |
| -------------------------------------------------------------------------------------------------------------- | ------------------ | --------------------------------------------------- |
| [YOLO ONNX Detection Inference with C++](./YOLOv8-CPP-Inference)                                               | C++/ONNX           | [<PERSON><PERSON>](https://github.com/JustasBart)   |
| [YOLO OpenCV ONNX Detection Python](./YOLOv8-OpenCV-ONNX-Python)                                               | OpenCV/Python/ONNX | [Farid Inawan](https://github.com/frdteknikelektro) |
| [YOLO .NET ONNX ImageSharp](https://github.com/dme-compunet/YOLOv8)                                            | C#/ONNX/ImageSharp | [Compuet](https://github.com/dme-compunet)          |
| [YOLO .Net ONNX Detection C#](https://www.nuget.org/packages/Yolov8.Net)                                       | C# .Net            | [Samuel Stainback](https://github.com/sstainba)     |
| [YOLOv8 on NVIDIA Jetson(TensorRT and DeepStream)](https://wiki.seeedstudio.com/YOLOv8-DeepStream-TRT-Jetson/) | Python             | [Lakshantha](https://github.com/lakshanthad)        |
| [YOLOv8 ONNXRuntime Python](./YOLOv8-ONNXRuntime)                                                              | Python/ONNXRuntime | [Semih Demirel](https://github.com/semihhdemirel)   |

### How to Contribute

We welcome contributions from the community in the form of examples, applications, and guides. To contribute, please follow these steps:

1. Create a pull request (PR) with the `[Example]` prefix in the title, adding your project folder to the `examples/` directory in the repository.
1. Ensure that your project meets the following criteria:
   - Utilizes the `ultralytics` package.
   - Includes a `README.md` file with instructions on how to run the project.
   - Avoids adding large assets or dependencies unless absolutely necessary.
   - The contributor is expected to provide support for issues related to their examples.

If you have any questions or concerns about these requirements, please submit a PR, and we will be more than happy to guide you.
