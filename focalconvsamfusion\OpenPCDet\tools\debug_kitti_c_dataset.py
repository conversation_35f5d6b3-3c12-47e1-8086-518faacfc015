#!/usr/bin/env python3
"""
KITTI-C数据集诊断脚本
检查数据集路径、文件结构和配置
"""

import _init_path
import argparse
from pathlib import Path
from pcdet.config import cfg, cfg_from_yaml_file

def check_config():
    """检查配置文件"""
    print("🔍 检查配置文件...")
    
    cfg_file = 'cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml'
    try:
        cfg_from_yaml_file(cfg_file, cfg)
        print(f"✅ 配置文件加载成功: {cfg_file}")
        
        print(f"📂 数据路径: {cfg.DATA_CONFIG.DATA_PATH}")
        print(f"📄 Info文件路径: {cfg.DATA_CONFIG.INFO_PATH}")
        
        return True
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False

def check_data_path(data_path):
    """检查数据路径"""
    print(f"\n🔍 检查数据路径: {data_path}")
    
    data_path = Path(data_path)
    if not data_path.exists():
        print(f"❌ 数据路径不存在: {data_path}")
        return False
    
    print(f"✅ 数据路径存在")
    
    # 列出子目录
    subdirs = [d for d in data_path.iterdir() if d.is_dir()]
    print(f"📁 子目录 ({len(subdirs)}个):")
    for subdir in sorted(subdirs):
        print(f"  - {subdir.name}")
    
    return True

def check_corruption_data(data_path, corruption_type):
    """检查特定腐败类型的数据"""
    print(f"\n🔍 检查 {corruption_type} 数据...")
    
    data_path = Path(data_path)
    corruption_path = data_path / corruption_type
    
    if not corruption_path.exists():
        print(f"❌ {corruption_type} 目录不存在: {corruption_path}")
        return False
    
    print(f"✅ {corruption_type} 目录存在")
    
    # 检查必需的子目录
    required_dirs = ['images', 'velodyne', 'calib', 'labels']
    for dir_name in required_dirs:
        dir_path = corruption_path / dir_name
        if dir_path.exists():
            print(f"  ✅ {dir_name}/ 存在")
            
            # 检查val子目录
            if dir_name in ['images', 'velodyne', 'labels']:
                val_dir = dir_path / 'val'
                if val_dir.exists():
                    file_count = len(list(val_dir.glob('*')))
                    print(f"    ✅ {dir_name}/val/ 存在，{file_count} 个文件")
                else:
                    print(f"    ❌ {dir_name}/val/ 不存在")
        else:
            print(f"  ❌ {dir_name}/ 不存在")
    
    # 检查info文件
    info_file = data_path / f'kitti_c_infos_val_{corruption_type}.pkl'
    if info_file.exists():
        print(f"  ✅ Info文件存在: {info_file.name}")
        print(f"    📊 文件大小: {info_file.stat().st_size / 1024 / 1024:.2f} MB")
    else:
        print(f"  ❌ Info文件不存在: {info_file.name}")
        
        # 查找可能的info文件
        possible_info_files = list(data_path.glob('*kitti_c_infos*.pkl'))
        if possible_info_files:
            print(f"  🔍 发现其他info文件:")
            for info_file in possible_info_files:
                print(f"    - {info_file.name}")
    
    return True

def check_sample_files(data_path, corruption_type):
    """检查样本文件"""
    print(f"\n🔍 检查 {corruption_type} 样本文件...")
    
    data_path = Path(data_path)
    corruption_path = data_path / corruption_type
    
    # 检查图像文件
    images_dir = corruption_path / 'images' / 'val'
    if images_dir.exists():
        image_files = list(images_dir.glob('*.png'))[:3]
        print(f"📸 图像文件示例 ({len(list(images_dir.glob('*.png')))} 个):")
        for img_file in image_files:
            size_mb = img_file.stat().st_size / 1024 / 1024
            print(f"  - {img_file.name} ({size_mb:.2f} MB)")
    
    # 检查点云文件
    velodyne_dir = corruption_path / 'velodyne' / 'val'
    if velodyne_dir.exists():
        velodyne_files = list(velodyne_dir.glob('*.npy'))[:3]
        print(f"☁️ 点云文件示例 ({len(list(velodyne_dir.glob('*.npy')))} 个):")
        for vel_file in velodyne_files:
            size_mb = vel_file.stat().st_size / 1024 / 1024
            print(f"  - {vel_file.name} ({size_mb:.2f} MB)")

def main():
    parser = argparse.ArgumentParser(description='KITTI-C数据集诊断')
    parser.add_argument('--corruption', type=str, default='fog',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='要检查的腐败类型')
    args = parser.parse_args()
    
    print("🚀 KITTI-C数据集诊断")
    print("=" * 50)
    
    # 1. 检查配置
    if not check_config():
        return
    
    # 2. 检查数据路径
    if not check_data_path(cfg.DATA_CONFIG.DATA_PATH):
        return
    
    # 3. 检查特定腐败类型数据
    if not check_corruption_data(cfg.DATA_CONFIG.DATA_PATH, args.corruption):
        return
    
    # 4. 检查样本文件
    check_sample_files(cfg.DATA_CONFIG.DATA_PATH, args.corruption)
    
    print("\n" + "=" * 50)
    print("✅ 诊断完成")
    
    print(f"\n💡 如果要运行KITTI-C测试，使用:")
    print(f"python test_kitti_c_simple.py --corruption {args.corruption}")

if __name__ == '__main__':
    main()
