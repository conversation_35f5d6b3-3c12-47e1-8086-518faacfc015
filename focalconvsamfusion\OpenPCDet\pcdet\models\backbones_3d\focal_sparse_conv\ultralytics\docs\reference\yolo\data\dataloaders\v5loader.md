---
description: Efficiently load images and labels to models using Ultralytics YOLO's InfiniteDataLoader, LoadScreenshots, and LoadStreams.
keywords: YOLO, data loader, image classification, object detection, Ultralytics
---

## InfiniteDataLoader
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.InfiniteDataLoader
<br><br>

## _RepeatSampler
---
### ::: ultralytics.yolo.data.dataloaders.v5loader._RepeatSampler
<br><br>

## LoadScreenshots
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.LoadScreenshots
<br><br>

## LoadImages
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.LoadImages
<br><br>

## LoadStreams
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.LoadStreams
<br><br>

## LoadImagesAndLabels
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.LoadImagesAndLabels
<br><br>

## ClassificationDataset
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.ClassificationDataset
<br><br>

## get_hash
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.get_hash
<br><br>

## exif_size
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.exif_size
<br><br>

## exif_transpose
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.exif_transpose
<br><br>

## seed_worker
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.seed_worker
<br><br>

## create_dataloader
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.create_dataloader
<br><br>

## img2label_paths
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.img2label_paths
<br><br>

## flatten_recursive
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.flatten_recursive
<br><br>

## extract_boxes
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.extract_boxes
<br><br>

## autosplit
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.autosplit
<br><br>

## verify_image_label
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.verify_image_label
<br><br>

## create_classification_dataloader
---
### ::: ultralytics.yolo.data.dataloaders.v5loader.create_classification_dataloader
<br><br>
