#!/usr/bin/env python3
"""
KITTI-C内存优化测试脚本
针对GPU内存不足的情况进行优化
"""

import _init_path
import argparse
import datetime
import glob
import os
import re
import time
import gc
import torch
from pathlib import Path

import numpy as np
from tensorboardX import SummaryWriter

from eval_utils import eval_utils
from pcdet.config import cfg, cfg_from_list, cfg_from_yaml_file, log_config_to_file
from pcdet.datasets import build_dataloader
from pcdet.models import build_network
from pcdet.utils import common_utils


def parse_config():
    parser = argparse.ArgumentParser(description='Memory optimized KITTI-C test')
    parser.add_argument('--cfg_file', type=str, default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml', 
                       help='specify the config for testing')
    parser.add_argument('--ckpt', type=str, default='../output/kitti_models/sam_onlinev3/robofusion_tmux/ckpt/checkpoint_epoch_80.pth', 
                       help='checkpoint to start from')
    parser.add_argument('--batch_size', type=int, default=1, help='batch size for testing')
    parser.add_argument('--workers', type=int, default=1, help='number of workers for dataloader')
    parser.add_argument('--extra_tag', type=str, default='kitti_c_memory_optimized', help='extra tag for this experiment')
    parser.add_argument('--save_to_file', action='store_true', default=True, help='save results to file')
    parser.add_argument('--infer_time', action='store_true', default=True, help='calculate inference latency')
    
    args = parser.parse_args()
    
    cfg_from_yaml_file(args.cfg_file, cfg)
    cfg.TAG = Path(args.cfg_file).stem
    cfg.EXP_GROUP_PATH = '/'.join(args.cfg_file.split('/')[1:-1])
    
    np.random.seed(1024)
    
    return args, cfg


def clear_memory():
    """清理GPU内存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()


def eval_single_ckpt_memory_optimized(model, test_loader, args, eval_output_dir, logger, epoch_id):
    """内存优化的单checkpoint评估"""
    
    # 加载checkpoint
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=False)
    model = model.cuda()
    model.eval()
    
    # 清理内存
    clear_memory()
    
    logger.info('*************** EPOCH %s EVALUATION *****************' % epoch_id)
    
    # 开始评估
    with torch.no_grad():
        eval_utils.eval_one_epoch(
            cfg, args, model, test_loader, epoch_id, logger, dist_test=False,
            result_dir=eval_output_dir, save_to_file=args.save_to_file
        )


def main():
    args, cfg = parse_config()
    
    # 强制使用单GPU
    dist_test = False
    total_gpus = 1
    
    # 设置batch size
    args.batch_size = 1  # 强制使用batch size 1
    
    # 创建输出目录
    output_dir = cfg.ROOT_DIR / 'output' / cfg.EXP_GROUP_PATH / cfg.TAG / args.extra_tag
    output_dir.mkdir(parents=True, exist_ok=True)
    
    eval_output_dir = output_dir / 'eval'
    
    # 从checkpoint文件名提取epoch信息
    num_list = re.findall(r'\d+', args.ckpt) if args.ckpt is not None else []
    epoch_id = num_list[-1] if num_list.__len__() > 0 else 'no_number'
    eval_output_dir = eval_output_dir / ('epoch_%s' % epoch_id) / cfg.DATA_CONFIG.DATA_SPLIT['test']
    
    eval_output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    log_file = eval_output_dir / ('log_eval_%s.txt' % datetime.datetime.now().strftime('%Y%m%d-%H%M%S'))
    logger = common_utils.create_logger(log_file, rank=cfg.LOCAL_RANK)
    
    # 记录配置信息
    logger.info('**********************Start logging**********************')
    gpu_list = os.environ['CUDA_VISIBLE_DEVICES'] if 'CUDA_VISIBLE_DEVICES' in os.environ.keys() else 'ALL'
    logger.info('CUDA_VISIBLE_DEVICES=%s' % gpu_list)
    
    logger.info('total_batch_size: %d' % args.batch_size)
    for key, val in vars(args).items():
        logger.info('{:16} {}'.format(key, val))
    log_config_to_file(cfg, logger=logger)
    
    # 构建数据加载器（内存优化）
    logger.info('Building dataset...')
    test_set, test_loader, sampler = build_dataloader(
        dataset_cfg=cfg.DATA_CONFIG,
        class_names=cfg.CLASS_NAMES,
        batch_size=args.batch_size,
        dist=dist_test, 
        workers=args.workers, 
        logger=logger, 
        training=False,
        corruptions=[None, None],  # 不使用额外的corruption
        severity=0, 
        save_cor_flag=False
    )
    
    logger.info(f'Dataset built successfully. Total samples: {len(test_set)}')
    
    # 构建模型
    logger.info('Building model...')
    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=test_set)
    
    # 清理内存
    clear_memory()
    
    logger.info('Model built successfully.')
    
    # 开始评估
    logger.info('Starting evaluation...')
    eval_single_ckpt_memory_optimized(model, test_loader, args, eval_output_dir, logger, epoch_id)
    
    logger.info('Evaluation completed successfully!')


if __name__ == '__main__':
    main()
