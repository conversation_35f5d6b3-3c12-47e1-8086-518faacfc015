#!/usr/bin/env python3
"""
KITTI-C结果分析脚本
用于分析KITTI-C测试结果并生成mAP报告

数据集结构要求：
KITTI-C/
├── fog/
│   ├── calib/          # 000000.txt
│   ├── images/val/     # 000000_fog_d1.png
│   ├── velodyne/val/   # 000000_fog_d1.npy
│   └── labels/val/     # 000000.txt (Ground Truth标注) ← 必需！
├── snow/labels/val/    # ← 必需！
├── rain/labels/val/    # ← 必需！
└── sun/labels/val/     # ← 必需！

如果缺少labels文件夹，将无法计算mAP！
"""
"""
KITTI-C结果分析脚本
分析检测结果的详细统计信息
"""

import pickle
import numpy as np
import argparse
from pathlib import Path


def analyze_detection_results(result_file):
    """分析检测结果"""
    
    print(f"=== 分析检测结果文件: {result_file} ===")
    
    # 加载结果
    try:
        with open(result_file, 'rb') as f:
            det_annos = pickle.load(f)
        print(f"✓ 成功加载结果文件，包含 {len(det_annos)} 个样本")
    except Exception as e:
        print(f"✗ 加载结果文件失败: {e}")
        return
    
    # 基本统计
    total_detections = 0
    confidence_scores = []
    detection_counts = []
    
    # 类别统计
    class_counts = {}
    
    # 置信度分布
    score_ranges = {
        '0.0-0.1': 0,
        '0.1-0.3': 0,
        '0.3-0.5': 0,
        '0.5-0.7': 0,
        '0.7-0.9': 0,
        '0.9-1.0': 0
    }
    
    print("\n=== 详细分析 ===")
    
    for i, anno in enumerate(det_annos):
        # 检测数量
        num_detections = len(anno.get('name', []))
        detection_counts.append(num_detections)
        total_detections += num_detections
        
        # 类别统计
        for name in anno.get('name', []):
            class_counts[name] = class_counts.get(name, 0) + 1
        
        # 置信度统计
        scores = anno.get('score', [])
        confidence_scores.extend(scores)
        
        for score in scores:
            if score < 0.1:
                score_ranges['0.0-0.1'] += 1
            elif score < 0.3:
                score_ranges['0.1-0.3'] += 1
            elif score < 0.5:
                score_ranges['0.3-0.5'] += 1
            elif score < 0.7:
                score_ranges['0.5-0.7'] += 1
            elif score < 0.9:
                score_ranges['0.7-0.9'] += 1
            else:
                score_ranges['0.9-1.0'] += 1
    
    # 输出统计结果
    print(f"总样本数: {len(det_annos)}")
    print(f"总检测数: {total_detections}")
    print(f"平均每帧检测数: {total_detections / len(det_annos):.2f}")
    
    print(f"\n=== 检测数量分布 ===")
    detection_counts = np.array(detection_counts)
    print(f"最少检测数: {detection_counts.min()}")
    print(f"最多检测数: {detection_counts.max()}")
    print(f"中位数检测数: {np.median(detection_counts):.1f}")
    print(f"标准差: {np.std(detection_counts):.2f}")
    
    # 检测数量直方图
    unique, counts = np.unique(detection_counts, return_counts=True)
    print(f"\n检测数量直方图:")
    for num, count in zip(unique, counts):
        print(f"  {num}个检测: {count}帧 ({count/len(det_annos)*100:.1f}%)")
    
    print(f"\n=== 类别统计 ===")
    for class_name, count in sorted(class_counts.items()):
        print(f"{class_name}: {count}个检测")
    
    print(f"\n=== 置信度分布 ===")
    if confidence_scores:
        confidence_scores = np.array(confidence_scores)
        print(f"平均置信度: {confidence_scores.mean():.3f}")
        print(f"置信度标准差: {confidence_scores.std():.3f}")
        print(f"最低置信度: {confidence_scores.min():.3f}")
        print(f"最高置信度: {confidence_scores.max():.3f}")
        
        print(f"\n置信度区间分布:")
        for range_name, count in score_ranges.items():
            percentage = count / len(confidence_scores) * 100 if len(confidence_scores) > 0 else 0
            print(f"  {range_name}: {count}个检测 ({percentage:.1f}%)")
    else:
        print("没有置信度信息")
    
    # 分析前几个样本的详细信息
    print(f"\n=== 前5个样本详细信息 ===")
    for i in range(min(5, len(det_annos))):
        anno = det_annos[i]
        print(f"\n样本 {i}:")
        print(f"  检测数量: {len(anno.get('name', []))}")
        if 'name' in anno and len(anno['name']) > 0:
            print(f"  类别: {list(anno['name'])}")
            if 'score' in anno:
                print(f"  置信度: {[f'{s:.3f}' for s in anno['score']]}")
            if 'boxes_lidar' in anno:
                print(f"  3D框数量: {len(anno['boxes_lidar'])}")


def find_result_files():
    """查找所有可能的结果文件"""
    base_path = Path('../output/kitti_models/sam_onlinev3_kitti_c_test')

    print("=== 查找结果文件 ===")

    # 查找所有可能的结果文件
    result_files = []

    # 查找 result.pkl 文件
    for pkl_file in base_path.rglob('result.pkl'):
        result_files.append(pkl_file)
        print(f"找到 result.pkl: {pkl_file}")

    # 查找 predictions.pkl 文件
    for pkl_file in base_path.rglob('predictions.pkl'):
        result_files.append(pkl_file)
        print(f"找到 predictions.pkl: {pkl_file}")

    # 查找其他 .pkl 文件
    for pkl_file in base_path.rglob('*.pkl'):
        if pkl_file.name not in ['result.pkl', 'predictions.pkl']:
            result_files.append(pkl_file)
            print(f"找到其他pkl文件: {pkl_file}")

    return result_files


def main():
    parser = argparse.ArgumentParser(description='分析KITTI-C检测结果')
    parser.add_argument('--result_file', type=str, default=None, help='结果文件路径')
    parser.add_argument('--find_all', action='store_true', help='查找并分析所有结果文件')

    args = parser.parse_args()

    if args.find_all or args.result_file is None:
        # 查找所有结果文件
        result_files = find_result_files()

        if not result_files:
            print("没有找到任何结果文件！")
            return

        print(f"\n找到 {len(result_files)} 个结果文件")

        # 分析每个文件
        for i, result_file in enumerate(result_files):
            print(f"\n{'='*60}")
            print(f"分析文件 {i+1}/{len(result_files)}: {result_file.name}")
            print(f"{'='*60}")
            analyze_detection_results(result_file)
    else:
        # 分析指定文件
        result_file = Path(args.result_file)
        if not result_file.exists():
            print(f"错误: 结果文件不存在: {result_file}")
            print("请检查路径是否正确，或使用 --find_all 查找所有文件")
            return

        analyze_detection_results(result_file)


if __name__ == '__main__':
    main()
