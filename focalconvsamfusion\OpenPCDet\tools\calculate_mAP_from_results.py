#!/usr/bin/env python3
"""
从已有的result.pkl文件计算mAP
专门解决你当前的问题
"""

import _init_path
import argparse
import pickle
import json
import numpy as np
from pathlib import Path

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import build_dataloader
from pcdet.utils import common_utils


def parse_args():
    parser = argparse.ArgumentParser(description='Calculate mAP from existing result.pkl')
    parser.add_argument('--result_file', type=str, required=True,
                       help='path to result.pkl file')
    parser.add_argument('--corruption', type=str, default='sun',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml',
                       help='config file')
    
    return parser.parse_args()


def main():
    args = parse_args()
    
    print("🧮 从结果文件计算mAP")
    print(f"📂 结果文件: {args.result_file}")
    print(f"📂 腐败类型: {args.corruption}")
    
    # 检查结果文件是否存在
    result_file = Path(args.result_file)
    if not result_file.exists():
        print(f"❌ 结果文件不存在: {result_file}")
        return
    
    # 加载配置
    cfg_from_yaml_file(args.cfg_file, cfg)
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption
    
    # 检查info文件
    info_file_path = Path(cfg.DATA_CONFIG.DATA_PATH) / f'kitti_c_infos_val_{args.corruption}.pkl'
    if not info_file_path.exists():
        print(f"❌ Info文件不存在: {info_file_path}")
        print("请先运行 fix_kitti_c_infos.py 生成包含标注的info文件")
        return
    
    print(f"✅ Info文件存在: {info_file_path}")
    
    try:
        # 构建数据集（只需要用于evaluation）
        print("📊 构建数据集...")
        test_set, _, _ = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=1,
            dist=False, workers=1, logger=None, training=False
        )
        
        print(f"✅ 数据集构建成功，样本数量: {len(test_set)}")
        
        # 加载检测结果
        print("📥 加载检测结果...")
        with open(result_file, 'rb') as f:
            det_annos = pickle.load(f)
        
        print(f"✅ 加载了 {len(det_annos)} 个检测结果")
        
        # 检查结果格式
        if det_annos and len(det_annos) > 0:
            first_result = det_annos[0]
            print(f"📋 结果格式检查:")
            print(f"  - 第一个结果类型: {type(first_result)}")
            if isinstance(first_result, dict):
                print(f"  - 包含字段: {list(first_result.keys())}")
                if 'name' in first_result:
                    print(f"  - 检测类别数: {len(first_result.get('name', []))}")
        
        # 计算mAP
        print("📈 计算mAP...")
        print("=" * 60)
        
        try:
            eval_results = test_set.evaluation(det_annos, cfg.CLASS_NAMES)
            
            if isinstance(eval_results, tuple):
                ap_result_str, ap_dict = eval_results
            else:
                ap_result_str = str(eval_results)
                ap_dict = eval_results if isinstance(eval_results, dict) else {}
            
            print("🎉 mAP计算完成!")
            print("📊 评估结果:")
            print("=" * 60)
            print(ap_result_str)
            print("=" * 60)
            
            # 保存结果到同一目录
            output_dir = result_file.parent
            
            # 保存JSON结果（转换numpy类型）
            def convert_numpy_types(obj):
                """递归转换numpy类型为Python原生类型"""
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, dict):
                    return {key: convert_numpy_types(value) for key, value in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy_types(item) for item in obj]
                else:
                    return obj

            # 转换ap_dict中的numpy类型
            ap_dict_serializable = convert_numpy_types(ap_dict)

            mAP_json_path = output_dir / f'mAP_results_{args.corruption}.json'
            with open(mAP_json_path, 'w') as f:
                json.dump(ap_dict_serializable, f, indent=2)
            
            # 保存文本结果
            mAP_text_path = output_dir / f'mAP_results_{args.corruption}.txt'
            with open(mAP_text_path, 'w') as f:
                f.write(f"KITTI-C {args.corruption.upper()} mAP Results\n")
                f.write("=" * 60 + "\n")
                f.write(ap_result_str)
                f.write("\n" + "=" * 60 + "\n")
            
            print(f"\n💾 结果已保存:")
            print(f"  📄 JSON: {mAP_json_path}")
            print(f"  📄 文本: {mAP_text_path}")
            
            # 提取并显示关键指标
            if ap_dict:
                print(f"\n📋 关键指标 ({args.corruption.upper()}):")
                for key, value in ap_dict.items():
                    if isinstance(value, (int, float)):
                        print(f"  {key}: {value:.4f}")
                    elif isinstance(value, list) and len(value) > 0:
                        if all(isinstance(v, (int, float)) for v in value):
                            print(f"  {key}: {[f'{v:.4f}' for v in value]}")
            
        except Exception as e:
            print(f"❌ mAP计算失败: {e}")
            print("🔍 错误详情:")
            import traceback
            traceback.print_exc()
            
            # 尝试检查数据集是否有ground truth
            print(f"\n🔍 检查数据集ground truth...")
            if hasattr(test_set, 'kitti_infos') and len(test_set.kitti_infos) > 0:
                first_info = test_set.kitti_infos[0]
                has_annos = 'annos' in first_info
                print(f"  - 第一个info包含annos: {has_annos}")
                if has_annos:
                    annos = first_info['annos']
                    if isinstance(annos, dict) and 'name' in annos:
                        print(f"  - 标注对象数量: {len(annos['name'])}")
                    else:
                        print(f"  - annos格式: {type(annos)}")
                else:
                    print("  ❌ 数据集缺少ground truth标注!")
                    print("  💡 请运行 fix_kitti_c_infos.py 重新生成包含标注的info文件")
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
