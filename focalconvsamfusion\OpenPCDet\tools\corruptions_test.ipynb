{"cells": [{"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import open3d\n", "from visual_utils import open3d_vis_utils as V"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["points = np.fromfile('/sda/dxg/OpenPCDet/data/kitti/testing/velodyne/000008.bin', dtype=np.float32, count=-1).reshape([-1,4])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["V.draw_scenes(points=points)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["from pcdet.utils.LiDAR_corruptions import rain_sim, snow_sim, fog_sim, scene_glare_noise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["points_rain = rain_sim(pointcloud=points, severity=5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["points_snow = snow_sim(pointcloud=points, severity=5)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["points_fog = fog_sim(pointcloud=points, severity=5)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["points_glare = scene_glare_noise(pointcloud=points, severity=5)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8.69 s ± 74 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%timeit points2 = snow_sim(pointcloud=points, severity=5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pcdet.utils.LiDAR_corruptions import moving_noise_bbox"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["import pickle\n", "with open ('../data/kitti/kitti_infos_train.pkl', 'rb') as f:\n", "    info = pickle.load(f)"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['point_cloud', 'image', 'calib', 'annos'])"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["info[0].keys()"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'num_features': 4, 'lidar_idx': '000000'}"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["info[0]['point_cloud']"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': array(['Pedestrian'], dtype='<U10'),\n", " 'truncated': array([0.]),\n", " 'occluded': array([0.]),\n", " 'alpha': array([-0.2]),\n", " 'bbox': array([[712.4 , 143.  , 810.73, 307.92]], dtype=float32),\n", " 'dimensions': array([[1.2 , 1.89, 0.48]]),\n", " 'location': array([[1.84, 1.47, 8.41]], dtype=float32),\n", " 'rotation_y': array([0.01]),\n", " 'score': array([-1.]),\n", " 'difficulty': array([0], dtype=int32),\n", " 'index': array([0], dtype=int32),\n", " 'gt_boxes_lidar': array([[ 8.73138142, -1.85591745, -0.65469939,  1.2       ,  0.48      ,\n", "          1.89      , -1.58079633]]),\n", " 'num_points_in_gt': array([377], dtype=int32)}"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["info[0]['annos']"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["V.draw_scenes(points=points)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["V.draw_scenes(points=points_glare)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pcdet.utils.Camera_corruptions import ImageBBoxOperation\n", "import torch\n", "from skimage import io\n", "\n", "\n", "img = io.imread('/sda/dxg/OpenPCDet/data/kitti/training/image_2/000008.png')\n", "severity = 5\n", "bbox_shear = ImageBBoxOperation(severity)\n", "img_bgr_255_np_uint8 = img\n", "bboxes_corners = results['gt_bboxes_3d'].corners\n", "bboxes_centers = results['gt_bboxes_3d'].center\n", "lidar2img = results['lidar2img'] \n", "img_rgb_255_np_uint8 = img_bgr_255_np_uint8[:, :, [2, 1, 0]]\n", "c = [0.05, 0.1, 0.15, 0.2, 0.25][bbox_shear.severity - 1]\n", "b = np.random.uniform(c - 0.05, c + 0.05) * np.random.choice([-1, 1])\n", "d = np.random.uniform(c - 0.05, c + 0.05) * np.random.choice([-1, 1])\n", "e = np.random.uniform(c - 0.05, c + 0.05) * np.random.choice([-1, 1])\n", "f = np.random.uniform(c - 0.05, c + 0.05) * np.random.choice([-1, 1])\n", "transform_matrix = torch.tensor([\n", "    [1, 0, b],\n", "    [d, 1, e],\n", "    [f, 0, 1]\n", "]).float()\n", "\n", "image_aug_rgb = bbox_shear(\n", "    image=img_rgb_255_np_uint8,\n", "    bboxes_centers=bboxes_centers,\n", "    bboxes_corners=bboxes_corners,\n", "    transform_matrix=transform_matrix,\n", "    lidar2img=lidar2img,\n", ")\n", "image_aug_bgr = image_aug_rgb[:, :, [2, 1, 0]]\n", "results['img'] = image_aug_bgr"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["log_eval_[None, None]_0_20230609-102337.txt\n", "log_eval_[None, None]_0_20230609-103018.txt\n"]}], "source": ["import os\n", "# import pathlib2\n", "path='../output'\n", "dataset = 'kitti'\n", "if dataset == 'kitti':\n", "    dataset = 'kitti_models'\n", "model = 'pointpillar'\n", "\n", "dir = os.path.join(path, dataset, model)\n", "for corruption in os.listdir(dir):\n", "    dir2 = os.path.join(dir, corruption, 'eval')\n", "    for epoch in os.listdir(dir2):\n", "        dir3 = os.path.join(dir2, epoch, 'val', 'default')\n", "        for log in os.listdir(dir3):\n", "            if 'pkl' in log:\n", "                continue\n", "            print(log)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'path' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m path \u001b[39m/\u001b[39m dataset \u001b[39m/\u001b[39m model\n", "\u001b[0;31mNameError\u001b[0m: name 'path' is not defined"]}], "source": ["path / dataset / model"]}], "metadata": {"kernelspec": {"display_name": "pcdetV6", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "e3acc441e30b53e87b3c2c8d40223225c0eec0cb5a45a0521505beeab62ca306"}}}, "nbformat": 4, "nbformat_minor": 2}