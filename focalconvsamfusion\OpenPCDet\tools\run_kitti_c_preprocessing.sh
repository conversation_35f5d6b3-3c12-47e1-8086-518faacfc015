#!/bin/bash

# KITTI-C数据集预处理脚本
# 专门为路径 /data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C 设计

echo "=== KITTI-C数据集预处理开始 ==="

# 设置工作目录
cd /data/liufeifei/project/RoboFusion-master/RoboFusion-master_lzp/focalconvsamfusion/OpenPCDet/tools

# 检查数据路径是否存在
DATA_PATH="/data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C"

if [ ! -d "$DATA_PATH" ]; then
    echo "错误: 数据路径不存在: $DATA_PATH"
    exit 1
fi

echo "数据路径确认: $DATA_PATH"

# 检查可用的腐败类型
echo "检查可用的腐败类型..."
for corruption in fog rain snow sun; do
    if [ -d "$DATA_PATH/$corruption" ]; then
        echo "找到腐败类型: $corruption"
        if [ -d "$DATA_PATH/$corruption/images" ] && [ -d "$DATA_PATH/$corruption/velodyne" ]; then
            echo "  - images目录: ✓"
            echo "  - velodyne目录: ✓"
        else
            echo "  - 警告: 缺少images或velodyne目录"
        fi
    fi
done

# 运行预处理脚本
echo ""
echo "开始运行预处理脚本..."
python create_kitti_c_data.py

# 检查生成的文件
echo ""
echo "检查生成的文件..."
if [ -f "$DATA_PATH/kitti_c_infos_train.pkl" ]; then
    echo "✓ kitti_c_infos_train.pkl 已生成"
else
    echo "✗ kitti_c_infos_train.pkl 未生成"
fi

if [ -f "$DATA_PATH/kitti_c_infos_val.pkl" ]; then
    echo "✓ kitti_c_infos_val.pkl 已生成"
else
    echo "✗ kitti_c_infos_val.pkl 未生成"
fi

if [ -f "$DATA_PATH/kitti_c_dbinfos_train.pkl" ]; then
    echo "✓ kitti_c_dbinfos_train.pkl 已生成"
else
    echo "✗ kitti_c_dbinfos_train.pkl 未生成"
fi

echo ""
echo "=== KITTI-C数据集预处理完成 ==="
echo ""
echo "现在可以运行训练命令:"
echo "CUDA_VISIBLE_DEVICES=0,1,2,3 python -m torch.distributed.launch \\"
echo "    --nproc_per_node=4 \\"
echo "    --master_port=29500 \\"
echo "    train.py \\"
echo "    --cfg_file cfgs/kitti_models/sam_onlinev3_kitti_c.yaml \\"
echo "    --launcher pytorch \\"
echo "    --extra_tag robofusion_kitti_c"
