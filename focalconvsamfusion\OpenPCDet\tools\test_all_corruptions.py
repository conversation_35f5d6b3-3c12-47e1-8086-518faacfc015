#!/usr/bin/env python3
"""
测试所有KITTI-C腐败类型的脚本
现在有了ground truth标注，可以获得完整的mAP结果
"""

import _init_path
import argparse
import datetime
import glob
import os
import re
import time
import gc
import torch
from pathlib import Path
import subprocess
import sys

import numpy as np
from tensorboardX import SummaryWriter

from eval_utils import eval_utils
from pcdet.config import cfg, cfg_from_list, cfg_from_yaml_file, log_config_to_file
from pcdet.datasets import build_dataloader
from pcdet.models import build_network
from pcdet.utils import common_utils


def test_corruption_type(corruption_type, cfg_file, ckpt_path, output_dir):
    """测试特定腐败类型"""
    
    print(f"\n{'='*60}")
    print(f"🧪 测试腐败类型: {corruption_type.upper()}")
    print(f"{'='*60}")
    
    # 修改配置文件以使用对应的info文件
    cfg_from_yaml_file(cfg_file, cfg)
    
    # 更新info文件路径
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{corruption_type}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = corruption_type
    
    # 设置输出目录
    cfg.TAG = f"sam_onlinev3_kitti_c_{corruption_type}"
    cfg.EXP_GROUP_PATH = 'kitti_models'
    
    # 创建输出目录
    eval_output_dir = output_dir / f'eval_{corruption_type}'
    eval_output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    log_file = eval_output_dir / f'log_eval_{corruption_type}.txt'
    logger = common_utils.create_logger(log_file, rank=cfg.LOCAL_RANK)
    
    # 记录配置
    logger.info('**********************Start logging**********************')
    gpu_list = os.environ.get('CUDA_VISIBLE_DEVICES', None)
    logger.info('CUDA_VISIBLE_DEVICES=%s' % gpu_list)
    
    for key, val in vars(cfg).items():
        logger.info('{:16} {}'.format(key, val))
    log_config_to_file(cfg, logger=logger)
    
    # 构建数据集
    test_set, test_loader, sampler = build_dataloader(
        dataset_cfg=cfg.DATA_CONFIG,
        class_names=cfg.CLASS_NAMES,
        batch_size=1,  # 使用batch size 1
        dist=False, workers=1, logger=logger, training=False
    )
    
    # 构建模型
    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=test_set)
    
    # 加载checkpoint
    model.load_params_from_file(filename=ckpt_path, logger=logger, to_cpu=False)
    model = model.cuda()
    model.eval()
    
    # 清理内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()
    
    logger.info(f'*************** 开始评估 {corruption_type.upper()} *****************')
    
    # 开始评估
    with torch.no_grad():
        eval_utils.eval_one_epoch(
            cfg, None, model, test_loader, 0, logger, dist_test=False,
            result_dir=eval_output_dir, save_to_file=True
        )
    
    logger.info(f'*************** 完成评估 {corruption_type.upper()} *****************')
    
    return eval_output_dir


def parse_results(result_dir):
    """解析评估结果"""
    
    result_file = result_dir / 'result.pkl'
    if not result_file.exists():
        return None
    
    # 查找日志文件中的结果
    log_files = list(result_dir.glob('log_*.txt'))
    if not log_files:
        return None
    
    log_file = log_files[0]
    results = {}
    
    with open(log_file, 'r') as f:
        lines = f.readlines()
    
    # 解析mAP结果
    for line in lines:
        if 'Car AP@' in line:
            results['ap_line'] = line.strip()
        elif 'bbox AP:' in line:
            results['bbox_ap'] = line.strip()
        elif 'bev  AP:' in line:
            results['bev_ap'] = line.strip()
        elif '3d   AP:' in line:
            results['3d_ap'] = line.strip()
    
    return results


def main():
    parser = argparse.ArgumentParser(description='Test all KITTI-C corruption types')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml',
                       help='specify the config for testing')
    parser.add_argument('--ckpt', type=str, 
                       default='../output/kitti_models/sam_onlinev3/robofusion_tmux/ckpt/checkpoint_epoch_80.pth',
                       help='checkpoint to start from')
    parser.add_argument('--output_dir', type=str, default='./output_all_corruptions',
                       help='output directory for results')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("🚀 开始测试所有KITTI-C腐败类型")
    print(f"📂 配置文件: {args.cfg_file}")
    print(f"📂 模型文件: {args.ckpt}")
    print(f"📂 输出目录: {output_dir}")
    
    # 测试所有腐败类型
    corruption_types = ['fog', 'snow', 'rain', 'sun']
    all_results = {}
    
    for corruption in corruption_types:
        try:
            result_dir = test_corruption_type(corruption, args.cfg_file, args.ckpt, output_dir)
            results = parse_results(result_dir)
            all_results[corruption] = results
            
            print(f"✅ {corruption.upper()} 测试完成")
            
        except Exception as e:
            print(f"❌ {corruption.upper()} 测试失败: {e}")
            all_results[corruption] = None
    
    # 生成汇总报告
    print(f"\n{'='*80}")
    print("📊 KITTI-C 腐败鲁棒性测试结果汇总")
    print(f"{'='*80}")
    
    for corruption in corruption_types:
        print(f"\n🔍 {corruption.upper()}:")
        if all_results[corruption]:
            for key, value in all_results[corruption].items():
                print(f"  {value}")
        else:
            print("  ❌ 测试失败或结果不可用")
    
    # 保存汇总结果
    summary_file = output_dir / 'corruption_summary.txt'
    with open(summary_file, 'w') as f:
        f.write("KITTI-C 腐败鲁棒性测试结果汇总\n")
        f.write("="*80 + "\n\n")
        
        for corruption in corruption_types:
            f.write(f"{corruption.upper()}:\n")
            if all_results[corruption]:
                for key, value in all_results[corruption].items():
                    f.write(f"  {value}\n")
            else:
                f.write("  测试失败或结果不可用\n")
            f.write("\n")
    
    print(f"\n💾 汇总结果已保存到: {summary_file}")
    print(f"🎉 所有测试完成！")


if __name__ == '__main__':
    main()
