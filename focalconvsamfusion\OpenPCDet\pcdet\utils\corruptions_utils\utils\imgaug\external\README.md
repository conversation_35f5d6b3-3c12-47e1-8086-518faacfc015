This directory is intended for libraries that are required, but not added to the dependencies.
That is e.g. the case to avoid bloating up the dependencies for small things
or because the libraries had to be somehow modified.

* `opensimplex.py`: https://github.com/lmas/opensimplex
* `poly_point_isect.py`: https://github.com/ideasman42/isect_segments-bent<PERSON>_<PERSON><PERSON><PERSON>
* `poly_point_isect_py2py3.py`: Same as `poly_point_isect.py`, but modified to also be compatible with python 2.7. 