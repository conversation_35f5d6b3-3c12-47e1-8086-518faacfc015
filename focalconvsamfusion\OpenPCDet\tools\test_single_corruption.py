#!/usr/bin/env python3
"""
测试单个KITTI-C腐败类型的脚本
现在有了ground truth标注，可以获得mAP结果
"""

import _init_path
import argparse
import datetime
import glob
import os
import re
import time
import gc
import torch
from pathlib import Path

import numpy as np
from tensorboardX import SummaryWriter

from eval_utils import eval_utils
from pcdet.config import cfg, cfg_from_list, cfg_from_yaml_file, log_config_to_file
from pcdet.datasets import build_dataloader
from pcdet.models import build_network
from pcdet.utils import common_utils


def parse_config():
    parser = argparse.ArgumentParser(description='Test single KITTI-C corruption type with mAP')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml',
                       help='specify the config for testing')
    parser.add_argument('--ckpt', type=str, 
                       default='../output/kitti_models/sam_onlinev3/robofusion_tmux/ckpt/checkpoint_epoch_80.pth',
                       help='checkpoint to start from')
    parser.add_argument('--corruption', type=str, default='fog',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type to test')
    parser.add_argument('--batch_size', type=int, default=1, help='batch size for testing')
    parser.add_argument('--workers', type=int, default=1, help='number of workers for dataloader')
    parser.add_argument('--extra_tag', type=str, default='with_gt', help='extra tag for this experiment')
    parser.add_argument('--save_to_file', action='store_true', default=True, help='save results to file')
    parser.add_argument('--infer_time', action='store_true', default=True, help='calculate inference latency')
    
    args = parser.parse_args()
    
    # 加载配置文件
    cfg_from_yaml_file(args.cfg_file, cfg)
    
    # 更新配置以使用对应的info文件
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption
    
    # 设置标签
    cfg.TAG = f"sam_onlinev3_kitti_c_{args.corruption}"
    cfg.EXP_GROUP_PATH = 'kitti_models'
    
    np.random.seed(1024)
    
    return args, cfg


def clear_memory():
    """清理GPU内存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()


def eval_single_ckpt_with_gt(model, test_loader, test_set, args, eval_output_dir, logger, epoch_id):
    """带ground truth的单checkpoint评估，包含mAP计算"""

    # 加载checkpoint
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=False)
    model = model.cuda()
    model.eval()

    # 清理内存
    clear_memory()

    logger.info(f'*************** 开始评估 {args.corruption.upper()} (带mAP计算) *****************')

    # 步骤1: 运行推理获取检测结果
    with torch.no_grad():
        eval_utils.eval_one_epoch(
            cfg, args, model, test_loader, epoch_id, logger, dist_test=False,
            result_dir=eval_output_dir, save_to_file=args.save_to_file
        )

    # 步骤2: 加载检测结果并计算mAP
    result_pkl_path = eval_output_dir / 'result.pkl'
    if result_pkl_path.exists():
        logger.info('*************** 开始计算mAP *****************')

        # 加载检测结果
        import pickle
        with open(result_pkl_path, 'rb') as f:
            det_annos = pickle.load(f)

        logger.info(f'加载了 {len(det_annos)} 个检测结果')

        # 调用数据集的evaluation方法计算mAP
        try:
            ap_result_str, ap_dict = test_set.evaluation(det_annos, cfg.CLASS_NAMES)

            logger.info('*************** mAP评估结果 *****************')
            logger.info('\n' + ap_result_str)

            # 保存mAP结果
            import json
            mAP_result_path = eval_output_dir / 'mAP_results.json'
            with open(mAP_result_path, 'w') as f:
                json.dump(ap_dict, f, indent=2)

            logger.info(f'mAP结果已保存到: {mAP_result_path}')

            # 在控制台显示关键结果
            print(f"\n📊 {args.corruption.upper()} mAP评估结果:")
            print(ap_result_str)

        except Exception as e:
            logger.error(f'mAP计算失败: {e}')
            print(f"❌ mAP计算失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        logger.warning(f'未找到结果文件: {result_pkl_path}')
        print(f"⚠️ 未找到结果文件: {result_pkl_path}")


def main():
    args, cfg = parse_config()
    
    print(f"🧪 测试腐败类型: {args.corruption.upper()}")
    print(f"📂 使用info文件: kitti_c_infos_val_{args.corruption}.pkl")
    print(f"📂 数据路径: {cfg.DATA_CONFIG.DATA_PATH}")
    
    # 检查info文件是否存在
    info_file_path = Path(cfg.DATA_CONFIG.DATA_PATH) / f'kitti_c_infos_val_{args.corruption}.pkl'
    if not info_file_path.exists():
        print(f"❌ Info文件不存在: {info_file_path}")
        print("请先运行 setup_kitti_c_labels.py 生成包含标注的info文件")
        return
    else:
        print(f"✅ Info文件存在: {info_file_path}")
    
    # 强制使用单GPU
    dist_test = False
    total_gpus = 1
    
    # 设置batch size
    args.batch_size = 1  # 强制使用batch size 1
    
    # 创建输出目录
    output_dir = cfg.ROOT_DIR / 'output' / cfg.EXP_GROUP_PATH / cfg.TAG / args.extra_tag
    output_dir.mkdir(parents=True, exist_ok=True)
    
    eval_output_dir = output_dir / 'eval'
    
    # 从checkpoint文件名提取epoch信息
    num_list = re.findall(r'\d+', args.ckpt) if args.ckpt is not None else []
    epoch_id = num_list[-1] if num_list.__len__() > 0 else 'no_number'
    eval_output_dir = eval_output_dir / ('epoch_%s' % epoch_id) / cfg.DATA_CONFIG.DATA_SPLIT['test']
    
    eval_output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    log_file = eval_output_dir / ('log_eval_%s' % datetime.datetime.now().strftime('%Y%m%d-%H%M%S'))
    logger = common_utils.create_logger(log_file, rank=cfg.LOCAL_RANK)
    
    # 记录配置
    logger.info('**********************Start logging**********************')
    gpu_list = os.environ.get('CUDA_VISIBLE_DEVICES', None)
    logger.info('CUDA_VISIBLE_DEVICES=%s' % gpu_list)
    
    for key, val in vars(cfg).items():
        logger.info('{:16} {}'.format(key, val))
    log_config_to_file(cfg, logger=logger)
    
    if args.infer_time:
        os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
    
    # 构建数据集
    test_set, test_loader, sampler = build_dataloader(
        dataset_cfg=cfg.DATA_CONFIG,
        class_names=cfg.CLASS_NAMES,
        batch_size=args.batch_size,
        dist=dist_test, workers=args.workers, logger=logger, training=False
    )
    
    # 构建模型
    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=test_set)
    
    logger.info(f'数据集样本数量: {len(test_set)}')
    logger.info(f'腐败类型: {args.corruption}')
    logger.info(f'Info文件: {cfg.DATA_CONFIG.INFO_PATH["test"]}')
    
    # 开始评估
    eval_single_ckpt_with_gt(model, test_loader, test_set, args, eval_output_dir, logger, epoch_id)
    
    logger.info('**********************End evaluation**********************')
    
    print(f"\n🎉 {args.corruption.upper()} 测试完成!")
    print(f"📊 结果保存在: {eval_output_dir}")
    print(f"📄 日志文件: {log_file}")
    
    # 查找并显示结果
    result_files = list(eval_output_dir.glob('result.pkl'))
    if result_files:
        print(f"✅ 结果文件: {result_files[0]}")
    
    # 显示日志中的关键结果
    if log_file.exists():
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        print(f"\n📊 关键结果:")
        for line in lines[-20:]:  # 显示最后20行
            if any(keyword in line for keyword in ['AP@', 'bbox AP:', 'bev  AP:', '3d   AP:', 'recall']):
                print(f"  {line.strip()}")


if __name__ == '__main__':
    main()
