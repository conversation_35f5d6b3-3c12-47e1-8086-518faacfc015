import _init_path
import argparse
import datetime
import glob
import os
from pathlib import Path
from test import repeat_eval_ckpt

import torch
import torch.nn as nn
from tensorboardX import SummaryWriter

from pcdet.config import cfg, cfg_from_list, cfg_from_yaml_file, log_config_to_file
from pcdet.datasets import build_dataloader
from pcdet.models import build_network, model_fn_decorator
from pcdet.utils import common_utils
from train_utils.optimization import build_optimizer, build_scheduler
from train_utils.train_utils import train_model
import warnings
warnings.filterwarnings("ignore")

def print_gpu_memory(stage_name, rank=0):
    """打印GPU内存使用情况"""
    if torch.cuda.is_available():
        current_device = torch.cuda.current_device()
        allocated = torch.cuda.memory_allocated(current_device) / 1024**3
        reserved = torch.cuda.memory_reserved(current_device) / 1024**3
        total = torch.cuda.get_device_properties(current_device).total_memory / 1024**3
        print(f"RANK {rank} - {stage_name}: GPU{current_device} Allocated={allocated:.2f}GB, Reserved={reserved:.2f}GB, Total={total:.2f}GB")

def parse_config():
    parser = argparse.ArgumentParser(description='arg parser')
    parser.add_argument('--cfg_file', type=str, default=None, help='specify the config for training')

    parser.add_argument('--batch_size', type=int, default=None, required=False, help='batch size for training')
    parser.add_argument('--epochs', type=int, default=None, required=False, help='number of epochs to train for')
    parser.add_argument('--workers', type=int, default=4, help='number of workers for dataloader')
    parser.add_argument('--extra_tag', type=str, default='default', help='extra tag for this experiment')
    parser.add_argument('--ckpt', type=str, default=None, help='checkpoint to start from')
    parser.add_argument('--pretrained_model', type=str, default=None, help='pretrained_model')
    parser.add_argument('--launcher', choices=['none', 'pytorch', 'slurm'], default='none')
    parser.add_argument('--tcp_port', type=int, default=34830, help='tcp port for distrbuted training')
    parser.add_argument('--master_port', type=int, default=2333, help='tcp port for distrbuted training')
    parser.add_argument('--sync_bn', action='store_true', default=False, help='whether to use sync bn')
    parser.add_argument('--fix_random_seed', action='store_true', default=False, help='')
    parser.add_argument('--ckpt_save_interval', type=int, default=1, help='number of training epochs')
    parser.add_argument('--local_rank', type=int, default=0, help='local rank for distributed training')
    parser.add_argument('--max_ckpt_save_num', type=int, default=10, help='max number of saved checkpoint')
    parser.add_argument('--merge_all_iters_to_one_epoch', action='store_true', default=False, help='')
    parser.add_argument('--set', dest='set_cfgs', default=None, nargs=argparse.REMAINDER,
                        help='set extra config keys if needed')

    parser.add_argument('--max_waiting_mins', type=int, default=0, help='max waiting minutes')
    parser.add_argument('--start_epoch', type=int, default=0, help='')
    parser.add_argument('--save_to_file', action='store_true', default=False, help='')

    args = parser.parse_args()

    cfg_from_yaml_file(args.cfg_file, cfg)
    cfg.TAG = Path(args.cfg_file).stem
    cfg.EXP_GROUP_PATH = '/'.join(args.cfg_file.split('/')[1:-1])  # remove 'cfgs' and 'xxxx.yaml'

    if args.set_cfgs is not None:
        cfg_from_list(args.set_cfgs, cfg)

    return args, cfg


def main():
    # 强制设置内存管理策略
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:256,garbage_collection_threshold:0.6'
    
    args, cfg = parse_config()
    
    print_gpu_memory("程序开始", rank=args.local_rank)
    
    if args.launcher == 'none':
        dist_train = False
        total_gpus = 1
        print("使用单GPU模式")
    else:
        total_gpus, cfg.LOCAL_RANK = getattr(common_utils, 'init_dist_%s' % args.launcher)(
            args.tcp_port, args.local_rank, backend='nccl'
        )
        print(f"RANK {cfg.LOCAL_RANK}: total_gpus={total_gpus}")
        dist_train = True
        
        # 确保每个进程使用正确的GPU
        torch.cuda.set_device(cfg.LOCAL_RANK)
        print(f"RANK {cfg.LOCAL_RANK}: 设置当前GPU为 {torch.cuda.current_device()}")

    print_gpu_memory("分布式初始化后", rank=cfg.LOCAL_RANK)

    if args.batch_size is None:
        args.batch_size = cfg.OPTIMIZATION.BATCH_SIZE_PER_GPU
    else:
        assert args.batch_size % total_gpus == 0, 'Batch size should match the number of gpus'
        args.batch_size = args.batch_size // total_gpus

    # 临时减小batch_size进行测试
    original_batch_size = args.batch_size
    args.batch_size = min(args.batch_size, 2)  # 最大为2进行测试
    print(f"RANK {cfg.LOCAL_RANK}: 原始batch_size={original_batch_size}, 测试batch_size={args.batch_size}")

    args.epochs = cfg.OPTIMIZATION.NUM_EPOCHS if args.epochs is None else args.epochs

    if args.fix_random_seed:
        common_utils.set_random_seed(666)

    output_dir = cfg.ROOT_DIR / 'output' / cfg.EXP_GROUP_PATH / cfg.TAG / args.extra_tag
    ckpt_dir = output_dir / 'ckpt'
    output_dir.mkdir(parents=True, exist_ok=True)
    ckpt_dir.mkdir(parents=True, exist_ok=True)

    log_file = output_dir / ('log_train_%s.txt' % datetime.datetime.now().strftime('%Y%m%d-%H%M%S'))
    logger = common_utils.create_logger(log_file, rank=cfg.LOCAL_RANK)

    # log to file
    logger.info('**********************Start logging**********************')
    gpu_list = os.environ['CUDA_VISIBLE_DEVICES'] if 'CUDA_VISIBLE_DEVICES' in os.environ.keys() else 'ALL'
    logger.info('CUDA_VISIBLE_DEVICES=%s' % gpu_list)

    if dist_train:
        logger.info('total_batch_size: %d' % (total_gpus * args.batch_size))
    for key, val in vars(args).items():
        logger.info('{:16} {}'.format(key, val))
    log_config_to_file(cfg, logger=logger)
    if cfg.LOCAL_RANK == 0:
        os.system('cp %s %s' % (args.cfg_file, output_dir))

    tb_log = SummaryWriter(log_dir=str(output_dir / 'tensorboard')) if cfg.LOCAL_RANK == 0 else None

    print_gpu_memory("开始创建数据加载器", rank=cfg.LOCAL_RANK)

    # -----------------------create dataloader & network & optimizer---------------------------
    train_set, train_loader, train_sampler = build_dataloader(
        dataset_cfg=cfg.DATA_CONFIG,
        class_names=cfg.CLASS_NAMES,
        batch_size=args.batch_size,
        dist=dist_train, 
        workers=1,  # 减少workers避免内存问题
        logger=logger,
        training=True,
        merge_all_iters_to_one_epoch=args.merge_all_iters_to_one_epoch,
        total_epochs=args.epochs
    )

    print_gpu_memory("数据加载器创建完成", rank=cfg.LOCAL_RANK)

    # 清理缓存
    torch.cuda.empty_cache()
    print_gpu_memory("清理缓存后", rank=cfg.LOCAL_RANK)

    print_gpu_memory("开始创建模型", rank=cfg.LOCAL_RANK)

    # 创建模型时先在CPU上创建，然后再移动到GPU
    with torch.cuda.device(cfg.LOCAL_RANK if dist_train else 0):
        model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=train_set)
        
    print_gpu_memory("模型创建完成(CPU)", rank=cfg.LOCAL_RANK)

    # 移动到GPU
    model = model.to(device=f'cuda:{cfg.LOCAL_RANK if dist_train else 0}')
    print_gpu_memory("模型移动到GPU", rank=cfg.LOCAL_RANK)

    if args.sync_bn:
        model = torch.nn.SyncBatchNorm.convert_sync_batchnorm(model)
        print_gpu_memory("SyncBN转换后", rank=cfg.LOCAL_RANK)

    # 清理缓存
    torch.cuda.empty_cache()
    print_gpu_memory("模型加载完成，清理缓存后", rank=cfg.LOCAL_RANK)

    optimizer = build_optimizer(model, cfg.OPTIMIZATION)
    print_gpu_memory("优化器创建完成", rank=cfg.LOCAL_RANK)

    # load checkpoint if it is possible
    start_epoch = it = 0
    last_epoch = -1
    if args.pretrained_model is not None:
        print_gpu_memory("开始加载预训练模型", rank=cfg.LOCAL_RANK)
        model.load_params_from_file(filename=args.pretrained_model, to_cpu=dist_train, logger=logger)
        print_gpu_memory("预训练模型加载完成", rank=cfg.LOCAL_RANK)

    if args.ckpt is not None:
        it, start_epoch = model.load_params_with_optimizer(args.ckpt, to_cpu=dist_train, optimizer=optimizer, logger=logger)
        last_epoch = start_epoch + 1
    else:
        ckpt_list = glob.glob(str(ckpt_dir / '*checkpoint_epoch_*.pth'))
        if len(ckpt_list) > 0:
            ckpt_list.sort(key=os.path.getmtime)
            it, start_epoch = model.load_params_with_optimizer(
                ckpt_list[-1], to_cpu=dist_train, optimizer=optimizer, logger=logger
            )
            last_epoch = start_epoch + 1

    print_gpu_memory("检查点加载完成", rank=cfg.LOCAL_RANK)

    model.train()  # before wrap to DistributedDataParallel to support fixed some parameters
    
    if dist_train:
        print(f"RANK {cfg.LOCAL_RANK}: 开始创建DistributedDataParallel")
        print_gpu_memory("DDP创建前", rank=cfg.LOCAL_RANK)
        
        # 确保模型在正确的设备上
        model = model.to(f'cuda:{cfg.LOCAL_RANK}')
        
        model = nn.parallel.DistributedDataParallel(
            model, 
            device_ids=[cfg.LOCAL_RANK], 
            output_device=cfg.LOCAL_RANK,
            find_unused_parameters=True,
            broadcast_buffers=False  # 可能有助于减少内存使用
        )
        print_gpu_memory("DDP创建完成", rank=cfg.LOCAL_RANK)
    else:
        print("非分布式训练，不创建DDP")
        
    logger.info(model)

    # 清理缓存
    torch.cuda.empty_cache()
    print_gpu_memory("DDP完成，清理缓存后", rank=cfg.LOCAL_RANK)

    lr_scheduler, lr_warmup_scheduler = build_scheduler(
        optimizer, total_iters_each_epoch=len(train_loader), total_epochs=args.epochs,
        last_epoch=last_epoch, optim_cfg=cfg.OPTIMIZATION
    )

    print_gpu_memory("调度器创建完成", rank=cfg.LOCAL_RANK)

    # 在开始训练前进行一次前向传播测试
    print(f"RANK {cfg.LOCAL_RANK}: 开始训练前内存测试...")
    try:
        # 获取一个batch数据进行测试
        data_iter = iter(train_loader)
        batch_dict = next(data_iter)
        print_gpu_memory("获取一个batch后", rank=cfg.LOCAL_RANK)
        
        # 测试前向传播
        with torch.no_grad():
            model.eval()
            if dist_train:
                ret_dict, tb_dict, disp_dict = model(batch_dict)
            else:
                ret_dict, tb_dict, disp_dict = model(batch_dict)
            print_gpu_memory("前向传播测试完成", rank=cfg.LOCAL_RANK)
            
        model.train()
        torch.cuda.empty_cache()
        print_gpu_memory("测试完成，清理缓存后", rank=cfg.LOCAL_RANK)
        
    except Exception as e:
        print(f"RANK {cfg.LOCAL_RANK}: 前向传播测试失败: {e}")
        print_gpu_memory("前向传播测试失败", rank=cfg.LOCAL_RANK)

    # -----------------------start training---------------------------
    logger.info('**********************Start training %s/%s(%s)**********************'
                % (cfg.EXP_GROUP_PATH, cfg.TAG, args.extra_tag))
    
    print_gpu_memory("开始正式训练", rank=cfg.LOCAL_RANK)
    
    train_model(
        model,
        optimizer,
        train_loader,
        model_func=model_fn_decorator(),
        lr_scheduler=lr_scheduler,
        optim_cfg=cfg.OPTIMIZATION,
        start_epoch=start_epoch,
        total_epochs=args.epochs,
        start_iter=it,
        rank=cfg.LOCAL_RANK,
        tb_log=tb_log,
        ckpt_save_dir=ckpt_dir,
        train_sampler=train_sampler,
        lr_warmup_scheduler=lr_warmup_scheduler,
        ckpt_save_interval=args.ckpt_save_interval,
        max_ckpt_save_num=args.max_ckpt_save_num,
        merge_all_iters_to_one_epoch=args.merge_all_iters_to_one_epoch
    )

    if hasattr(train_set, 'use_shared_memory') and train_set.use_shared_memory:
        train_set.clean_shared_memory()

    logger.info('**********************End training %s/%s(%s)**********************\n\n\n'
                % (cfg.EXP_GROUP_PATH, cfg.TAG, args.extra_tag))

    logger.info('**********************Start evaluation %s/%s(%s)**********************' %
                (cfg.EXP_GROUP_PATH, cfg.TAG, args.extra_tag))
    test_set, test_loader, sampler = build_dataloader(
        dataset_cfg=cfg.DATA_CONFIG,
        class_names=cfg.CLASS_NAMES,
        batch_size=args.batch_size,
        dist=dist_train, workers=args.workers, logger=logger, training=False
    )
    eval_output_dir = output_dir / 'eval' / 'eval_with_train'
    eval_output_dir.mkdir(parents=True, exist_ok=True)
    args.start_epoch = max(args.epochs - 10, 0)  # Only evaluate the last 10 epochs

    repeat_eval_ckpt(
        model.module if dist_train else model,
        test_loader, args, eval_output_dir, logger, ckpt_dir,
        dist_test=dist_train
    )
    logger.info('**********************End evaluation %s/%s(%s)**********************' %
                (cfg.EXP_GROUP_PATH, cfg.TAG, args.extra_tag))


if __name__ == '__main__':
    main()
# import _init_path
# import argparse
# import datetime
# import glob
# import os
# from pathlib import Path
# from test import repeat_eval_ckpt

# import torch
# import torch.nn as nn
# from tensorboardX import SummaryWriter

# from pcdet.config import cfg, cfg_from_list, cfg_from_yaml_file, log_config_to_file
# from pcdet.datasets import build_dataloader
# from pcdet.models import build_network, model_fn_decorator
# from pcdet.utils import common_utils
# from train_utils.optimization import build_optimizer, build_scheduler
# from train_utils.train_utils import train_model
# import warnings
# warnings.filterwarnings("ignore")
# # os.environ['MASTER_PORT'] = '8888'
# def parse_config():
#     parser = argparse.ArgumentParser(description='arg parser')
#     parser.add_argument('--cfg_file', type=str, default=None, help='specify the config for training')

#     parser.add_argument('--batch_size', type=int, default=None, required=False, help='batch size for training')
#     parser.add_argument('--epochs', type=int, default=None, required=False, help='number of epochs to train for')
#     parser.add_argument('--workers', type=int, default=4, help='number of workers for dataloader')
#     parser.add_argument('--extra_tag', type=str, default='default', help='extra tag for this experiment')
#     parser.add_argument('--ckpt', type=str, default=None, help='checkpoint to start from')
#     parser.add_argument('--pretrained_model', type=str, default=None, help='pretrained_model')
#     parser.add_argument('--launcher', choices=['none', 'pytorch', 'slurm'], default='none')
#     parser.add_argument('--tcp_port', type=int, default=34830, help='tcp port for distrbuted training')
#     parser.add_argument('--master_port', type=int, default=2333, help='tcp port for distrbuted training')
#     parser.add_argument('--sync_bn', action='store_true', default=False, help='whether to use sync bn')
#     parser.add_argument('--fix_random_seed', action='store_true', default=False, help='')
#     parser.add_argument('--ckpt_save_interval', type=int, default=1, help='number of training epochs')
#     parser.add_argument('--local_rank', type=int, default=0, help='local rank for distributed training')
#     parser.add_argument('--max_ckpt_save_num', type=int, default=10, help='max number of saved checkpoint')
#     parser.add_argument('--merge_all_iters_to_one_epoch', action='store_true', default=False, help='')
#     parser.add_argument('--set', dest='set_cfgs', default=None, nargs=argparse.REMAINDER,
#                         help='set extra config keys if needed')

#     parser.add_argument('--max_waiting_mins', type=int, default=0, help='max waiting minutes')
#     parser.add_argument('--start_epoch', type=int, default=0, help='')
#     parser.add_argument('--save_to_file', action='store_true', default=False, help='')

#     args = parser.parse_args()

#     cfg_from_yaml_file(args.cfg_file, cfg)
#     cfg.TAG = Path(args.cfg_file).stem
#     cfg.EXP_GROUP_PATH = '/'.join(args.cfg_file.split('/')[1:-1])  # remove 'cfgs' and 'xxxx.yaml'

#     if args.set_cfgs is not None:
#         cfg_from_list(args.set_cfgs, cfg)

#     return args, cfg


# def main():
#     # os.environ['MASTER_PORT'] = '8888'
#     args, cfg = parse_config()
#     if args.launcher == 'none':
#         dist_train = False
#         total_gpus = 2
#     else:
#         total_gpus, cfg.LOCAL_RANK = getattr(common_utils, 'init_dist_%s' % args.launcher)(
#             args.tcp_port, args.local_rank, backend='nccl'
#         )
#         print("total_gpus",total_gpus)
#         dist_train = True

#     if args.batch_size is None:
#         args.batch_size = cfg.OPTIMIZATION.BATCH_SIZE_PER_GPU
#     else:
#         assert args.batch_size % total_gpus == 0, 'Batch size should match the number of gpus'
#         args.batch_size = args.batch_size // total_gpus

#     args.epochs = cfg.OPTIMIZATION.NUM_EPOCHS if args.epochs is None else args.epochs

#     if args.fix_random_seed:
#         common_utils.set_random_seed(666)

#     output_dir = cfg.ROOT_DIR / 'output' / cfg.EXP_GROUP_PATH / cfg.TAG / args.extra_tag
#     ckpt_dir = output_dir / 'ckpt'
#     output_dir.mkdir(parents=True, exist_ok=True)
#     ckpt_dir.mkdir(parents=True, exist_ok=True)

#     log_file = output_dir / ('log_train_%s.txt' % datetime.datetime.now().strftime('%Y%m%d-%H%M%S'))
#     logger = common_utils.create_logger(log_file, rank=cfg.LOCAL_RANK)

#     # log to file
#     logger.info('**********************Start logging**********************')
#     gpu_list = os.environ['CUDA_VISIBLE_DEVICES'] if 'CUDA_VISIBLE_DEVICES' in os.environ.keys() else 'ALL'
#     logger.info('CUDA_VISIBLE_DEVICES=%s' % gpu_list)

#     if dist_train:
#         logger.info('total_batch_size: %d' % (total_gpus * args.batch_size))
#     for key, val in vars(args).items():
#         logger.info('{:16} {}'.format(key, val))
#     log_config_to_file(cfg, logger=logger)
#     if cfg.LOCAL_RANK == 0:
#         os.system('cp %s %s' % (args.cfg_file, output_dir))

#     tb_log = SummaryWriter(log_dir=str(output_dir / 'tensorboard')) if cfg.LOCAL_RANK == 0 else None

#     # -----------------------create dataloader & network & optimizer---------------------------
#     train_set, train_loader, train_sampler = build_dataloader(
#         dataset_cfg=cfg.DATA_CONFIG,
#         class_names=cfg.CLASS_NAMES,
#         batch_size=args.batch_size,
#         dist=dist_train, workers=args.workers,
#         logger=logger,
#         training=True,
#         merge_all_iters_to_one_epoch=args.merge_all_iters_to_one_epoch,
#         total_epochs=args.epochs
#     )

#     model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=train_set).to(device='cuda')
#     if args.sync_bn:
#         model = torch.nn.SyncBatchNorm.convert_sync_batchnorm(model)
#     model.cuda()

#     optimizer = build_optimizer(model, cfg.OPTIMIZATION)

#     # load checkpoint if it is possible
#     start_epoch = it = 0
#     last_epoch = -1
#     if args.pretrained_model is not None:
#         model.load_params_from_file(filename=args.pretrained_model, to_cpu=dist_train, logger=logger)

#     if args.ckpt is not None:
#         it, start_epoch = model.load_params_with_optimizer(args.ckpt, to_cpu=dist_train, optimizer=optimizer, logger=logger)
#         last_epoch = start_epoch + 1
#     else:
#         ckpt_list = glob.glob(str(ckpt_dir / '*checkpoint_epoch_*.pth'))
#         if len(ckpt_list) > 0:
#             ckpt_list.sort(key=os.path.getmtime)
#             it, start_epoch = model.load_params_with_optimizer(
#                 ckpt_list[-1], to_cpu=dist_train, optimizer=optimizer, logger=logger
#             )
#             last_epoch = start_epoch + 1

#     model.train()  # before wrap to DistributedDataParallel to support fixed some parameters
#     if dist_train:
#         model = nn.parallel.DistributedDataParallel(model, device_ids=[cfg.LOCAL_RANK % torch.cuda.device_count()], \
#             find_unused_parameters=True)
#     logger.info(model)

#     lr_scheduler, lr_warmup_scheduler = build_scheduler(
#         optimizer, total_iters_each_epoch=len(train_loader), total_epochs=args.epochs,
#         last_epoch=last_epoch, optim_cfg=cfg.OPTIMIZATION
#     )

#     # -----------------------start training---------------------------
#     logger.info('**********************Start training %s/%s(%s)**********************'
#                 % (cfg.EXP_GROUP_PATH, cfg.TAG, args.extra_tag))
#     train_model(
#         model,
#         optimizer,
#         train_loader,
#         model_func=model_fn_decorator(),
#         lr_scheduler=lr_scheduler,
#         optim_cfg=cfg.OPTIMIZATION,
#         start_epoch=start_epoch,
#         total_epochs=args.epochs,
#         start_iter=it,
#         rank=cfg.LOCAL_RANK,
#         tb_log=tb_log,
#         ckpt_save_dir=ckpt_dir,
#         train_sampler=train_sampler,
#         lr_warmup_scheduler=lr_warmup_scheduler,
#         ckpt_save_interval=args.ckpt_save_interval,
#         max_ckpt_save_num=args.max_ckpt_save_num,
#         merge_all_iters_to_one_epoch=args.merge_all_iters_to_one_epoch
#     )

#     if hasattr(train_set, 'use_shared_memory') and train_set.use_shared_memory:
#         train_set.clean_shared_memory()

#     logger.info('**********************End training %s/%s(%s)**********************\n\n\n'
#                 % (cfg.EXP_GROUP_PATH, cfg.TAG, args.extra_tag))

#     logger.info('**********************Start evaluation %s/%s(%s)**********************' %
#                 (cfg.EXP_GROUP_PATH, cfg.TAG, args.extra_tag))
#     test_set, test_loader, sampler = build_dataloader(
#         dataset_cfg=cfg.DATA_CONFIG,
#         class_names=cfg.CLASS_NAMES,
#         batch_size=args.batch_size,
#         dist=dist_train, workers=args.workers, logger=logger, training=False
#     )
#     eval_output_dir = output_dir / 'eval' / 'eval_with_train'
#     eval_output_dir.mkdir(parents=True, exist_ok=True)
#     args.start_epoch = max(args.epochs - 10, 0)  # Only evaluate the last 10 epochs

#     repeat_eval_ckpt(
#         model.module if dist_train else model,
#         test_loader, args, eval_output_dir, logger, ckpt_dir,
#         dist_test=dist_train
#     )
#     logger.info('**********************End evaluation %s/%s(%s)**********************' %
#                 (cfg.EXP_GROUP_PATH, cfg.TAG, args.extra_tag))


# if __name__ == '__main__':
#     main()
