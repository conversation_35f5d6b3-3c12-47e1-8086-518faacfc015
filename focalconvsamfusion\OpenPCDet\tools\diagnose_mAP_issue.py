#!/usr/bin/env python3
"""
诊断mAP为0的问题
检查检测结果和GT的匹配情况
"""

import _init_path
import argparse
import pickle
import numpy as np
import copy
from pathlib import Path

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import build_dataloader
from pcdet.datasets.kitti.kitti_object_eval_python import eval as kitti_eval


def parse_args():
    parser = argparse.ArgumentParser(description='Diagnose mAP issue')
    parser.add_argument('--result_file', type=str, required=True,
                       help='path to result.pkl file')
    parser.add_argument('--corruption', type=str, default='sun',
                       choices=['fog', 'snow', 'rain', 'sun'],
                       help='corruption type')
    parser.add_argument('--cfg_file', type=str, 
                       default='cfgs/kitti_models/sam_onlinev3_kitti_c_original.yaml',
                       help='config file')
    parser.add_argument('--sample_count', type=int, default=5,
                       help='number of samples to analyze')
    
    return parser.parse_args()


def analyze_coordinate_ranges(det_annos, gt_annos):
    """分析坐标范围"""
    
    print("\n🔍 坐标范围分析:")
    
    # 收集检测结果的坐标
    det_locations = []
    det_dimensions = []
    det_rotations = []
    
    for det in det_annos:
        if isinstance(det, dict) and 'location' in det and len(det['location']) > 0:
            det_locations.extend(det['location'])
            det_dimensions.extend(det['dimensions'])
            det_rotations.extend(det['rotation_y'])
    
    # 收集GT的坐标
    gt_locations = []
    gt_dimensions = []
    gt_rotations = []
    
    for gt in gt_annos:
        if isinstance(gt, dict) and 'location' in gt and len(gt['location']) > 0:
            gt_locations.extend(gt['location'])
            gt_dimensions.extend(gt['dimensions'])
            gt_rotations.extend(gt['rotation_y'])
    
    if det_locations and gt_locations:
        det_locations = np.array(det_locations)
        gt_locations = np.array(gt_locations)
        det_dimensions = np.array(det_dimensions)
        gt_dimensions = np.array(gt_dimensions)
        det_rotations = np.array(det_rotations)
        gt_rotations = np.array(gt_rotations)
        
        print(f"📊 检测结果统计:")
        print(f"  位置范围 - X: [{det_locations[:, 0].min():.2f}, {det_locations[:, 0].max():.2f}]")
        print(f"  位置范围 - Y: [{det_locations[:, 1].min():.2f}, {det_locations[:, 1].max():.2f}]")
        print(f"  位置范围 - Z: [{det_locations[:, 2].min():.2f}, {det_locations[:, 2].max():.2f}]")
        print(f"  尺寸范围 - L: [{det_dimensions[:, 0].min():.2f}, {det_dimensions[:, 0].max():.2f}]")
        print(f"  尺寸范围 - W: [{det_dimensions[:, 1].min():.2f}, {det_dimensions[:, 1].max():.2f}]")
        print(f"  尺寸范围 - H: [{det_dimensions[:, 2].min():.2f}, {det_dimensions[:, 2].max():.2f}]")
        print(f"  旋转范围: [{det_rotations.min():.2f}, {det_rotations.max():.2f}]")
        
        print(f"\n📊 GT标注统计:")
        print(f"  位置范围 - X: [{gt_locations[:, 0].min():.2f}, {gt_locations[:, 0].max():.2f}]")
        print(f"  位置范围 - Y: [{gt_locations[:, 1].min():.2f}, {gt_locations[:, 1].max():.2f}]")
        print(f"  位置范围 - Z: [{gt_locations[:, 2].min():.2f}, {gt_locations[:, 2].max():.2f}]")
        print(f"  尺寸范围 - L: [{gt_dimensions[:, 0].min():.2f}, {gt_dimensions[:, 0].max():.2f}]")
        print(f"  尺寸范围 - W: [{gt_dimensions[:, 1].min():.2f}, {gt_dimensions[:, 1].max():.2f}]")
        print(f"  尺寸范围 - H: [{gt_dimensions[:, 2].min():.2f}, {gt_dimensions[:, 2].max():.2f}]")
        print(f"  旋转范围: [{gt_rotations.min():.2f}, {gt_rotations.max():.2f}]")
        
        # 检查坐标系是否匹配
        det_center = np.mean(det_locations, axis=0)
        gt_center = np.mean(gt_locations, axis=0)
        distance = np.linalg.norm(det_center - gt_center)
        
        print(f"\n🎯 坐标系匹配分析:")
        print(f"  检测结果中心: [{det_center[0]:.2f}, {det_center[1]:.2f}, {det_center[2]:.2f}]")
        print(f"  GT标注中心: [{gt_center[0]:.2f}, {gt_center[1]:.2f}, {gt_center[2]:.2f}]")
        print(f"  中心距离: {distance:.2f}")
        
        if distance > 50:
            print("  ⚠️ 警告：坐标系可能不匹配！")
        else:
            print("  ✅ 坐标系看起来匹配")


def analyze_sample_details(det_annos, gt_annos, sample_count=5):
    """分析具体样本的详情"""
    
    print(f"\n🔍 样本详细分析 (前{sample_count}个样本):")
    
    for i in range(min(sample_count, len(det_annos), len(gt_annos))):
        det = det_annos[i]
        gt = gt_annos[i]
        
        print(f"\n📋 样本 {i}:")
        
        # 检测结果
        if isinstance(det, dict):
            det_count = len(det.get('name', []))
            det_scores = det.get('score', [])
            det_names = det.get('name', [])
            print(f"  检测结果: {det_count} 个对象")
            if det_count > 0:
                print(f"    类别: {list(det_names)}")
                print(f"    置信度: {[f'{s:.3f}' for s in det_scores[:3]]}")  # 只显示前3个
        
        # GT标注
        if isinstance(gt, dict):
            gt_count = len(gt.get('name', []))
            gt_names = gt.get('name', [])
            print(f"  GT标注: {gt_count} 个对象")
            if gt_count > 0:
                print(f"    类别: {list(gt_names)}")
        
        # 检查是否有Car类别的匹配
        det_car_count = sum(1 for name in det.get('name', []) if name == 'Car')
        gt_car_count = sum(1 for name in gt.get('name', []) if name == 'Car')
        print(f"  Car类别: 检测{det_car_count}个, GT{gt_car_count}个")


def test_iou_calculation(det_annos, gt_annos):
    """测试IoU计算"""
    
    print(f"\n🔍 IoU计算测试:")
    
    # 找一个有检测结果和GT的样本
    for i, (det, gt) in enumerate(zip(det_annos, gt_annos)):
        if (isinstance(det, dict) and len(det.get('name', [])) > 0 and
            isinstance(gt, dict) and len(gt.get('name', [])) > 0):
            
            det_names = det.get('name', [])
            gt_names = gt.get('name', [])
            
            # 找Car类别
            det_car_indices = [j for j, name in enumerate(det_names) if name == 'Car']
            gt_car_indices = [j for j, name in enumerate(gt_names) if name == 'Car']
            
            if det_car_indices and gt_car_indices:
                print(f"  样本 {i}: 找到Car类别匹配")
                
                # 获取第一个Car的信息
                det_idx = det_car_indices[0]
                gt_idx = gt_car_indices[0]
                
                det_loc = det['location'][det_idx]
                det_dim = det['dimensions'][det_idx]
                det_rot = det['rotation_y'][det_idx]
                
                gt_loc = gt['location'][gt_idx]
                gt_dim = gt['dimensions'][gt_idx]
                gt_rot = gt['rotation_y'][gt_idx]
                
                print(f"    检测框: 位置{det_loc}, 尺寸{det_dim}, 旋转{det_rot:.3f}")
                print(f"    GT框:   位置{gt_loc}, 尺寸{gt_dim}, 旋转{gt_rot:.3f}")
                
                # 计算距离
                distance = np.linalg.norm(np.array(det_loc) - np.array(gt_loc))
                print(f"    中心距离: {distance:.3f}")
                
                if distance > 10:
                    print(f"    ⚠️ 警告：距离过大，可能坐标系不匹配")
                
                break
    else:
        print("  ❌ 没有找到同时包含检测结果和GT的Car样本")


def main():
    args = parse_args()
    
    print("🔍 mAP问题诊断")
    print(f"📂 结果文件: {args.result_file}")
    print(f"📂 腐败类型: {args.corruption}")
    
    # 检查结果文件
    result_file = Path(args.result_file)
    if not result_file.exists():
        print(f"❌ 结果文件不存在: {result_file}")
        return
    
    # 加载配置
    cfg_from_yaml_file(args.cfg_file, cfg)
    cfg.DATA_CONFIG.INFO_PATH['test'] = [f'kitti_c_infos_val_{args.corruption}.pkl']
    cfg.DATA_CONFIG.DEFAULT_CORRUPTION = args.corruption
    
    try:
        # 构建数据集
        print("📊 构建数据集...")
        test_set, _, _ = build_dataloader(
            dataset_cfg=cfg.DATA_CONFIG,
            class_names=cfg.CLASS_NAMES,
            batch_size=1,
            dist=False, workers=0, logger=None, training=False
        )
        
        # 加载检测结果
        print("📥 加载检测结果...")
        with open(result_file, 'rb') as f:
            det_annos = pickle.load(f)
        
        # 提取GT标注
        print("📋 提取GT标注...")
        gt_annos = []
        for info in test_set.kitti_infos:
            gt_annos.append(copy.deepcopy(info['annos']))
        
        print(f"✅ 数据加载完成")
        print(f"  检测结果: {len(det_annos)} 个")
        print(f"  GT标注: {len(gt_annos)} 个")
        
        # 开始诊断
        analyze_coordinate_ranges(det_annos, gt_annos)
        analyze_sample_details(det_annos, gt_annos, args.sample_count)
        test_iou_calculation(det_annos, gt_annos)
        
        # 统计总体情况
        total_det_objects = sum(len(det.get('name', [])) for det in det_annos if isinstance(det, dict))
        total_gt_objects = sum(len(gt.get('name', [])) for gt in gt_annos if isinstance(gt, dict))
        
        det_car_count = sum(sum(1 for name in det.get('name', []) if name == 'Car') 
                           for det in det_annos if isinstance(det, dict))
        gt_car_count = sum(sum(1 for name in gt.get('name', []) if name == 'Car') 
                          for gt in gt_annos if isinstance(gt, dict))
        
        print(f"\n📊 总体统计:")
        print(f"  总检测对象: {total_det_objects}")
        print(f"  总GT对象: {total_gt_objects}")
        print(f"  检测Car数量: {det_car_count}")
        print(f"  GT Car数量: {gt_car_count}")
        
        print(f"\n💡 诊断完成！请检查上述信息是否有异常。")
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
