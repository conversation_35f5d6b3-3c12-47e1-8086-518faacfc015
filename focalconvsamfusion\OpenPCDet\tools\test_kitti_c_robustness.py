#!/usr/bin/env python3
"""
KITTI-C鲁棒性测试脚本
测试RoboFusion模型在不同腐败条件下的性能
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def test_corruption(corruption_type, checkpoint_path, output_dir):
    """测试特定腐败类型"""
    
    print(f"\n=== 测试腐败类型: {corruption_type} ===")
    
    # 修改数据集配置中的腐败类型
    config_file = "cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml"
    
    # 构建测试命令
    cmd = [
        "python", "test.py",
        "--cfg_file", config_file,
        "--ckpt", checkpoint_path,
        "--extra_tag", f"kitti_c_{corruption_type}_robustness",
        "--save_to_file",
        "--infer_time"
    ]
    
    print(f"运行命令: {' '.join(cmd)}")
    
    try:
        # 运行测试
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
        
        if result.returncode == 0:
            print(f"✓ {corruption_type} 测试完成")
            return True
        else:
            print(f"✗ {corruption_type} 测试失败")
            print(f"错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ {corruption_type} 测试超时")
        return False
    except Exception as e:
        print(f"✗ {corruption_type} 测试出错: {e}")
        return False

def parse_results(output_dir, corruption_type):
    """解析测试结果"""
    
    result_dir = output_dir / f"kitti_c_{corruption_type}_robustness" / "eval"
    
    # 查找结果文件
    result_files = list(result_dir.glob("**/eval_results.txt"))
    
    if not result_files:
        print(f"未找到 {corruption_type} 的结果文件")
        return None
    
    result_file = result_files[0]
    
    try:
        with open(result_file, 'r') as f:
            content = f.read()
            
        # 提取关键指标
        lines = content.split('\n')
        results = {}
        
        for line in lines:
            if 'Car AP@0.70, 0.70, 0.70:' in line:
                # 提取AP值
                parts = line.split(':')[1].strip().split(',')
                results['AP_Easy'] = float(parts[0].strip())
                results['AP_Moderate'] = float(parts[1].strip())
                results['AP_Hard'] = float(parts[2].strip())
                break
        
        return results
        
    except Exception as e:
        print(f"解析 {corruption_type} 结果时出错: {e}")
        return None

def main():
    """主函数"""
    
    print("=== KITTI-C鲁棒性测试工具 ===")
    
    # 配置参数
    checkpoint_path = "../output/kitti_models/sam_onlinev3/robofusion_optimized/ckpt/checkpoint_epoch_80.pth"
    output_dir = Path("../output/kitti_models/sam_onlinev3")
    
    # 检查权重文件是否存在
    if not Path(checkpoint_path).exists():
        print(f"错误: 权重文件不存在: {checkpoint_path}")
        print("请确认您的模型权重路径是否正确")
        return
    
    # 腐败类型列表
    corruption_types = ['fog', 'rain', 'snow', 'sun']
    
    # 存储所有结果
    all_results = {}
    
    # 测试每种腐败类型
    for corruption_type in corruption_types:
        success = test_corruption(corruption_type, checkpoint_path, output_dir)
        
        if success:
            # 解析结果
            results = parse_results(output_dir, corruption_type)
            if results:
                all_results[corruption_type] = results
    
    # 生成汇总报告
    print("\n" + "="*60)
    print("KITTI-C鲁棒性测试结果汇总")
    print("="*60)
    
    if all_results:
        print(f"{'腐败类型':<10} {'Easy AP':<10} {'Moderate AP':<12} {'Hard AP':<10}")
        print("-" * 50)
        
        for corruption_type, results in all_results.items():
            print(f"{corruption_type:<10} {results.get('AP_Easy', 'N/A'):<10.3f} "
                  f"{results.get('AP_Moderate', 'N/A'):<12.3f} {results.get('AP_Hard', 'N/A'):<10.3f}")
        
        # 保存结果到JSON文件
        results_file = output_dir / "kitti_c_robustness_results.json"
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2)
        
        print(f"\n详细结果已保存到: {results_file}")
        
    else:
        print("没有成功的测试结果")
    
    print("\n=== 测试完成 ===")

def test_single_corruption():
    """测试单个腐败类型的简化版本"""
    
    corruption_type = "fog"  # 默认测试fog
    checkpoint_path = "../output/kitti_models/sam_onlinev3/robofusion_optimized/ckpt/checkpoint_epoch_80.pth"
    
    print(f"=== 测试单个腐败类型: {corruption_type} ===")
    
    # 构建测试命令
    cmd = [
        "python", "test.py",
        "--cfg_file", "cfgs/kitti_models/sam_onlinev3_kitti_c_test.yaml",
        "--ckpt", checkpoint_path,
        "--extra_tag", f"kitti_c_{corruption_type}_test",
        "--save_to_file",
        "--infer_time"
    ]
    
    print(f"运行命令: {' '.join(cmd)}")
    print("开始测试...")
    
    # 直接运行，不捕获输出，让用户看到实时进度
    try:
        subprocess.run(cmd, check=True)
        print(f"\n✓ {corruption_type} 测试完成!")
    except subprocess.CalledProcessError as e:
        print(f"\n✗ {corruption_type} 测试失败，返回码: {e.returncode}")
    except Exception as e:
        print(f"\n✗ 测试出错: {e}")

if __name__ == '__main__':
    # 使用简化版本进行单个测试
    test_single_corruption()
    
    # 如果需要测试所有腐败类型，取消注释下面这行
    # main()
