#!/usr/bin/env python3
"""
KITTI-C数据集结构检查脚本
检查数据集是否包含必要的ground truth标注文件
"""

import os
import sys
from pathlib import Path


def check_kitti_c_structure(data_path):
    """检查KITTI-C数据集结构"""
    
    data_path = Path(data_path)
    print(f"检查数据集路径: {data_path}")
    
    if not data_path.exists():
        print(f"❌ 数据集路径不存在: {data_path}")
        return False
    
    # 检查腐败类型文件夹
    corruption_types = ['fog', 'snow', 'rain', 'sun']
    missing_labels = []
    
    for corruption in corruption_types:
        corruption_path = data_path / corruption
        
        if not corruption_path.exists():
            print(f"❌ 缺少腐败类型文件夹: {corruption}")
            continue
            
        print(f"\n📁 检查 {corruption} 文件夹:")
        
        # 检查必要的子文件夹
        required_dirs = ['calib', 'images', 'velodyne']
        for req_dir in required_dirs:
            dir_path = corruption_path / req_dir
            if dir_path.exists():
                print(f"  ✓ {req_dir}/ 存在")
                
                # 检查val子文件夹
                if req_dir in ['images', 'velodyne']:
                    val_path = dir_path / 'val'
                    if val_path.exists():
                        file_count = len(list(val_path.glob('*')))
                        print(f"    ✓ {req_dir}/val/ 存在 ({file_count} 个文件)")
                    else:
                        print(f"    ❌ {req_dir}/val/ 不存在")
            else:
                print(f"  ❌ {req_dir}/ 不存在")
        
        # 检查关键的labels文件夹
        labels_path = corruption_path / 'labels'
        if labels_path.exists():
            labels_val_path = labels_path / 'val'
            if labels_val_path.exists():
                label_count = len(list(labels_val_path.glob('*.txt')))
                print(f"  ✓ labels/val/ 存在 ({label_count} 个标注文件)")
            else:
                print(f"  ❌ labels/val/ 不存在")
                missing_labels.append(f"{corruption}/labels/val/")
        else:
            print(f"  ❌ labels/ 不存在")
            missing_labels.append(f"{corruption}/labels/")
    
    # 检查info文件
    print(f"\n📄 检查info文件:")
    info_files = ['kitti_c_infos_train.pkl', 'kitti_c_infos_val.pkl', 'kitti_c_dbinfos_train.pkl']
    for info_file in info_files:
        info_path = data_path / info_file
        if info_path.exists():
            print(f"  ✓ {info_file} 存在")
        else:
            print(f"  ❌ {info_file} 不存在")
    
    # 总结
    print(f"\n📊 检查结果总结:")
    if missing_labels:
        print(f"❌ 缺少关键的标注文件夹:")
        for missing in missing_labels:
            print(f"   - {missing}")
        print(f"\n💡 这就是为什么无法计算mAP的原因！")
        print(f"   没有ground truth标注，就无法计算精度指标。")
        return False
    else:
        print(f"✓ 数据集结构完整，包含所有必要的标注文件")
        return True


def suggest_solutions():
    """提供解决方案建议"""
    
    print(f"\n🔧 解决方案:")
    print(f"1. 重新下载完整的KITTI-C数据集（包含labels文件夹）")
    print(f"2. 如果有原始KITTI验证集，可以复制其标注文件到KITTI-C")
    print(f"3. 联系数据集提供者获取完整版本")
    
    print(f"\n📝 标注文件格式应该是:")
    print(f"   每个.txt文件包含目标的3D边界框信息")
    print(f"   格式: type truncated occluded alpha bbox_2d dimensions_3d location_3d rotation_y")


def main():
    if len(sys.argv) != 2:
        print("用法: python check_kitti_c_dataset.py <KITTI-C数据集路径>")
        print("示例: python check_kitti_c_dataset.py /data/liufeifei/project/robofusion/mmdetection3d/data/KITTI-C/KITTI-C/KITTI-C")
        sys.exit(1)
    
    data_path = sys.argv[1]
    
    print("=" * 60)
    print("KITTI-C数据集结构检查工具")
    print("=" * 60)
    
    is_complete = check_kitti_c_structure(data_path)
    
    if not is_complete:
        suggest_solutions()
    
    print("=" * 60)


if __name__ == '__main__':
    main()
