---
description: Explore and learn about functions in Ultralytics MaskData library such as mask_to_rle_pytorch, area_from_rle, generate_crop_boxes, and more.
keywords: Ultralytics, SAM, MaskData, mask_to_rle_pytorch, area_from_rle, generate_crop_boxes, batched_mask_to_box, documentation
---

## MaskData
---
### ::: ultralytics.vit.sam.amg.MaskData
<br><br>

## is_box_near_crop_edge
---
### ::: ultralytics.vit.sam.amg.is_box_near_crop_edge
<br><br>

## box_xyxy_to_xywh
---
### ::: ultralytics.vit.sam.amg.box_xyxy_to_xywh
<br><br>

## batch_iterator
---
### ::: ultralytics.vit.sam.amg.batch_iterator
<br><br>

## mask_to_rle_pytorch
---
### ::: ultralytics.vit.sam.amg.mask_to_rle_pytorch
<br><br>

## rle_to_mask
---
### ::: ultralytics.vit.sam.amg.rle_to_mask
<br><br>

## area_from_rle
---
### ::: ultralytics.vit.sam.amg.area_from_rle
<br><br>

## calculate_stability_score
---
### ::: ultralytics.vit.sam.amg.calculate_stability_score
<br><br>

## build_point_grid
---
### ::: ultralytics.vit.sam.amg.build_point_grid
<br><br>

## build_all_layer_point_grids
---
### ::: ultralytics.vit.sam.amg.build_all_layer_point_grids
<br><br>

## generate_crop_boxes
---
### ::: ultralytics.vit.sam.amg.generate_crop_boxes
<br><br>

## uncrop_boxes_xyxy
---
### ::: ultralytics.vit.sam.amg.uncrop_boxes_xyxy
<br><br>

## uncrop_points
---
### ::: ultralytics.vit.sam.amg.uncrop_points
<br><br>

## uncrop_masks
---
### ::: ultralytics.vit.sam.amg.uncrop_masks
<br><br>

## remove_small_regions
---
### ::: ultralytics.vit.sam.amg.remove_small_regions
<br><br>

## coco_encode_rle
---
### ::: ultralytics.vit.sam.amg.coco_encode_rle
<br><br>

## batched_mask_to_box
---
### ::: ultralytics.vit.sam.amg.batched_mask_to_box
<br><br>