---
description: Enhance image data with Albumentations CenterCrop, normalize, augment_hsv, replicate, random_perspective, cutout, & box_candidates.
keywords: YOLO, object detection, data loaders, V5 augmentations, CenterCrop, normalize, random_perspective
---

## Albumentations
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.Albumentations
<br><br>

## LetterBox
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.LetterBox
<br><br>

## CenterCrop
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.CenterCrop
<br><br>

## ToTensor
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.ToTensor
<br><br>

## normalize
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.normalize
<br><br>

## denormalize
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.denormalize
<br><br>

## augment_hsv
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.augment_hsv
<br><br>

## hist_equalize
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.hist_equalize
<br><br>

## replicate
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.replicate
<br><br>

## letterbox
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.letterbox
<br><br>

## random_perspective
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.random_perspective
<br><br>

## copy_paste
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.copy_paste
<br><br>

## cutout
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.cutout
<br><br>

## mixup
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.mixup
<br><br>

## box_candidates
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.box_candidates
<br><br>

## classify_albumentations
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.classify_albumentations
<br><br>

## classify_transforms
---
### ::: ultralytics.yolo.data.dataloaders.v5augmentations.classify_transforms
<br><br>
