# Ultralytics YOLO 🚀, AGPL-3.0 license

site_name: Ultralytics YOLOv8 Docs
site_url: https://docs.ultralytics.com
site_description: Explore Ultralytics YOLOv8, a cutting-edge real-time object detection and image segmentation model for various applications and hardware platforms.
site_author: Ultralytics
repo_url: https://github.com/ultralytics/ultralytics
edit_uri: https://github.com/ultralytics/ultralytics/tree/main/docs
repo_name: ultralytics/ultralytics
remote_name: https://github.com/ultralytics/docs

theme:
  name: material
  custom_dir: docs/overrides
  logo: https://github.com/ultralytics/assets/raw/main/logo/Ultralytics_Logotype_Reverse.svg
  favicon: assets/favicon.ico
  icon:
    repo: fontawesome/brands/github
  font:
    text: Helvetica
    code: Roboto Mono

  palette:
    # Palette toggle for light mode
    - scheme: default
      # primary: grey
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode

    # Palette toggle for dark mode
    - scheme: slate
      # primary: black
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  features:
    - content.action.edit
    - content.code.annotate
    - content.code.copy
    - content.tooltips
    - search.highlight
    - search.share
    - search.suggest
    - toc.follow
    - toc.integrate
    - navigation.top
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.expand
    - navigation.footer
    - navigation.tracking
    - navigation.instant
    - navigation.indexes
    - content.tabs.link  # all code tabs change simultaneously

# Customization
copyright: <a href="https://ultralytics.com" target="_blank">Ultralytics 2023.</a> All rights reserved.
extra:
  # version:
  #   provider: mike  #  version drop-down menu
  robots: robots.txt
  analytics:
    provider: google
    property: G-2M5EHKC0BH
  #    feedback:
  #      title: Was this page helpful?
  #      ratings:
  #        - icon: material/heart
  #          name: This page was helpful
  #          data: 1
  #          note: Thanks for your feedback!
  #        - icon: material/heart-broken
  #          name: This page could be improved
  #          data: 0
  #          note: >-
  #            Thanks for your feedback!<br>
  #            <a href="https://github.com/ultralytics/ultralytics/issues/new?title=Docs+Feedback+for+{title}+page+at+https://docs.ultralytics.com/{url}&labels=enhancement&template=feature-request.yml" target="_blank" rel="noopener">Tell us what we can improve.</a>

  social:
    - icon: fontawesome/brands/github
      link: https://github.com/ultralytics
    - icon: fontawesome/brands/linkedin
      link: https://www.linkedin.com/company/ultralytics/
    - icon: fontawesome/brands/twitter
      link: https://twitter.com/ultralytics
    - icon: fontawesome/brands/youtube
      link: https://www.youtube.com/ultralytics
    - icon: fontawesome/brands/docker
      link: https://hub.docker.com/r/ultralytics/ultralytics/
    - icon: fontawesome/brands/python
      link: https://pypi.org/project/ultralytics/
    - icon: fontawesome/brands/discord
      link: https://discord.gg/bNc8wwx7Qy

extra_css:
  - stylesheets/style.css
  - https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css

markdown_extensions:
  # Div text decorators
  - admonition
  - md_in_html
  - pymdownx.details
  - pymdownx.superfences
  - tables
  - attr_list
  - def_list
  # Syntax highlight
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets:
      base_path: ./
  - pymdownx.emoji:
      emoji_index: !!python/name:materialx.emoji.twemoji  # noqa
      emoji_generator: !!python/name:materialx.emoji.to_svg
  - pymdownx.tabbed:
      alternate_style: true

  # Highlight
  - pymdownx.critic
  - pymdownx.caret
  - pymdownx.keys
  - pymdownx.mark
  - pymdownx.tilde

# Primary navigation ---------------------------------------------------------------------------------------------------
nav:
  - Home:
      - Home: index.md
      - Quickstart: quickstart.md
      - Modes:
          - modes/index.md
          - Train: modes/train.md
          - Val: modes/val.md
          - Predict: modes/predict.md
          - Export: modes/export.md
          - Track: modes/track.md
          - Benchmark: modes/benchmark.md
      - Tasks:
          - tasks/index.md
          - Detect: tasks/detect.md
          - Segment: tasks/segment.md
          - Classify: tasks/classify.md
          - Pose: tasks/pose.md
  - Quickstart: quickstart.md
  - Modes:
      - modes/index.md
      - Train: modes/train.md
      - Val: modes/val.md
      - Predict: modes/predict.md
      - Export: modes/export.md
      - Track: modes/track.md
      - Benchmark: modes/benchmark.md
  - Tasks:
      - tasks/index.md
      - Detect: tasks/detect.md
      - Segment: tasks/segment.md
      - Classify: tasks/classify.md
      - Pose: tasks/pose.md
  - Models:
      - models/index.md
      - YOLOv3: models/yolov3.md
      - YOLOv4: models/yolov4.md
      - YOLOv5: models/yolov5.md
      - YOLOv6: models/yolov6.md
      - YOLOv7: models/yolov7.md
      - YOLOv8: models/yolov8.md
      - SAM (Segment Anything Model): models/sam.md
      - FastSAM (Fast Segment Anything Model): models/fast-sam.md
      - YOLO-NAS (Neural Architecture Search): models/yolo-nas.md
      - RT-DETR (Realtime Detection Transformer): models/rtdetr.md
  - Datasets:
      - datasets/index.md
      - Detection:
          - datasets/detect/index.md
          - Argoverse: datasets/detect/argoverse.md
          - COCO: datasets/detect/coco.md
          - COCO8: datasets/detect/coco8.md
          - GlobalWheat2020: datasets/detect/globalwheat2020.md
          - Objects365: datasets/detect/objects365.md
          - SKU-110K: datasets/detect/sku-110k.md
          - VisDrone: datasets/detect/visdrone.md
          - VOC: datasets/detect/voc.md
          - xView: datasets/detect/xview.md
      - Segmentation:
          - datasets/segment/index.md
          - COCO: datasets/segment/coco.md
          - COCO8-seg: datasets/segment/coco8-seg.md
      - Pose:
          - datasets/pose/index.md
          - COCO: datasets/pose/coco.md
          - COCO8-pose: datasets/pose/coco8-pose.md
      - Classification:
          - datasets/classify/index.md
          - Caltech 101: datasets/classify/caltech101.md
          - Caltech 256: datasets/classify/caltech256.md
          - CIFAR-10: datasets/classify/cifar10.md
          - CIFAR-100: datasets/classify/cifar100.md
          - Fashion-MNIST: datasets/classify/fashion-mnist.md
          - ImageNet: datasets/classify/imagenet.md
          - ImageNet-10: datasets/classify/imagenet10.md
          - Imagenette: datasets/classify/imagenette.md
          - Imagewoof: datasets/classify/imagewoof.md
          - MNIST: datasets/classify/mnist.md
      - Multi-Object Tracking:
          - datasets/track/index.md
  - Usage:
      - CLI: usage/cli.md
      - Python: usage/python.md
      - Callbacks: usage/callbacks.md
      - Configuration: usage/cfg.md
      - Advanced Customization: usage/engine.md
  - YOLOv5:
      - yolov5/index.md
      - Quickstart: yolov5/quickstart_tutorial.md
      - Environments:
          - Amazon Web Services (AWS): yolov5/environments/aws_quickstart_tutorial.md
          - Google Cloud (GCP): yolov5/environments/google_cloud_quickstart_tutorial.md
          - Docker Image: yolov5/environments/docker_image_quickstart_tutorial.md
      - Tutorials:
          - Train Custom Data: yolov5/tutorials/train_custom_data.md
          - Tips for Best Training Results: yolov5/tutorials/tips_for_best_training_results.md
          - Multi-GPU Training: yolov5/tutorials/multi_gpu_training.md
          - PyTorch Hub: yolov5/tutorials/pytorch_hub_model_loading.md
          - TFLite, ONNX, CoreML, TensorRT Export: yolov5/tutorials/model_export.md
          - NVIDIA Jetson Nano Deployment: yolov5/tutorials/running_on_jetson_nano.md
          - Test-Time Augmentation (TTA): yolov5/tutorials/test_time_augmentation.md
          - Model Ensembling: yolov5/tutorials/model_ensembling.md
          - Pruning/Sparsity Tutorial: yolov5/tutorials/model_pruning_and_sparsity.md
          - Hyperparameter evolution: yolov5/tutorials/hyperparameter_evolution.md
          - Transfer learning with frozen layers: yolov5/tutorials/transfer_learning_with_frozen_layers.md
          - Architecture Summary: yolov5/tutorials/architecture_description.md
          - Roboflow Datasets: yolov5/tutorials/roboflow_datasets_integration.md
          - Neural Magic's DeepSparse: yolov5/tutorials/neural_magic_pruning_quantization.md
          - Comet Logging: yolov5/tutorials/comet_logging_integration.md
          - Clearml Logging: yolov5/tutorials/clearml_logging_integration.md
  - Ultralytics HUB:
      - hub/index.md
      - Quickstart: hub/quickstart.md
      - Datasets: hub/datasets.md
      - Projects: hub/projects.md
      - Models: hub/models.md
      - Integrations: hub/integrations.md
      - Ultralytics HUB App:
          - hub/app/index.md
          - 'iOS': hub/app/ios.md
          - 'Android': hub/app/android.md
      - Inference API: hub/inference_api.md
  - Reference:
      - hub:
          - __init__: reference/hub/__init__.md
          - auth: reference/hub/auth.md
          - session: reference/hub/session.md
          - utils: reference/hub/utils.md
      - nn:
          - autobackend: reference/nn/autobackend.md
          - autoshape: reference/nn/autoshape.md
          - modules:
              - block: reference/nn/modules/block.md
              - conv: reference/nn/modules/conv.md
              - head: reference/nn/modules/head.md
              - transformer: reference/nn/modules/transformer.md
              - utils: reference/nn/modules/utils.md
          - tasks: reference/nn/tasks.md
      - tracker:
          - track: reference/tracker/track.md
          - trackers:
              - basetrack: reference/tracker/trackers/basetrack.md
              - bot_sort: reference/tracker/trackers/bot_sort.md
              - byte_tracker: reference/tracker/trackers/byte_tracker.md
          - utils:
              - gmc: reference/tracker/utils/gmc.md
              - kalman_filter: reference/tracker/utils/kalman_filter.md
              - matching: reference/tracker/utils/matching.md
      - vit:
          - rtdetr:
              - model: reference/vit/rtdetr/model.md
              - predict: reference/vit/rtdetr/predict.md
              - train: reference/vit/rtdetr/train.md
              - val: reference/vit/rtdetr/val.md
          - sam:
              - amg: reference/vit/sam/amg.md
              - autosize: reference/vit/sam/autosize.md
              - build: reference/vit/sam/build.md
              - model: reference/vit/sam/model.md
              - modules:
                  - decoders: reference/vit/sam/modules/decoders.md
                  - encoders: reference/vit/sam/modules/encoders.md
                  - mask_generator: reference/vit/sam/modules/mask_generator.md
                  - prompt_predictor: reference/vit/sam/modules/prompt_predictor.md
                  - sam: reference/vit/sam/modules/sam.md
                  - transformer: reference/vit/sam/modules/transformer.md
              - predict: reference/vit/sam/predict.md
          - utils:
              - loss: reference/vit/utils/loss.md
              - ops: reference/vit/utils/ops.md
      - yolo:
          - cfg:
              - __init__: reference/yolo/cfg/__init__.md
          - data:
              - annotator: reference/yolo/data/annotator.md
              - augment: reference/yolo/data/augment.md
              - base: reference/yolo/data/base.md
              - build: reference/yolo/data/build.md
              - converter: reference/yolo/data/converter.md
              - dataloaders:
                  - stream_loaders: reference/yolo/data/dataloaders/stream_loaders.md
                  - v5augmentations: reference/yolo/data/dataloaders/v5augmentations.md
                  - v5loader: reference/yolo/data/dataloaders/v5loader.md
              - dataset: reference/yolo/data/dataset.md
              - dataset_wrappers: reference/yolo/data/dataset_wrappers.md
              - utils: reference/yolo/data/utils.md
          - engine:
              - exporter: reference/yolo/engine/exporter.md
              - model: reference/yolo/engine/model.md
              - predictor: reference/yolo/engine/predictor.md
              - results: reference/yolo/engine/results.md
              - trainer: reference/yolo/engine/trainer.md
              - validator: reference/yolo/engine/validator.md
          - nas:
              - model: reference/yolo/nas/model.md
              - predict: reference/yolo/nas/predict.md
              - val: reference/yolo/nas/val.md
          - utils:
              - __init__: reference/yolo/utils/__init__.md
              - autobatch: reference/yolo/utils/autobatch.md
              - benchmarks: reference/yolo/utils/benchmarks.md
              - callbacks:
                  - base: reference/yolo/utils/callbacks/base.md
                  - clearml: reference/yolo/utils/callbacks/clearml.md
                  - comet: reference/yolo/utils/callbacks/comet.md
                  - dvc: reference/yolo/utils/callbacks/dvc.md
                  - hub: reference/yolo/utils/callbacks/hub.md
                  - mlflow: reference/yolo/utils/callbacks/mlflow.md
                  - neptune: reference/yolo/utils/callbacks/neptune.md
                  - raytune: reference/yolo/utils/callbacks/raytune.md
                  - tensorboard: reference/yolo/utils/callbacks/tensorboard.md
                  - wb: reference/yolo/utils/callbacks/wb.md
              - checks: reference/yolo/utils/checks.md
              - dist: reference/yolo/utils/dist.md
              - downloads: reference/yolo/utils/downloads.md
              - errors: reference/yolo/utils/errors.md
              - files: reference/yolo/utils/files.md
              - instance: reference/yolo/utils/instance.md
              - loss: reference/yolo/utils/loss.md
              - metrics: reference/yolo/utils/metrics.md
              - ops: reference/yolo/utils/ops.md
              - patches: reference/yolo/utils/patches.md
              - plotting: reference/yolo/utils/plotting.md
              - tal: reference/yolo/utils/tal.md
              - torch_utils: reference/yolo/utils/torch_utils.md
          - v8:
              - classify:
                  - predict: reference/yolo/v8/classify/predict.md
                  - train: reference/yolo/v8/classify/train.md
                  - val: reference/yolo/v8/classify/val.md
              - detect:
                  - predict: reference/yolo/v8/detect/predict.md
                  - train: reference/yolo/v8/detect/train.md
                  - val: reference/yolo/v8/detect/val.md
              - pose:
                  - predict: reference/yolo/v8/pose/predict.md
                  - train: reference/yolo/v8/pose/train.md
                  - val: reference/yolo/v8/pose/val.md
              - segment:
                  - predict: reference/yolo/v8/segment/predict.md
                  - train: reference/yolo/v8/segment/train.md
                  - val: reference/yolo/v8/segment/val.md

  - Help:
      - Help: help/index.md
      - Frequently Asked Questions (FAQ): help/FAQ.md
      - Contributing Guide: help/contributing.md
      - Continuous Integration (CI) Guide: help/CI.md
      - Contributor License Agreement (CLA): help/CLA.md
      - Minimum Reproducible Example (MRE) Guide: help/minimum_reproducible_example.md
      - Code of Conduct: help/code_of_conduct.md
      - Security Policy: SECURITY.md

# Plugins including 301 redirects navigation ---------------------------------------------------------------------------
plugins:
  - mkdocstrings
  - search
  - ultralytics:
      add_desc: False
      add_image: True
      add_share_buttons: True
      default_image: https://github.com/ultralytics/ultralytics/assets/26833433/6d09221c-c52a-4234-9a5d-b862e93c6529
  - redirects:
      redirect_maps:
        callbacks.md: usage/callbacks.md
        cfg.md: usage/cfg.md
        cli.md: usage/cli.md
        config.md: usage/cfg.md
        engine.md: usage/engine.md
        environments/AWS-Quickstart.md: yolov5/environments/aws_quickstart_tutorial.md
        environments/Docker-Quickstart.md: yolov5/environments/docker_image_quickstart_tutorial.md
        environments/GCP-Quickstart.md: yolov5/environments/google_cloud_quickstart_tutorial.md
        FAQ/augmentation.md: yolov5/tutorials/tips_for_best_training_results.md
        package-framework.md: index.md
        package-framework/mock_detector.md: index.md
        predict.md: modes/predict.md
        python.md: usage/python.md
        quick-start.md: quickstart.md
        app.md: hub/app/index.md
        sdk.md: index.md
        reference/base_pred.md: reference/yolo/engine/predictor.md
        reference/base_trainer.md: reference/yolo/engine/trainer.md
        reference/exporter.md: reference/yolo/engine/exporter.md
        reference/model.md: reference/yolo/engine/model.md
        reference/nn.md: reference/nn/modules/head.md
        reference/ops.md: reference/yolo/utils/ops.md
        reference/results.md: reference/yolo/engine/results.md
        reference/base_val.md: index.md
        tasks/classification.md: tasks/classify.md
        tasks/detection.md: tasks/detect.md
        tasks/segmentation.md: tasks/segment.md
        tasks/keypoints.md: tasks/pose.md
        tasks/tracking.md: modes/track.md
        tutorials/architecture-summary.md: yolov5/tutorials/architecture_description.md
        tutorials/clearml-logging.md: yolov5/tutorials/clearml_logging_integration.md
        tutorials/comet-logging.md: yolov5/tutorials/comet_logging_integration.md
        tutorials/hyperparameter-evolution.md: yolov5/tutorials/hyperparameter_evolution.md
        tutorials/model-ensembling.md: yolov5/tutorials/model_ensembling.md
        tutorials/multi-gpu-training.md: yolov5/tutorials/multi_gpu_training.md
        tutorials/nvidia-jetson.md: yolov5/tutorials/running_on_jetson_nano.md
        tutorials/pruning-sparsity.md: yolov5/tutorials/model_pruning_and_sparsity.md
        tutorials/pytorch-hub.md: yolov5/tutorials/pytorch_hub_model_loading.md
        tutorials/roboflow.md: yolov5/tutorials/roboflow_datasets_integration.md
        tutorials/test-time-augmentation.md: yolov5/tutorials/test_time_augmentation.md
        tutorials/torchscript-onnx-coreml-export.md: yolov5/tutorials/model_export.md
        tutorials/train-custom-datasets.md: yolov5/tutorials/train_custom_data.md
        tutorials/training-tips-best-results.md: yolov5/tutorials/tips_for_best_training_results.md
        tutorials/transfer-learning-froze-layers.md: yolov5/tutorials/transfer_learning_with_frozen_layers.md
        tutorials/weights-and-biasis-logging.md: yolov5/tutorials/comet_logging_integration.md
        yolov5/pytorch_hub.md: yolov5/tutorials/pytorch_hub_model_loading.md
        yolov5/hyp_evolution.md: yolov5/tutorials/hyperparameter_evolution.md
        yolov5/pruning_sparsity.md: yolov5/tutorials/model_pruning_and_sparsity.md
        yolov5/roboflow.md: yolov5/tutorials/roboflow_datasets_integration.md
        yolov5/comet.md: yolov5/tutorials/comet_logging_integration.md
        yolov5/clearml.md: yolov5/tutorials/clearml_logging_integration.md
        yolov5/tta.md: yolov5/tutorials/test_time_augmentation.md
        yolov5/multi_gpu_training.md: yolov5/tutorials/multi_gpu_training.md
        yolov5/ensemble.md: yolov5/tutorials/model_ensembling.md
        yolov5/jetson_nano.md: yolov5/tutorials/running_on_jetson_nano.md
        yolov5/transfer_learn_frozen.md: yolov5/tutorials/transfer_learning_with_frozen_layers.md
        yolov5/neural_magic.md: yolov5/tutorials/neural_magic_pruning_quantization.md
        yolov5/train_custom_data.md: yolov5/tutorials/train_custom_data.md
        yolov5/architecture.md: yolov5/tutorials/architecture_description.md
        yolov5/export.md: yolov5/tutorials/model_export.md
        yolov5/yolov5_quickstart_tutorial.md: yolov5/quickstart_tutorial.md
        yolov5/tips_for_best_training_results.md: yolov5/tutorials/tips_for_best_training_results.md
        yolov5/tutorials/yolov5_neural_magic_tutorial.md: yolov5/tutorials/neural_magic_pruning_quantization.md
        yolov5/tutorials/model_ensembling_tutorial.md: yolov5/tutorials/model_ensembling.md
        yolov5/tutorials/pytorch_hub_tutorial.md: yolov5/tutorials/pytorch_hub_model_loading.md
        yolov5/tutorials/yolov5_architecture_tutorial.md: yolov5/tutorials/architecture_description.md
        yolov5/tutorials/multi_gpu_training_tutorial.md: yolov5/tutorials/multi_gpu_training.md
        yolov5/tutorials/yolov5_pytorch_hub_tutorial.md: yolov5/tutorials/pytorch_hub_model_loading.md
        yolov5/tutorials/model_export_tutorial.md: yolov5/tutorials/model_export.md
        yolov5/tutorials/jetson_nano_tutorial.md: yolov5/tutorials/running_on_jetson_nano.md
        yolov5/tutorials/yolov5_model_ensembling_tutorial.md: yolov5/tutorials/model_ensembling.md
        yolov5/tutorials/roboflow_integration.md: yolov5/tutorials/roboflow_datasets_integration.md
        yolov5/tutorials/pruning_and_sparsity_tutorial.md: yolov5/tutorials/model_pruning_and_sparsity.md
        yolov5/tutorials/yolov5_transfer_learning_with_frozen_layers_tutorial.md: yolov5/tutorials/transfer_learning_with_frozen_layers.md
        yolov5/tutorials/transfer_learning_with_frozen_layers_tutorial.md: yolov5/tutorials/transfer_learning_with_frozen_layers.md
        yolov5/tutorials/yolov5_model_export_tutorial.md: yolov5/tutorials/model_export.md
        yolov5/tutorials/neural_magic_tutorial.md: yolov5/tutorials/neural_magic_pruning_quantization.md
        yolov5/tutorials/yolov5_clearml_integration_tutorial.md: yolov5/tutorials/clearml_logging_integration.md
        yolov5/tutorials/yolov5_train_custom_data.md: yolov5/tutorials/train_custom_data.md
        yolov5/tutorials/comet_integration_tutorial.md: yolov5/tutorials/comet_logging_integration.md
        yolov5/tutorials/yolov5_pruning_and_sparsity_tutorial.md: yolov5/tutorials/model_pruning_and_sparsity.md
        yolov5/tutorials/yolov5_jetson_nano_tutorial.md: yolov5/tutorials/running_on_jetson_nano.md
        yolov5/tutorials/yolov5_roboflow_integration.md: yolov5/tutorials/roboflow_datasets_integration.md
        yolov5/tutorials/hyperparameter_evolution_tutorial.md: yolov5/tutorials/hyperparameter_evolution.md
        yolov5/tutorials/yolov5_hyperparameter_evolution_tutorial.md: yolov5/tutorials/hyperparameter_evolution.md
        yolov5/tutorials/clearml_integration_tutorial.md: yolov5/tutorials/clearml_logging_integration.md
        yolov5/tutorials/test_time_augmentation_tutorial.md: yolov5/tutorials/test_time_augmentation.md
        yolov5/tutorials/yolov5_test_time_augmentation_tutorial.md: yolov5/tutorials/test_time_augmentation.md
        yolov5/environments/yolov5_amazon_web_services_quickstart_tutorial.md: yolov5/environments/aws_quickstart_tutorial.md
        yolov5/environments/yolov5_google_cloud_platform_quickstart_tutorial.md: yolov5/environments/google_cloud_quickstart_tutorial.md
        yolov5/environments/yolov5_docker_image_quickstart_tutorial.md: yolov5/environments/docker_image_quickstart_tutorial.md
